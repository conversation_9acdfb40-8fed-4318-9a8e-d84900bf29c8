<template>
  <div class="isTxt" v-if="isTxt">
    {{innerValue || '-'}}
  </div>
  <RxkSelect
    v-else
    :list="list"
    allow-create
    filterable
    default-first-option
    v-model="innerValue"
  />
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkSelect } from '@/components/common/RxkSelect'
import { isNullOrUndefOrEmpty } from '@/utils/is'
import { getDetaultValue } from '@/apis/objectManage'
import type { List } from '@/types/common'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue', 'refreshDataList'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})
const list = ref<List[]>([])

onMounted(() => {
  if (isNullOrUndefOrEmpty(innerValue.value)) {
    console.log('会执行吗2321', props.data, innerValue)
    // 关联已有数据，要去调接口查出list
    const defaultValueConfigInfo = props.data.defaultValueConfigInfo
    const postData = {
      connectColumnSerial: defaultValueConfigInfo.connectColumnSerial,
      connectTableCode: defaultValueConfigInfo.connectTableCode,
      connectType: defaultValueConfigInfo.connectType,
      conditionInfo: defaultValueConfigInfo.conditionInfo
    }
    getDetaultValue(postData).then((res) => {
      if(res && res.length) {
        if (props.data.defaultValueConfigInfo.connectType === 10) {
          list.value = [{ label: res[0][defaultValueConfigInfo.connectColumnSerial], value: res[0][defaultValueConfigInfo.connectColumnSerial] }]
        } else {
          list.value = res.map((item: Recordable) => ({ label: item[defaultValueConfigInfo.connectColumnSerial], value: item[defaultValueConfigInfo.connectColumnSerial] }))
        }
        innerValue.value = list.value[0]?.value || ''
      }
    })
  }
})

</script>