import { ref } from 'vue'
import type { ColumnType } from '@/types/table'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { productTypeEnum, billingTypeEnum, statusEnum,pushTypeEnum } from '@/enums/carlife'
import timeUtils from '@/utils/libs/time'

const channelCommonFiled: ColumnType[] = [
  {
    key: 'productType',
    title: '三方类型',
    render({ cellData }) {
      return (
        <>
          <span>
            {productTypeEnum.find((item) => item.value === cellData.productType)
              ?.label || '-'}
          </span>
        </>
      )
    }
  },
  {
    key: 'billingType',
    title: '结算类型',
    desc: '',
    width: 120,
    render({ cellData }) {
      return (
        <>
          <span>
            {billingTypeEnum.find((item) => item.value === cellData.billingType)
              ?.label || '-'}
          </span>
        </>
      )
    }
  },
  { key: 'income', title: '收益额', desc: '', width: 120,cellRender: { name: 'millage' } },

  {
    key: 'status',
    title: '当前状态',
    desc: '',
    width: 120,
    render({ cellData }) {
      return (
        <>
          <span>
            {statusEnum.find((item) => item.value === cellData.status)
              ?.label || '-'}
          </span>
        </>
      )
    }
  },
  {
    key: 'guideVisitPv,guideVisitUv',
    title: 'guide页访问pv/uv',
    desc: '',
    width: 100,
    render({ cellData }) {
      return (
        <>
          <span>{`${cellData.guideVisitPv} | ${cellData.guideVisitUv}`}</span>
        </>
      )
    }
  },
  {
    key: 'guideClickPv,guideClickUv',
    title: 'guide页点击pv/uv',
    desc: '',
    width: 100,
    render({ cellData }) {
      return (
        <>
          <span>{`${cellData.guideClickPv} | ${cellData.guideClickUv}`}</span>
        </>
      )
    }
  },
  { key: 'ctr', title: '曝光点击率（%）', desc: '', width: 120 },
  { key: 'productRequestQuantity', title: '三方请求量', desc: '', width: 150 },
  {
    key: 'productRequestSuccessQuantity',
    title: '请求三方成功量',
    desc: '',
    width: 150
  },
  { key: 'successRequestRate', title: '三方请求成功率', desc: '', width: 150 }
]

/** 按渠道统计 */
export const channelStatistics = {
  searchFormData: ref<FormSchema[]>([
    {
      fieldName: '三方名称',
      component: 'Input',
      key: 'productName',
      val: ''
    },
    {
      fieldName: '用户名称',
      component: 'Input',
      key: 'userName',
      val: ''
    },

    {
      key: 'userPhone',
      fieldName: '用户手机号',
      component: 'Input',
      val: ''
    }
  ]),
  columns: ref<ColumnType[]>([
    { key: 'userId', title: '用户ID', desc: '', width: 120 },
    { key: 'userName', title: '用户姓名', desc: '', width: 120 },
    { key: 'userPhone', title: '手机号', desc: '', width: 120 },
    { key: 'carNo', title: '车牌号', desc: '', width: 120 },
    { key: 'channelName', title: '渠道名称', desc: '', width: 120 },
    { key: 'productName', title: '三方名称', desc: '', width: 120 },
    {
      key: 'status',
      title: '三方状态',
      desc: '',
      width: 120,
      render({ cellData }) {
        return (
          <>
            <span>
              {statusEnum.find((item) => item.value === cellData.status)
                ?.label || '-'}
            </span>
          </>
        )
      }
    },
    { key: 'applyStatus', title: '推送状态', desc: '', width: 120, render({ cellData }) {
      return (
        <>
          <span>
            {pushTypeEnum.find((item) => item.value === cellData.applyStatus)
              ?.label || '-'}
          </span>
        </>
      )
    } },
    { key: 'income', title: '收益额', desc: '', width: 120,cellRender: { name: 'millage' } },
    { key: 'updateTime', title: '状态更新时间', desc: '', width: 120 },
    { key: 'firstRequestTime', title: '首次请求时间', desc: '', width: 120 },
    { key: 'pushTime', title: '用户提交时间', desc: '', width: 120 }
  ])
}
/** 按单日明细统计 */
export const channelDayStatistics = {
  searchFormData: ref<FormSchema[]>([
    {
      key: 'timeRange',
      fieldName: '日期范围',
      component: 'DateRange',
      val: timeUtils.transTypeToTime(60, 'yyyy-MM-dd')
    }
  ]),
  columns: ref<ColumnType[]>([
    { key: 'day', title: '日期', fixed: 'left', width: 150 },
    { key: 'channelName', title: '渠道名称', width: 150 },
    ...channelCommonFiled
  ])
}
/** 按日期统计 */
export const channelDateStatistics = {
  searchFormData: ref<FormSchema[]>([
    {
      fieldName: '三方名称',
      key: 'productName',
      val: '',
      componentProps: {
        clearable: true
      },
      component: 'Input'
    },
    {
      key: 'timeRange',
      fieldName: '日期范围',
      component: 'DateRange',
      val: timeUtils.transTypeToTime(20, 'yyyy-MM-dd')
    }
  ]),
  columns: ref<ColumnType[]>([
    { key: 'day', title: '日期', fixed: 'left', width: 150 },
    { key: 'productName', title: '三方名称', fixed: 'left', width: 150 },
    ...channelCommonFiled
  ])
}
