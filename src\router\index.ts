import { createRouter, createWebHistory } from 'vue-router'
import { rootRoute } from '@/router/routes'
import { getToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import type { App } from 'vue'
// 创建路由实例并传递 `routes` 配置
const router = createRouter({
  history: createWebHistory(),
  routes: rootRoute as unknown as RouteRecordRaw[]
})

router.beforeEach((to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
  if (to.meta.isLogin && !getToken()) {
    ElMessage.warning('登录已失效，请重新登录')
    return next('/login')
  }
  next()
})

export function setupRouter (app: App) {
  app.use(router)
}

export { router }
