<template>
  <div class="channel-statistics-page">
    <SearchFilter @register="registerSetting" @search="search" />

    <div class="table-box">
      <RxkVTable @register="registerTable" />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { unref, onMounted, ref } from 'vue'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { channelDateStatistics } from '../data'
import { fetchGetuserProductData } from '@/apis/carlife'
import timeUtils from '@/utils/libs/time'
onMounted(() => {
  const [transStartTime, transEndTime] = timeUtils.transTypeToTime(
    20,
    'yyyy-MM-dd'
  )
  searchInfo.model.dayStart = transStartTime
  searchInfo.model.dayEnd = transEndTime

  setSearchInfo(searchInfo)
  reload()
})

// 搜索表单数据
const [registerSetting] = useSearch({
  schemas: unref(channelDateStatistics.searchFormData)
})

const searchInfo: Record<string, any> = {
  model: {
    dayStart: '',
    dayEnd: ''
  }
}

const [registerTable, { reload, setSearchInfo }] = useTable({
  api: fetchGetuserProductData,
  columns: unref(channelDateStatistics.columns),
  searchInfo: searchInfo,
  immediate: false // 是否立刻请求
})

const search = (val: { [k: string]: any }) => {
  Object.assign(searchInfo.model, val)

  searchInfo.model.dayStart = val.timeRange?.[0] ?? ''
  searchInfo.model.dayEnd = val.timeRange?.[1] ?? ''
  delete searchInfo.model.timeRange

  setSearchInfo(searchInfo)
  reload()
}
</script>

<style lang="scss" scoped>
.channel-statistics-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-form--inline .el-form-item {
    margin-right: 40px;
  }
  .btn-box {
    padding: 0 16px 4px;
    display: flex;
    justify-content: flex-end;
  }
  .table-box {
    width: 100%;
    flex: 1;
    overflow-y: hidden;
  }
}

.sort-btn {
  &:hover {
    color: $primary-color;
  }
  cursor: pointer;
  margin-left: 8px;
}
</style>
