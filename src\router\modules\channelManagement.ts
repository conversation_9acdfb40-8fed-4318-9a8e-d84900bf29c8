import type { AppRouteRecordRaw } from '@/router/types'
export const channelManagement: AppRouteRecordRaw[] = [
  {
    path: 'channelManagement',
    name: 'ChannelManagement',
    meta: {
      title: '渠道管理',
      icon: 'judaoguan<PERSON>'
    },
    children: [
      {
        path: 'channelManagementList',
        name: 'ChannelManagementList',
        component: () => import('@/views/channelManagement/list/index.vue'),
        meta: {
          title: '渠道列表'
        }
      }
    ]
  }
]
