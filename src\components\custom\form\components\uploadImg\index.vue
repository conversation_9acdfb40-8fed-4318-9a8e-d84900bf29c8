<template>
  <RxkUploadFile class="customUpload"
                 :limit="limit"
                 listType="picture"
                 @handleSuccess="handleSuccess"
                 @handleRemove="handleRemove">
    <template #upload>
      <div class="uploadImg">选择图片</div>
    </template>
  </RxkUploadFile>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkUploadFile } from '@/components/common/RxkUploadFile'
import { operateListEnum } from '@/enums/preObjectManage'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

// const innerValue = computed({
//   get () {
//     return props.modelValue
//   },
//   set (newVal) {
//     console.log(newVal, 'newVal1')
//     emit('update:modelValue', newVal)
//   }
// })
const list = ref<string[]>([])
onMounted(() => {
  list.value = props.modelValue
})

watch(() => list.value, () => {
  emit('update:modelValue', list.value)
}, {
  deep: true
})
const limit = computed(() => {
  console.log(props.data, 'pppp1')
  const columnOperateList = props.data.columnOperateList || []
  const current = columnOperateList.find((item: Recordable) => item.code === operateListEnum.PICTURE_ONE)
  return current && current.value ? 2 : 10
})
function handleSuccess (fileList, file, res) {
  console.log(fileList, file, res)
  res.data.forEach((item: Recordable) => {
    list.value.push(item.path)
  })
}
function handleRemove (fileList: any, file: any) {
  console.log(fileList, 'fileList')
  console.log(file, 'file')
  const path = file.response.data[0].path
  const index = list.value.findIndex(item => item === path)
  list.value.splice(index, 1)
}
</script>
<style lang="scss" scoped>
.customUpload {
  width: 100%;
  :deep(.el-upload) {
    width: 100%;
  }
}
.uploadImg {
  height: 32px;
  line-height: 32px;
  border: 1px dashed #DCDFE6;
  border-radius: 4px;
  width: 100%;
  text-align: center;
  color: #5687FF;
}
</style>