import type { TreeKey } from 'element-plus/es/components/tree/src/tree.type'
import { isFunc, isBoolean, isObj, isArray, isNull, isString } from '@/utils/is'
import { intersectionWith, isEqual, mergeWith, unionWith, cloneDeep as lodashCloneDeep } from 'lodash-es'
// import { IdWorker } from './randomInt'
// const idWorker = new IdWorker(1n, 1n)
/**
 * @author：张胜
 * @desc：通用递归
 * */
export const recursion = (list: any[], fn: Fn, config = { children: 'children' }) => {
  const { children } = config
  const recursionFn = (arr = [], parent = null) => {
    let interruptFlag = false // 中断递归标志
    for (const item of arr) {
      interruptFlag = isFunc(fn) ? fn(item, parent) : false
      if (isBoolean(interruptFlag) && interruptFlag) { // 当函数返回布尔值且为true时,中断递归
        return true
      } else {
        interruptFlag = recursionFn(item[children] || [], item) || false
        if (isBoolean(interruptFlag) && interruptFlag) { // 当函数返回布尔值且为true时,中断递归
          return true
        }
      }
    }
  }
  recursionFn(list)
}

/**
 * @author：张胜
 * @desc：递归合并两个对象。
 * @param source The source object to merge from. 要合并的源对象。
 * @param target The target object to merge into. 目标对象，合并后结果存放于此。
 * @param mergeArrays How to merge arrays. Default is "replace".
 *        如何合并数组。默认为replace。
 *        - "union": Union the arrays. 对数组执行并集操作。[1,2] [1,2,3] => [1,2,3]
 *        - "intersection": Intersect the arrays. 对数组执行交集操作。[1] [2] => []   [1,2] [1] => [1]
 *        - "concat": Concatenate the arrays. 连接数组。[1] [1,2,3] => [1,1,2,3]
 *        - "replace": Replace the source array with the target array. 用目标数组替换源数组。[1] [2] => [2]
 * @returns The merged object. 合并后的对象。
 */
export function deepMerge<T extends object | null | undefined, U extends object | null | undefined> (
  source: T,
  target: U,
  mergeArrays: 'union' | 'intersection' | 'concat' | 'replace' = 'replace'
): T & U {
  if (!target) {
    return source as T & U
  }
  if (!source) {
    return target as T & U
  }
  return mergeWith({}, source, target, (sourceValue, targetValue) => {
    if (isArray(targetValue) && isArray(sourceValue)) {
      switch (mergeArrays) {
        case 'union':
          return unionWith(sourceValue, targetValue, isEqual)
        case 'intersection':
          return intersectionWith(sourceValue, targetValue, isEqual)
        case 'concat':
          return sourceValue.concat(targetValue)
        case 'replace':
          return targetValue
        default:
          throw new Error(`Unknown merge array strategy: ${mergeArrays as string}`)
      }
    }
    if (isObj(targetValue) && isObj(sourceValue)) {
      return deepMerge(sourceValue, targetValue, mergeArrays)
    }
    return undefined
  })
}

/**
 * @author：张胜
 * @desc；深拷贝
 * */
export const cloneDeep = (obj: any) => {
  return lodashCloneDeep(obj)
}

/**
 * @author：张胜
 * @desc：生成一个用不重复的ID
 */
export function GenNonDuplicateID (randomLength: number) {
  return (Number(Math.random().toString().substr(3, randomLength) + Date.now()).toString(36)).toUpperCase()
}

// uuid
export function getUuid (len = 32, radix = 16) {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
  const uuid = []
  let i
  radix = radix || chars.length
  if (len) {
    for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]
  } else {
    let r
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
    uuid[14] = '4'
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16
        uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r]
      }
    }
  }
  return uuid.join('')
}

export function _randomInt (n: number): number {
  // if (n <= 0) return -1 // 只能输入正整数哦
  // const limit = Math.pow(10, n)
  // const value = Math.floor(Math.random() * limit) // 用随机数向下取整，避免多一位
  // if (value < (limit / 10) && value !== 0) { // 取一位的时候就用不到这个函数啦，不过还是要把 0 这个小朋友放行的
  //   return _randomInt(n)
  // }
  // return value
  // return Number(idWorker.getUniqueId().toString())
  const numbers = '0123456789'
  let result = ''

  while (result.length < 6) {
    const randomIndex = Math.floor(Math.random() * numbers.length)
    const randomChar = numbers.charAt(randomIndex)
    result += randomChar.toString()
  }
  return Number(`${new Date().getTime()}${result}`)
}

/**
 * @author：张胜
 * @desc：阿拉伯数字转大写
 * */
// export const numToCapital = (num: number | string, type: boolean) => {
//   if (!num) return 0
//   const strNum = Number((num + '').replace(/[,，]*/g, '')) + '' // 记录字符
//   num = parseInt(String(Number(strNum))) // 转为整数，
//   let capitalAr = '零一二三四五六七八九十'
//   let unitAr = ['十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千']
//   if (type) {
//     capitalAr = '零壹贰叁肆伍陆柒捌玖拾'
//     unitAr = ['拾', '佰', '仟', '万', '拾', '佰', '仟', '亿', '拾', '佰', '仟'] // 单位
//   }
//   const resultAr: any = [] // 记录结果，后边json.in就可
//   const index = strNum.length - 1 // 记录位数
//   let idx = 0 // 记录单位
//   const percent = 10
//   const turnNum = (num: number, percent: number, index: number) => {
//     const unit = num / percent
//     const capital = capitalAr[Number(strNum[index])]
//     if (unit < 1) {
//       resultAr.push(capital)
//       // 出现11【一十一】这种情况
//       if (Number(strNum[index]) === 1 && (strNum.length === 2 || strNum.length === 6 || strNum.length === 10)) {
//         resultAr.pop()
//       }
//       return false // 结束递归
//     } else {
//       if (capital === '零') {
//         // 万和亿单位不删除
//         if (!['万', '亿'].includes(resultAr[resultAr.length - 1])) {
//           resultAr.pop()
//         }
//         // 前面有零在删掉一个零
//         if (resultAr[resultAr.length - 1] === '零') {
//           resultAr.pop()
//         }
//       }
//       resultAr.push(capital)
//       // 过滤存在【零万】【零亿】这种情况
//       if (['万', '亿'].includes(resultAr[resultAr.length - 2]) && capital === '零') {
//         resultAr.pop()
//       }
//       // 过滤【1亿万】这种情况
//       if (resultAr[0] === '万' && resultAr[1] === '亿') {
//         resultAr.shift()
//       }
//       // 末尾【零】删掉
//       if (resultAr[0] === '零') {
//         resultAr.pop()
//       }
//       resultAr.push(unitAr[idx++])
//       turnNum(num, percent * 10, --index)
//     }
//   }
//   turnNum(num, percent, index)
//   return resultAr.reverse().join('')
// }
// export const numToCapital = (n: string) => {
//   if (!/^(0|[1-9]\d*)(\.\d+)?$/.test(n)) {
//     return '数据非法'
//   }
//   let unit = '千百拾亿千百拾万千百拾元角分'
//   let str = ''
//   n = n.replace(',', '')
//   n += '00'
//   const p = n.indexOf('.')
//   if (p >= 0) {
//     n = n.substring(0, p) + n.substr(p + 1, 2)
//   }
//   unit = unit.substr(unit.length - n.length)
//   for (let i = 0; i < n.length; i++) {
//     str += '零壹贰叁肆伍陆柒捌玖'.charAt(Number(n.charAt(i))) + unit.charAt(i)
//   }
//   return str
//   .replace(/零(千|百|拾|角)/g, '零')
//   .replace(/(零)+/g, '零')
//   .replace(/零(万|亿|元)/g, '$1')
//   .replace(/(亿)万|壹(拾)/g, '$1$2')
//   .replace(/^元零?|零分/g, '')
//   .replace(/元$/g, '元整')
// }
export const numToCapital = (n: number) => {
  /** 数字金额大写转换(可以处理整数,小数,负数) */
  const fraction = ['角', '分']
  const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  const unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']]
  const head = n < 0 ? '负' : ''
  n = Math.abs(n)

  let s = ''

  for (var i = 0; i < fraction.length; i++) {
    s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '')
  }
  s = s || '整'
  n = Math.floor(n)

  for (var i = 0; i < unit[0].length && n > 0; i++) {
    let p = ''
    for (let j = 0; j < unit[1].length && n > 0; j++) {
      p = digit[n % 10] + unit[1][j] + p
      n = Math.floor(n / 10)
    }
    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s
  }
  return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整')

}
interface EnumsItem {
  value: string
}
export const filterEnums = (value: string, enums: EnumsItem[]) => {
  if (isNull(value)) {
    return {
      value: '-',
      label: '-'
    }
  }
  if (isNull(enums)) {
    return {
      value: '-',
      label: '-'
    }
  }
  const filterEnum = enums?.filter((item: EnumsItem) => {
    if (String(item.value) === String(value)) return item
  })
  if (filterEnum.length > 0) return filterEnum[0]
  else {
    return {
      value: '-',
      label: '-'
    }
  }
}

export const getBasParams = (data: any = {}) => {
  const params: any = {}
  for (const i in data) {
    const val = data[i]
    params[i] = isString(val) ? val.trim() : val
  }
  return {
    ...params
  }
}

// 根据路径获取文件名
export function getFileName (path: string) {
  const arr = path?.split('/') || []
  return arr[arr.length - 1]
}

/**
 * deepMergeReplace  比对更新 传了属性就更新（不管是undefined,null还是其他） 没有传就不更新
 * @param source
 * @param target
 */
export function deepMergeReplace (source: Recordable, target: Recordable): Recordable {
  const result = cloneDeep(source)
  for(const key in target){
    if(isObj(target[key])){
      result[key] = deepMergeReplace(source[key] || {}, target[key])
    } else {
      result[key] = target[key]
    }
  }
  return result
}

/**
 * 将图片转成base64
 * 图片上传之后我们并不能直接获取到图片的分辨率，我们需要先将其转为base64的格式再去获取图片的分辨率，我们可以使用FileReader来先对文件进行转换
 * */
export function imgToBase64 (file:any) {
  const reader = new FileReader()
  reader.readAsDataURL(file)
  return new Promise((resolve) => {
    reader.onload = () => {
      resolve(reader.result)
    }
  })
}
/**
 * 获取图片的分辨率
 * */
export function getImgPx (img:string) {
  const image = new Image()
  image.src = img
  return new Promise((resolve) => {
    image.onload = () => {
      const width = image.width
      const height = image.height
      resolve({ width, height })
    }
  })
}

export type ItemType = {
  key: string
  children?: ItemType[]
}

/**
 * @description 工具函数---从多级数组中递归找出key
 * @param data 数据源
 * @param key 目标key
 */
export function getAllKeysTool (data: ItemType[], key = 'key') {
  return data.reduce(
    (cur, pre) => {
      cur.push(pre.key)
      if (pre?.children?.length) {
        cur = cur.concat(getAllKeysTool(pre.children, key))
      }

      return cur
    }, 
    [] as string[]
  )
}

/**
 * @description 工具函数---过滤掉多级数组中不符合要求的数据，支持多层级和排序
 * @param data 数据源
 * @param filterFunc 过滤判断函数
 * @param sortKeys 排序keys
 */
export function recursionFilterTool (data: ItemType[], filterFunc: Function, sortKeys: TreeKey[] = []) {
  // 递归函数
  const recursionFunc = (nodes: ItemType[]): ItemType[] => {
    return nodes
    .sort((a, b) => sortKeys.indexOf(a.key) - sortKeys.indexOf(b.key))
    .filter((node: ItemType) => {
      // 对当前节点进行过滤
      const children = node.children ? 
        recursionFunc(node.children.sort((a, b) => sortKeys.indexOf(a.key) - sortKeys.indexOf(b.key))) 
        : []

      // 如果当前节点或其子节点通过过滤，则保留
      const state = filterFunc(node) || children.length > 0
      if (state) {
        // 若子节点过滤后为空，则移除children属性
        if (children.length === 0) delete node.children
        // 否则赋予过滤后的子节点
        else node.children = children.sort((a, b) => sortKeys.indexOf(a.key) - sortKeys.indexOf(b.key))
      }

      return state
    })
  }
   
  return recursionFunc(data)
}

/**
 * @description 工具函数---多级数组中查找符合条件的item
 * @param data 数据源
 * @param key 目标key
 * @param value 目标key对应value
 */
export function findItemTool (data: Recordable[], key: string, value: string | number): Recordable | null {
  for (let i = 0; i < data.length; i++) {
    if (data[i][key] === value) {
      return data[i]
    }

    if (data[i]?.children?.length > 0) {
      const targetItem = findItemTool(data[i].children, key, value)
      if (targetItem) {
        return targetItem
      }
    }
  }

  return null
}