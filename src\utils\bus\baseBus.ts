class Bus {
  public eventList:any
  constructor () {
    // 收集订阅信息,调度中心
    this.eventList = {} // 事件列表，这项是必须的
  }
  // 订阅
  $on (name: string, fn:Fn) {
    this.eventList[name] = this.eventList[name] || []
    this.eventList[name].push(fn)
  }

  // 发布
  $emit (name: string, data?: any) {
    if (this.eventList[name]) {
      this.eventList[name].forEach((fn: Fn) => {
        fn(data)
      })
    }
  }

  // 取消订阅
  $off (name: string) {
    if (this.eventList[name]) {
      delete this.eventList[name]
    }
  }
}

export default new Bus()