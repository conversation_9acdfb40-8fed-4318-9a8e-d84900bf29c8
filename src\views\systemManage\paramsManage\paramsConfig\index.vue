<template>
  <div class="params-config">
    <SearchFilter @register="registerSetting" @search="search" />
    <div class="operate-content">
      <div class="group-tabs">
        <el-tabs
          v-model="state.activeTab"
          type="card"
          class="demo-tabs"
          @tab-change="handleTabChange"
        >
          <el-tab-pane v-for="group in state.paramsGroupOptions" :label="group.name" :name="group.id"></el-tab-pane>
        </el-tabs>
      </div>
      
      <div class="right-content">
        <RxkButton type="primary" @click="handleAddDetail('add')">添加参数</RxkButton>
      </div>
    </div>
    <div class="table-box">
      <RxkVTable @register="registerTable" />
    </div>
    <AddDraw v-if="state.showAddDetailVisible" v-model:visible="state.showAddDetailVisible" :type="state.operateType" :data="state.operateItem" @close="state.showAddDetailVisible = false,state.operateItem=null" @confirm="handleAddEdit" />
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref, unref } from 'vue'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { SearchFilter } from '@/components/business/searchFilter'
import type { ColumnType } from '@/types/table'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { RxkButton } from '@/components/common/RxkButton'
import { useResource } from '@/hooks/useResource'
import type { paramsItem } from "../type";
import { addParamsApi, paramsPageApi, updateParamsApi, paramGroupListApi } from "@/apis/paramsManage";
import AddDraw from "./components/add.vue";
const { getAuth } = useResource()
const state = reactive({
  operateType: '',
  operateItem: null as paramsItem | null,
  showAddDetailVisible: false,
  activeTab: '',
  paramsGroupOptions: [] as {id: string, name: string}[]
})
const searchInfo = {
  count: false,
  model: {
    name: '',
    groupId: '',
    code: ''
  },
  orderBys: [],
  searches: ''
}
// 自定义表单数据
const searchFormData = ref<FormSchema[]>([
  {
    fieldName: '参数名称',
    component: 'Input',
    key: 'name',
    val: '',
    componentProps: {
      clearable: true,
      placeholder: '请输入参数名称',
      maxlength: 50
    }
  },
  {
    fieldName: '参数代码',
    component: 'Input',
    key: 'code',
    val: '',
    componentProps: {
      clearable: true,
      placeholder: '请输入参数代码',
      maxlength: 50
    }
  }
])
const columns = ref<ColumnType[]>([
  { key: 'id', title: 'ID', },
  { key: 'name', title: '参数名称', width: 300,},
  { key: 'description', title: '参数描述', width: 120 },
  { key: 'code', title: '参数代码' },
  { key: 'value', title: '参数值', width: 140, },
  { 
    key: 'operate',
    title: '操作',
    width: 120,
    render: ({ cellData }) => {
      return (
        <>
          { <RxkButton class={['operate-btn', cellData?.readOnly.toString() === '1' ? 'disabled' : '']} text onClick={() => handleAddDetail('edit', cellData)} >编辑</RxkButton> }
          { <RxkButton class="operate-btn" onClick={() => handleAddDetail('detail', cellData)} text>详情</RxkButton> }
        </>
      )
    }
  },
])
const getBasicColumns = () => unref(columns)
const [registerTable, { reload, setProps }] = useTable({
  api: paramsPageApi,
  columns: getBasicColumns(),
  searchInfo: searchInfo,
  immediate: false, // 是否立刻请求
  showRowIndex: false
})


const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})
const search = (val: { [k: string]: any }) => {
  searchInfo.model.name = val.name
  searchInfo.model.code = val.code
  searchInfo.model.groupId = state.activeTab
  setProps({searchInfo: {...searchInfo}})
  reload()
}

const handleAddDetail = (sign: string, row?: paramsItem)=>{
  if(sign === 'edit' && row?.readOnly.toString() === '1') return
  state.operateType = sign
  state.operateItem = row ? row : null
  state.showAddDetailVisible = true
}
function handleTabChange(){
  search({...searchInfo.model})
}
function handleAddEdit(form: any, fn: Fn){
 const apiFn = state.operateType === 'add' ? addParamsApi : updateParamsApi
 apiFn(form).then(()=>{
  ElMessage.success(`${state.operateType === 'add' ? '新增' : '编辑'}成功`);
  state.showAddDetailVisible = false
  reload()
 }).finally(()=>{
  fn?.()
 })
}
// 获取参数分组
function getParamsGroups () {
  paramGroupListApi().then((res)=>{
    state.paramsGroupOptions = res.length ? res : []
    state.activeTab = res.length ? res[0].id : ''
    search({...searchInfo.model})
  })
}
getParamsGroups()
</script>

<style lang="scss" scoped>
.params-config{
  height: 100%;
  overflow: hidden;
  @include flex-center(column, normal, normal);
  .operate-content{
    @include flex-center(row, space-between);
    padding: 0 16px 16px;
    :deep(.group-tabs){
      color: #666666;
      font-size: 14px;
      flex: 1;
      overflow: auto;
      margin-right: 48px;
      .el-tabs{
        height: 28px;
      }
      .el-tabs__header{
        margin-bottom: 0;
        overflow: hidden;
        border: none;
        height: 28px;
        .el-tabs__nav{
          border: none;
        }
        .el-tabs__nav-next, .el-tabs__nav-prev{
          line-height: 28px;
          border-radius: 4px;

          &:hover{
            background-color: #F4F4F5;
          }
        }
        .el-tabs__item{
          padding: 4px 10px;
          border: none;
          border-radius: 4px;
          height: fit-content;
          line-height: 20px;
          color: #666666;
          font-size: 12px;
          &.is-active{
            background: #EEF3FF;
            color: var(--el-color-primary);
          }
          &:hover{
            background-color: #F4F4F5;
          }
        }
      }
    }
  }
  .table-box{
    flex: 1;
    overflow: hidden;
  }
  :deep(.operate-btn){
    padding: 0;
    &:hover{
      background-color: transparent;
    }
    &.disabled{
      cursor: not-allowed;
      color: var(--el-disabled-text-color);
    }
  }
  .total-info{
    font-size: 13px;
    font-size: 13px;
    height: 100%;
    @include flex-center(row, normal, center);
    .label-text{
      color: #666;
    }
    .value-text{
      color: #333;
      margin-left: 8px;
    }
  }
}
</style>