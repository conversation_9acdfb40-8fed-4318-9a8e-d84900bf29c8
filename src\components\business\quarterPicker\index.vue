<template>
  <div class="yearPicker" ref="yearPicker" :width="width">
    <i class="dateIcon iconfont icon-leixingrijishijianactiveno"></i>
    <input
      class="_inner"
      ref="inputLeftRef"
      v-model="state.startShowQuarter"
      @focus="onFocus"
      type="text"
      name="quarterInput"
      readonly
      placeholder="开始时间"
    />
    <span>{{ sp }}</span>
    <input
      class="_inner"
      ref="inputRightRef"
      v-model="state.endShowQuarter"
      @focus="onFocus"
      type="text"
      name="quarterInput"
      readonly
      placeholder="结束时间"
    />
    <div class="_inner floatPanel" v-if="state.showPanel">
      <div class="_inner leftPanel">
        <div class="_inner panelHead">
          <i class="_inner el-icon-d-arrow-left" @click="onClickLeft"></i>
          <div style="font-size: 16px;color: #666;">{{ state.lastYear }} 年</div>
          <span></span>
        </div>
        <div class="_inner panelContent">
          <div
            :class="{
              oneSelected: item.index === state.startQuarter && oneSelected,
              startSelected: item.index === state.startQuarter,
              endSelected: item.index === state.endQuarter,
              betweenSelected: Number(item.index) >= Number(state.startQuarter) && Number(item.index) <= Number(state.endQuarter),
            }"
            v-for="item in leftQuarterList"
            :key="item.index"
          >
            <a
              :class="{
                cell: true,
                _inner: true,
                selected: item.index === state.startQuarter || item.index === state.endQuarter,
              }"
              @click="onClickItem(item)"
              @mouseover="onHoverItem(item)"
            >
              {{ item.label }}
            </a>
          </div>
        </div>
      </div>
      <div class="_inner rightPanel">
        <div class="_inner panelHead">
          <span></span>
          <div style="font-size: 16px;color: #666;">{{ state.curYear }} 年</div>
          <i class="_inner el-icon-d-arrow-right" @click="onClickRight"></i>
        </div>
        <div class="_inner panelContent">
          <div
            :class="{
              startSelected: item.index === state.startQuarter,
              endSelected: item.index === state.endQuarter,
              betweenSelected: Number(item.index) >= Number(state.startQuarter) && Number(item.index) <= Number(state.endQuarter),
            }"
            v-for="item in rightQuarterList"
            :key="item.index"
          >
            <a
              :class="{
                cell: true,
                _inner: true,
                selected: item.index === state.endQuarter || item.index === state.startQuarter,
              }"
              @click="onClickItem(item)"
              @mouseover="onHoverItem(item)"
            >
              {{ item.label }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, computed, onBeforeMount, onMounted, reactive, ref, nextTick } from "vue" 
const SELECT_STATE = {
  unselect: 0,
  selecting: 1,
  selected: 2
};
const props = defineProps({
  width: {
    default: 200
  },
  labelWidth: {
    default: 80
  },
  sp: {
    default: '至'
  },
  value: {
    default: {}
  }
})
const emit = defineEmits(['quarterChange'])
const state = reactive({
  startShowQuarter: null as null | number,
  endShowQuarter: null as null | number,
  quarterList: [],
  showPanel: false,
  startQuarter: null as null | number,
  endQuarter: null as null | number,
  curYear: 0, // 右侧年份
  curSelectedQuarter: 0,
  curState: SELECT_STATE.unselect,
  lastYear: 0, // 左侧年份
  startQuarterInfo: {},
  ensQuarterInfo: {},
  monthList: [
    [1, 2, 3],
    [4, 5, 6],
    [7, 8, 9],
    [10, 11, 12]
  ]
})
const inputLeftRef = ref()
const inputRightRef = ref()
const oneSelected= computed(()=>{
  return (
        state.curState === SELECT_STATE.selecting &&
        (state.startQuarter === state.endQuarter || state.endQuarter == null)
      );
})
const leftQuarterList= computed(()=>{
  return state.quarterList.slice(0, 4);

})
const rightQuarterList= computed(()=>{
  return state.quarterList.slice(4, 8);

})
function onHoverItem (item) {
  console.log(item.index);
  if (state.curState === SELECT_STATE.selecting) {
    const tmpStart = Number(state.curSelectedQuarter);
    state.endQuarter = Math.max(tmpStart, Number(item.index));
    state.startQuarter = Math.min(tmpStart, Number(item.index));
  }
}
function onClickItem (item) {
  console.log(state.startQuarter, item.index, 'onClickItem');
  if (
    state.curState === SELECT_STATE.unselect ||
    state.curState === SELECT_STATE.selected
  ) {
    state.startQuarter = item.index;
    state.curSelectedQuarter = item.index;
    state.endQuarter = null;
    state.curState = SELECT_STATE.selecting;
  } else if (state.curState === SELECT_STATE.selecting) {
    state.curState = SELECT_STATE.selected;
    if (state.endQuarter === null) {
      state.endQuarter = item.index;
    };
    state.startQuarterInfo = state.quarterList.filter(item => item.index === state.startQuarter)[0];
    state.endQuarterInfo = state.quarterList.filter(item => item.index === state.endQuarter)[0];
    state.startShowQuarter = `${state.startQuarterInfo?.year}年第${state.startQuarterInfo?.label}`;
    state.endShowQuarter = `${state.endQuarterInfo?.year}年第${state.endQuarterInfo?.label}`;
    console.log(state.startQuarter, state.endQuarter);
    emit('quarterChange', {
      startQuarter: state.startQuarterInfo,
      endQuarter: state.endQuarterInfo
    });
    state.showPanel = false;
  }
}
function onFocus () {
  nextTick(() => {
    state.showPanel = true;
  });
}
function updateQuarterList (currentQuarter?: any) {
  let rightIndex = 5;
  for (const i of '四三二一') {
    rightIndex--;
    const item = { month: state.monthList[rightIndex - 1], label: i + '季度', year: state.curYear, quarter: rightIndex, index: Number(String(state.curYear) + rightIndex) };
    state.quarterList.unshift(item);
  }
  let leftIndex = 5;
  for (const i of '四三二一') {
    leftIndex--;
    const item = { month: state.monthList[leftIndex - 1], label: i + '季度', year: state.lastYear, quarter: leftIndex, index: Number(String(state.lastYear) + leftIndex) };
    state.quarterList.unshift(item);
  }
  if (currentQuarter) {
    state.startQuarterInfo = state.quarterList.filter(item => item.quarter === currentQuarter.quarter)[0];
    state.endQuarterInfo = state.quarterList.filter(item => item.quarter === currentQuarter.quarter)[0];
    state.startShowQuarter = `${currentQuarter.year}年第${state.startQuarterInfo?.label}`;
    state.endShowQuarter = `${currentQuarter.year}年第${state.endQuarterInfo?.label}`;
    
    state.startQuarter = Number(String(currentQuarter.year) + state.startQuarterInfo?.quarter);
    state.endQuarter = Number(String(currentQuarter.year) + state.endQuarterInfo?.quarter);
  }
  console.log(state.quarterList);
}
function closePanel (e) {
  if (!state.showPanel) {
    return;
  }
  if (typeof e.target.className !== 'string') {
    nextTick(() => {
      state.showPanel = false;
    });
    return;
  }
  if (
    e.target.className.indexOf('_inner') === -1 ||
    (e.target.name === 'quarterInput' &&
      e.target !== inputLeftRef.value &&
      e.target !== inputRightRef.value)
  ) {
    nextTick(() => {
      state.showPanel = false;
    });
  }

  e.stopPropagation();
  return false;
}
function onClickLeft () {
  state.lastYear = state.lastYear * 1 - 1;
  state.curYear = state.lastYear * 1 + 1;
  updateQuarterList();
}
function onClickRight () {
  state.curYear = state.curYear * 1 + 1;
  state.lastYear = state.curYear * 1 - 1;
  updateQuarterList();
}
watch(()=>props.value, (val)=>{
  if(val){
    // 默认展示当当年季度
    state.curYear = props.value.year;
    state.lastYear = state.curYear * 1 - 1;
    updateQuarterList(props.value);
  }
})

onBeforeMount(()=>{
  document.removeEventListener('click', closePanel.bind(this));
})
onMounted(()=>{
  document.addEventListener('click', closePanel.bind(this));
})
</script>

<style lang="scss" scoped>
.yearPicker {
  font-size: 14px;
  display: inline-block;
  position: relative;
  transition: all 0.3s;
  .labelText {
    position: absolute;
    left: 8px;
  }
  background-color: #fff;
  span {
    height: 32px;
    line-height: 32px;
    color: #666666;
    font-size: 12px;
  }
  border: 1px solid #DCDFE6;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  padding: 0 8px;
  box-sizing: border-box;
  .floatPanel {
    > div {
      width: 50%;
      padding: 0 16px;
    }

    position: absolute;
    display: flex;
    background-color: #fff;
    z-index: 2000;
    border-radius: 4px;
    width: 464px;
    height: 190px;
    top: 40px;
    left: -185px;
    border: 1px solid #d2d2d2;
    .panelContent {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: calc(100% - 70px);
      .oneSelected {
        border-top-right-radius: 24px;
        border-bottom-right-radius: 24px;
      }
      .startSelected {
        background-color: #ECF2FE;
        border-top-left-radius: 24px;
        border-bottom-left-radius: 24px;
      }
      .endSelected {
        background-color: #ECF2FE;
        border-top-right-radius: 24px;
        border-bottom-right-radius: 24px;
      }
      .betweenSelected {
        background-color: #ECF2FE;
      }
      > div {
        width: 99px;
        height: 48px;
        line-height: 48px;
        margin: 3px 0;
        text-align: center;
        font-size: 12px;
        a {
          display: inline-block;
          width: 80px;
          height: 40px;
          cursor: pointer;
          line-height: 40px;
          border-radius: 18px;
        }
        .selected {
          background-color: #409EFF;
          color: #fff;
        }
      }
    }
    .panelHead {
      position: relative;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0 8px;
      i {
        cursor: pointer;
        &:hover {
          color: #409EFF;
        }
      }
    }
    .leftPanel .panelHead i {
      left: 20px;
    }
    .rightPanel .panelHead i {
      right: 20px;
    }
  }
  .floatPanel::before {
    content: "";
    height: 100%;
    position: absolute;
    left: 50%;
    width: 1px;
    border-left: 1px solid #e4e4e4;
  }
}
input {
  width: 99px;
  border: none;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
  background-color: transparent;
  color: #606266;
  text-align: center;
  font-size: 12px;
}
input:focus {
  outline: none;
  background-color: transparent;
}
input::placeholder {
  color: #adb2bc;
  font-size: 13px;
}
.yearPicker:hover {
  border-color: #409EFF;
}
.dateIcon {
  // position: absolute;
  // left: 10px;
  // top: 9px;
  color: #adb2bc;
}
</style>