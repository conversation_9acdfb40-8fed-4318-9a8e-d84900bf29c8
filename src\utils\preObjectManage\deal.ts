import { ref, unref } from 'vue'
export function useDealMap () {
  // 容器map
  const moduleMap = ref<Map<string, Recordable>>(new Map())
  // 分栏中的字段map
  const fieldMap = ref<Map<string, Recordable>>(new Map())
  // 每一个子表的map
  const subListMap = ref<Map<string, Map<string, Recordable>>>(new Map())

  // 字段Api的map
  const apiMap = ref<Map<string, Recordable>>(new Map())

  // 往容器map存
  function setModuleMap (key: string, data: Recordable) {
    moduleMap.value.set(key, data)
  }

  // 往分栏中的字段map存
  function setFieldMap (key: string, data: Recordable) {
    fieldMap.value.set(key, data)
  }

  // 往每个子表的map存
  function setSubListMap (key: string, subKey: string, data: Recordable) {
    console.log(data, key, subKey, 'data9988')
    // 找到当前子表
    const item = unref(subListMap).get(key)
    if (item) {
      item.set(subKey, data)
    } else {
      const map = new Map()
      map.set(subKey, data)
      subListMap.value.set(key, map)
    }
  }

  // 往字段Api的map存
  function setApiMap (key: string, data: Recordable) {
    apiMap.value.set(key, data)
  }

  // 删除容器map
  function deleteModuleMap (key: string) {
    if (moduleMap.value.has(key)) {
      moduleMap.value.delete(key)
    }
  }

  // 删除分栏中的字段map
  function deleteFieldMap (key: string) {
    if (fieldMap.value.has(key)) {
      fieldMap.value.delete(key)
    }
  }

  // 删除某个子表中的字段
  function deleteSubListMap (key: string, subKey: string) {
    // 找到当前子表
    const item = unref(subListMap).get(key)
    item && item.delete(subKey)
  }

  // 删除api中的map
  function deleteApiMap (key: string) {
    if (apiMap.value.has(key)) {
      apiMap.value.delete(key)
    }
  }

  // 修改容器map的内容
  function changeModuleMap (key: string, newData: Recordable) {
    const item = moduleMap.value.get(key)
    if (item) {
      moduleMap.value.set(key, newData)
    }
  }

  // 修改分栏中的字段map的内容
  function changeFieldMap (key: string, newData: Recordable) {
    const item = fieldMap.value.get(key)
    if (item) {
      fieldMap.value.set(key, newData)
    }
  }

  // 修改子表中的字段map的内容
  function changeSubListMap (key: string, subKey: string, data: Recordable) {
    // 找到当前子表
    const item = unref(subListMap).get(key)
    item && item.set(subKey, data)
  }

  // 修改api中的map
  function changeApiMap (key: string, newData: Recordable) {
    console.log(key, newData, 'lloojj')
    const item = apiMap.value.get(key)
    if (item) {
      apiMap.value.set(key, newData)
    }
  }

  // 判断容器map的内容是否重复
  function uniqueModuleMap (keyword: string) {
    const arr: Recordable[] = []
    const map = new Map()
    for (const [key, value] of unref(moduleMap)) {
      if (map.has(value[keyword])) {
        arr.push({ ...value })
      } else {
        map.set(value[keyword], key)
      }
    }
    return arr
  }

  // 判断分栏中的字段的内容以及子表map的内容是否重复
  function uniqueFieldMap (keyword: string) {
    const arr: Recordable[] = []
    console.log(subListMap.value.size, fieldMap, 'fieldMap9999')
    if (subListMap.value.size) {
      for(const [key, value] of unref(subListMap)) {
        const map = new Map()
        // 当前子表中的字段与所有分栏中的字段是否重复
        const newMap = new Map([...value.entries(), ...unref(fieldMap).entries()] )
        for (const [key, value] of newMap) {
          if (map.has(value[keyword])) {
            arr.push({ ...value })
          } else {
            map.set(value[keyword], key)
          }
        }
      }
    } else {
      const map = new Map()
      // 当前子表中的字段与所有分栏中的字段是否重复
      for (const [key, value] of unref(fieldMap)) {
        if (map.has(value[keyword])) {
          arr.push({ ...value })
        } else {
          map.set(value[keyword], key)
        }
      }

    }
    return Array.from(new Set(arr.map(obj => JSON.stringify(obj)))).map(str => JSON.parse(str))
  }

  // 判断同一个子表中的字段是否重复
  function uniqueSubListMap (keyword: string) {
    const arr: Recordable[] = []
    for(const [key, value] of unref(subListMap)) {
      const map = new Map()
      // 当前子表中的字段与所有分栏中的字段是否重复
      const newMap = new Map([...value.entries()] )
      for (const [key, value] of newMap) {
        if (map.has(value[keyword])) {
          arr.push({ ...value })
        } else {
          map.set(value[keyword], key)
        }
      }
    }
    return Array.from(new Set(arr.map(obj => JSON.stringify(obj)))).map(str => JSON.parse(str))
  }

  // 处理标题是否重复
  function uniqueTitle () {
    // 容器，字段错误
    let errUniqueArr: Recordable[] = []
    // 判断模块标题是否重复
    const moduleArr = uniqueModuleMap('title')
    const fieldArr = uniqueFieldMap('title')
    const subListArr = uniqueSubListMap('title')

    if (moduleArr.length) {
      errUniqueArr = errUniqueArr.concat(moduleArr)
    }
    if (fieldArr.length) {
      errUniqueArr = errUniqueArr.concat(fieldArr)
    }
    if (subListArr.length) {
      errUniqueArr = errUniqueArr.concat(subListArr)
    }
    return Array.from(new Set(errUniqueArr.map(obj => JSON.stringify(obj)))).map(str => JSON.parse(str))
  }

  function uniqueApiMap (keyword: string) {
    const arr: Recordable[] = []
    const map = new Map()
    for (const [key, value] of unref(apiMap)) {
      console.log(value, 'value99999')
      if (value[keyword]) {
        if (map.has(value[keyword])) {
          arr.push({ ...value })
        } else {
          map.set(value[keyword], key)
        }
      }
    }
    console.log(map, 'map9900')
    console.log(arr, 'arr92999')
    return arr
  }
  // 判断字段api是否重复
  function uniqueApi () {
    // 容器，字段错误
    let errUniqueArr: Recordable[] = []
    const moduleArr = uniqueApiMap('fieldApi')
    if (moduleArr.length) {
      errUniqueArr = errUniqueArr.concat(moduleArr)
    }
    return Array.from(new Set(errUniqueArr.map(obj => JSON.stringify(obj)))).map(str => JSON.parse(str))
  }
  return {
    moduleMap,
    setModuleMap,
    deleteModuleMap,
    changeModuleMap,
    fieldMap,
    setFieldMap,
    deleteFieldMap,
    changeFieldMap,
    subListMap,
    setSubListMap,
    deleteSubListMap,
    changeSubListMap,
    uniqueTitle,
    apiMap,
    setApiMap,
    deleteApiMap,
    changeApiMap,
    uniqueApi
  }
}