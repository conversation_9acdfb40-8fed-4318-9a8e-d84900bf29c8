<template>
  <div class="personnelSelection">
    <!-- 自定义触发内容 -->
    <div v-if="custom"
         style="width: inherit;"
         ref="customRef">
      <slot name="custom"/>
    </div>
    <div v-else style="width: inherit;">
      <!-- 默认触发内容 -->
      <el-select 
        style="width: 100%;"
        v-model="showValue"
        ref="selectRef"
        multiple
        :collapse-tags="collapseTags"
        collapse-tags-tooltip
        @visible-change="visibleChange"
        @focus="focus"
        @remove-tag="removeTag"
        v-bind="$attrs"
        :disabled="disabled"
        :placeholder="placeholderText">
        <el-option 
          v-for="(item, index) in selectedList"
          :key="index"
          :label="item.label"
          :disabled="item.disable"
          :value="item.value"/>
      </el-select>
    </div>
    <el-popover
      :visible="custom ? innerVisible : undefined"
      :virtual-ref="virtualRef"
      ref="popoverRef"
      trigger="click"
      title=""
      popper-class="personnelSelectionPopper"
      width="740"
      :placement="placement"
      destroy-on-close
      :popper-options="{
        modifiers: [{enabled:true}],
        strategy: 'fixed',
        placement:'auto'
      }"
      virtual-triggering
      @show="popoverShow">
      <SelectPanel
        @registerPanel="registerPanel"
        @close="close"
        @confirm="confirm"
        :showSure="showSure">
        <template #dynamic>
          <slot name="dynamic"/>
        </template>
      </SelectPanel>
    </el-popover>
  </div>
</template>

<script lang="ts" setup>
import { ref, unref, computed, onMounted, watch } from 'vue'
import SelectPanel from './selectPanel.vue'
import type { BasicProps } from './type'
import { usePanel } from './hooks/usePanel'
import { isFunc } from '@/utils/is'
const emit = defineEmits(['update:visible', 'update:value', 'sure', 'selectChange'])
const selectedList = ref<Recordable[]>([])

const popoverRef = ref()
const selectRef = ref()
const customRef = ref()
const virtualRef = ref()
const showPanel = ref<boolean>(false)
const showValue = ref<string[]>([]) // 展示的值

const innerVisible = computed({
  get: () => props.visible,
  set: (visible: boolean) => emit('update:visible', visible)
})

const props = withDefaults(defineProps<BasicProps>(), {
  custom: false,
  visible: false,
  activeName: 'members',
  paramsData: {},
  placeholder: '请选择',
  placement: 'bottom',
  collapseTags: true,
  disabled: false,
  showSure: true
})

const placeholderText = computed(() => {
  if(props?.dynamicParameters?.acquireCurrent || props?.dynamicParameters?.acquireCurrentFather){
    return '已配置动态参数'
  }
  return props.placeholder
})

const [registerPanel, { init, remove }] = usePanel()

function focus (){
  selectRef.value.blur()
}

async function visibleChange (){
  selectRef.value.blur()
  if(props.visibleBeforeFn && isFunc(props.visibleBeforeFn)) {
    const flag = await props.visibleBeforeFn()
    if(!flag){
      close()
    }
  }
  unref(popoverRef).popperRef?.delayHide?.()
  showPanel.value = true
}

function close () {
  if(props.custom) {
    innerVisible.value = false
  } else {
    unref(popoverRef).hide?.()
  }
  selectRef?.value?.blur()
  showPanel.value = false
}

function confirm (checkData:Recordable, noClose = false) {
  // 处理回显
  selectedList.value = checkData.list.map((el:Recordable) => {
    return {
      ...el,
      value: el.type + ':' + el.value
    }
  })
  showValue.value = checkData.list.map((el:Recordable) => el.type + ':' + el.value)
  if(!noClose){
    // 是否先对数据进行自定义的验证
    if(props.validateFn instanceof Function){
      props.validateFn(checkData).then(() => {
        emit('sure', checkData)
        close()
      }).catch(e => {
        console.log(e)
      })
    } else {
      emit('sure', checkData)
      close()
    }
  }
}

// 移除选项
function removeTag (tagValue: any) {
  let arr = tagValue.split(':')
  remove(arr[1], arr[0] )
}

// 首次加载
const firstLoad = ref(true)
function popoverShow (){
  if(unref(firstLoad)) {
    init(props)
    firstLoad.value = false
  }
}

// 确保传进来的数据已经请求到了
watch(() => props.data, (val) => {
  if(val.deComponentTreeVO !== undefined){
    if(unref(firstLoad)) {
      init(props)
      firstLoad.value = false
    }
  }
})

onMounted(async () => {
  if(!props.disabled) {
    virtualRef.value = props.custom ? customRef.value : selectRef.value
  }
  // 有默认值要先处理回显
  if(props.defaultValueData?.length){
    init(props)
    if(isFunc(props.api)){
      firstLoad.value = false
    }
  }
})
</script>

<style lang="scss">
.personnelSelectionPopper {
  padding: 0 !important;
  overflow-x: hidden;
  overflow-y: auto;
  height: auto;
}
</style>