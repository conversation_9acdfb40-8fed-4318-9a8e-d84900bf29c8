import type { PropType } from 'vue'

export const basicProps = {
  modelValue: {
    type: String as PropType<any>,
    default: ''
  },
  renderConfig: {
    type: Object as PropType<Recordable>,
    default: () => {}
  },
  data: {
    type: Object as PropType<Recordable>,
    default: () => {}
  },
  isTxt: {
    type: Boolean,
    default: false
  },
  positions: {
    type: Array as PropType<Recordable[]>,
    default: () => []
  }
}