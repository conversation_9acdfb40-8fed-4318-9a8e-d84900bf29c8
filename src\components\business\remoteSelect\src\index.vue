<template>
  <el-select
    class="customer-filter-select"
    v-model="innerValue"
    :multiple="multiple"
    :filterable="filterable"
    :remote="remote"
    :disabled="disabled"
    :placeholder="placeholder"
    remote-show-suffix
    :collapse-tags="collapseTags"
    clearable
    :remote-method="remoteMethod"
    :loading="loading"
    style="width: 100%"
    :value-key="valueKey"
    @change="changeValue"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      :disabled="item.disabled"
    />
  </el-select>
</template>

<script lang="ts" setup>
import type { ListItem, Field } from './type'
import { onMounted, ref, computed, type PropType } from 'vue'
import { platformAllApi } from '@/apis/dataStatistics'
const props = defineProps({
  modelValue: {
    type: [String, Object, Array],
    default: ''
  },
  multiple: {
    type: Boolean,
    default: true
  },
  getString: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: <PERSON>olean,
    default: true
  },
  collapseTags: {
    type: Boolean,
    default: true
  },
  remote: {
    type: <PERSON>olean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请输入'
  },
  valueKey: {
    type: String,
    default: 'value'
  },
  field: {
    type: Object as PropType<Field>,
    default: () => {
      return {
        label: 'label',
        value: 'value',
        data: ''
      }
    }
  },
  api: {
    type: Function,
    default: platformAllApi
  },
  disabledKeys: {
    type: Array,
    default: () => []
  }
})
const list = ref<ListItem[]>([])
const options = ref<ListItem[]>([])
const loading = ref(false)
const emit = defineEmits([
  'update:modelValue',
  'setOptions',
  'setItem',
  'change'
])

const innerValue = computed({
  get () {
    return props.multiple && props.getString
      ? props.modelValue
        ? props.modelValue.split(',')
        : []
      : props.modelValue
  },
  set (newVal) {
    emit(
      'update:modelValue',
      props.multiple && props.getString
        ? (newVal as string[]).join(',')
        : newVal
    )
  }
})

const getOptions = () => {
  let { label, value, data } = props.field
  props.api().then((res: any) => {
    const resData = data ? res[data] : res

    list.value = resData.map((i: { [x: string]: any }) => ({
      ...i,
      label: i[label],
      value: i[value],
      disabled: props.disabledKeys.includes(i[value])
    }))
    options.value = list.value
    emit('setOptions', list.value)
  })
}

onMounted(() => {
  getOptions()
})

const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      options.value = list.value.filter((item: { [x: string]: any }) => {
        return item.label.includes(query)
      })
    }, 200)
  } else {
    options.value = list.value
  }
}

const changeValue = (val: any) => {
  const item = options.value.find((it) => it.value === val)
  if (!props.multiple) {
    emit('setItem', item)
  }
  emit('change', val, item)
}
defineExpose({
  getOptions
})
</script>
