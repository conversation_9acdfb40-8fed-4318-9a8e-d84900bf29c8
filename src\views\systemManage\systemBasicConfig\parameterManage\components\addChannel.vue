<template>
  <RxkDialog
    v-model:modelValue="show"
    title="新增渠道"
    width="480"
    custom-class="add-channel-dialog"
    @sure="submit"
    @close="close"
  >
    <el-form ref="formRef"
             :model="formData"
             :rules="rules"
             v-if="show"
             class="add-channel-form">
      <el-form-item label="渠道名称" prop="apiName">
        <RxkInput v-model="formData.apiName" :maxlength="20" />
      </el-form-item>
      <el-form-item label="对接类型" prop="dockingType">
        <RxkSelect v-model="formData.dockingType" :list="dockingTypeEnum" />
      </el-form-item>
      <el-form-item label="加密方式" prop="filterType">
        <RxkSelect v-model="formData.filterType" :list="filterTypeEnum" />
      </el-form-item>
    </el-form>
  </RxkDialog>
</template>

<script setup lang="ts">
import { ref, unref, watch, computed } from 'vue'
import { RxkDialog } from '@/components/common/RxkDialog/index'
import { RxkInput, RxkSelect } from '@/components/common'
import { dockingTypeEnum, filterTypeEnum } from '../data'
import type { FormRules } from 'element-plus'
import { addCustomChannel } from '@/apis/systemBasicConfig'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:visible', 'refresh'])
const show = computed({
  get () {
    return props.visible
  },
  set (val) {
    emits('update:visible', val)
  }
})
watch(() => show.value, (val) => {
  if (!val) {
    formData.value = {
      dockingType: '',
      filterType: ''
    }
  }
})
const formData = ref<Recordable>({
  dockingType: '',
  filterType: ''
})
const rules = ref<FormRules>({
  apiName: { required: true, message: '请输入渠道名称', trigger: 'blur' },
  dockingType: { required: true, message: '请选择对接类型', trigger: 'change' },
  filterType: { required: true, message: '请选择加密方式', trigger: 'change' }
})
const formRef = ref()
function close () {
  show.value = false
}
function submit () {
  if (!formRef.value) return
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      addCustomChannel(unref(formData)).then(() => {
        ElMessage.success('操作成功')
        show.value = false
        emits('refresh')
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.add-channel-form {
  padding-top: 16px;
  padding-left: 24px;
  padding-right: 24px;
}
</style>
<style lang="scss">
.add-channel-dialog {
  .el-dialog__body {
    overflow-y: hidden;
    height: auto;
  }
  .footer {
    border-top: none;
  }
}
</style>