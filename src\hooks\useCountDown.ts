/**
 * @auth dmj
 * */

import { ref, unref } from 'vue'

export function useCountDown (value: number) {
  const count = ref<number>(value)
  const startBoolean = ref<boolean>(false)
  let timerId: ReturnType<typeof window.setInterval> | null
  function start () {
    if (unref(startBoolean)) {
      return
    }
    startBoolean.value = true
    timerId = window.setInterval(() => {
      if (unref(count) === 1) {
        stop()
        count.value = value
      } else {
        count.value -= 1
      }
    }, 1000) as unknown as ReturnType<typeof window.setInterval>
  }
  function stop () {
    startBoolean.value = false
    timerId && window.clearInterval(timerId)
    timerId = null
  }
  return {
    count,
    start,
    stop,
    isStart: startBoolean
  }
}
