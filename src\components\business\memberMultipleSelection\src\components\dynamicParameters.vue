<template>
  <div class="dynamicParameters">
    <div class="dynamicParameters-item">
      <el-checkbox v-model="dynamicParameters.acquireCurrent"
                   :true-label="1"
                   :false-label="0"
                   @change="selectChange">当前新增/修改数据的用户</el-checkbox>
    </div>
    <div class="dynamicParameters-item">
      <el-checkbox v-model="dynamicParameters.acquireCurrentFather"
                   :true-label="1"
                   :false-label="0"
                   @change="selectChange">当前新增/修改数据的用户的直属领导</el-checkbox>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, unref, type PropType } from 'vue'
import type { DynamicParameters } from '../type'
const emit = defineEmits(['update:dynamicParameters', 'updateParameter'])
const props = defineProps({
  data: {
    type: Object as PropType<DynamicParameters>,
    default: () => {[]}
  }
})
const dynamicParameters = computed({
  get: () => unref(props.data),
  set: (newValue) => {
    emit('update:dynamicParameters', newValue )
  }
})

function selectChange (){
  emit('updateParameter', dynamicParameters.value)
}
</script>

<style lang="scss" scoped>
.dynamicParameters {
  height: 100%;
  padding: 16px 0 0 16px;
  &-item {
    display: block;
    width: 100%;
  }
}
</style>