import { ref, unref } from 'vue'
import type { BasicProps, DynamicParameters, UserVOList } from '../type'
export interface PanelActionType{
  init: (props:BasicProps, fn?: Fn) => void;
  getCheckedUser: () => UserVOList[];
  updateValue: (data: UserVOList[], type?: string) => void;
  getSelectedShow: () => boolean;
  getContainChildren: () => boolean;
  getSaveDefault: () => boolean;
  getParameter: () => DynamicParameters;
  reload: (props:BasicProps, fn?: Fn) => void;
  handleCheckedValue: (data: UserVOList[]) => void;
}
export function usePanel ():[(instance: PanelActionType) => void, PanelActionType] {
  const panelRef = ref<Nullable<PanelActionType>>(null)
  function registerPanel (instance: PanelActionType, fn?:Fn){
    if (instance === unref(panelRef)) return
    panelRef.value = instance
    fn && fn()
    // props && instance.init(props, fn)
  }
  function getPanelInstance (): PanelActionType {
    const panel = unref(panelRef.value)
    return panel as PanelActionType
  }
  const methods: PanelActionType = {
    init: (props:BasicProps, fn?: Fn) => {
      getPanelInstance()?.init?.(unref(props), fn)
    },
    getCheckedUser: () => {
      return getPanelInstance().getCheckedUser()
    },
    updateValue: (data: UserVOList[], type?: string) => {
      getPanelInstance()?.updateValue(data, type)
    },
    getSelectedShow: () => {
      return getPanelInstance().getSelectedShow()
    },
    getContainChildren: () => {
      return getPanelInstance().getContainChildren()
    },
    getSaveDefault: () => {
      return getPanelInstance().getSaveDefault()
    },
    getParameter: () => {
      return getPanelInstance().getParameter()
    },
    reload: (props:BasicProps, fn?: Fn) => {
      getPanelInstance()?.reload?.(unref(props), fn)
    },
    handleCheckedValue: (data: UserVOList[]) => {
      getPanelInstance()?.handleCheckedValue(data)
    }
  }
  return [registerPanel, methods]
}