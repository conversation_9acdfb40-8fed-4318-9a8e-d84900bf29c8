<template>
  <div
    class="base-table-page"
    :class="{
      reHeight: getProps.showPagination,
      showfootheight: getProps.showFooter
    }"
  >
    <!-- 列表字段分组配置 -->
    <div
      class="group-btnBox"
      :style="{ marginBottom: getProps.showSetting ? '16px' : 'unset' }"
    >
      <ColumnGroupSetting
        v-if="getProps.showSetting"
        :is-join="getProps.isJoin"
        :columns="getProps.tableColumns"
        :extraKey="getProps.extraKey"
        :isMultiLevelHeader="getProps.isMultiLevelHeader"
        @columnLoaded="
          (data, otherActionObj) => columnLoadedFunc(data, otherActionObj)
        "
      />

      <slot name="btnBox" />
    </div>

    <div class="tw-h-full">
      <vxe-table
        ref="tableRef"
        :class="['custom-table', { isShowSetting: getProps.showSetting }]"
        :column-config="{ resizable: getProps.resizeAble, maxFixedSize: 1000 }"
        :loading="loading"
        border="none"
        :show-footer="getProps.showFooter"
        :data="getBindValues.data"
        :checkbox-config="checkboxConfig"
        :row-config="{ keyField: getProps.rowKey, useKey: true, isHover: true }"
        :scroll-y="{ enabled: true, gt: 50 }"
        v-bind="$attrs"
        :cell-style="(params) => cellStyle(params, 'cell-style')"
        :header-cell-style="(params) => cellStyle(params, 'header-cell-style')"
        :sort-config="sortConfig"
        auto-resize
        @sortChange="sortChangeFn"
        @resizable-change="($event: any) => resizableChange($event,refreshColumns)"
      >
        <vxe-column
          type="checkbox"
          :fixed="config.layout.shrink ? undefined : 'left'"
          :width="getFieldWidthValue('customCheck') || 60"
          field="customCheck"
          v-if="getProps.showSelection"
        >
          <template #header="{ checked, indeterminate }">
            <div class="batchSelect">
              <el-checkbox
                :modelValue="checked"
                :indeterminate="indeterminate"
                @change="toggleAllCheckboxEvent"
              />
              <!-- 批量筛选 -->
              <TableBatchSelect
                v-if="getProps.showbatchSelect"
                @registerTableBatchSelect="registerTableBatchSelect"
                :tableTotal="total"
                @tableBatchSelectSure="tableBatchSelectSure"
              />
            </div>
          </template>
          <template #checkbox="{ row, checked, indeterminate, disabled }">
            <el-checkbox
              :modelValue="checked"
              :disabled="isRowDisabled(row) || disabled"
              @change="toggleCheckboxEvent(row)"
            />
          </template>
        </vxe-column>

        <vxe-column
          type="seq"
          :width="getFieldWidthValue('customSeq') || 60"
          min-width="60"
          :fixed="config.layout.shrink ? undefined : 'left'"
          title="序号"
          :tree-node="getProps.isTreeNode"
          field="customSeq"
          v-if="getProps.showRowIndex"
        />
        <!-- 表里面的字段 -->
        <DynamicTableHeader :columns="getBindValues.columns">
          <template v-for="slot in Object.keys($slots)" #[slot]="scope">
            <slot :name="slot" v-bind="scope || {}" />
          </template>
        </DynamicTableHeader>
      </vxe-table>
    </div>
  </div>
  <div
    class="pagination-box"
    :class="{ paginationshowfoot: getProps.showFooter }"
  >
    <div style="display: flex">
      <slot name="leftSlot" />
    </div>
    <el-pagination
      v-if="getProps.showPagination"
      :class="[
        'custom-pagination',
        { 'custom-pagination-mini': config.layout.shrink }
      ]"
      :current-page="getPaginationInfo.pageNum"
      :page-size="Number(paginationRes?.value) || getPaginationInfo.pageSize"
      :page-sizes="getProps?.pageSizes || pageSizes"
      :layout="
        config.layout.shrink
          ? 'total, prev, next, jumper'
          : 'total, sizes, prev, pager, next, jumper'
      "
      :total="total"
      @size-change="handleSizeChangeFn"
      @current-change="handleCurrentChangeFn"
    />
  </div>
</template>
<script lang="ts" setup>
import './style.scss'
import {
  computed,
  unref,
  ref,
  watch,
  toRaw,
  useAttrs,
  nextTick,
  getCurrentInstance
} from 'vue'
import { useTableData } from './hooks/useTableData'
import type { BasicTableProps, ColumnSettingEmitType } from './type'
import { useColumns } from './hooks/useColumns'
import type { TableActionType } from './type'
import { usePagination } from './hooks/usePagination'
import { isNullOrUndefOrEmpty, isFunc, isObj } from '@/utils/is'
import { useSection } from './hooks/useSection'
import type { TableData } from './type'
import { useConfig } from '@/stores/modules/config'
import TableBatchSelect from './TableBatchSelect.vue'
// import { useRoute } from 'vue-router'
import {
  useTableBatchSelect,
  type BatchData
} from './hooks/useTableBatchSelect'
// import RxkSingeLineTooltip from '@/components/common/RxkSingeLineTooltip'
import { useResizeColumn } from './hooks/useResizeColumn'
import DynamicTableHeader from './DynamicTableHeader.vue'
import type { ColumnType } from '@/types/table'
// const maxSelections = 10
// const route = useRoute()
const attrs = useAttrs()
const config = useConfig()
// 复选框配置项
const checkboxConfig = {
  reserve: true,
  checkMethod: ({ row }) => {
    // console.log(getSectionData())
    // const len = getSectionData().length
    // if (len && len >= maxSelections) {
    //   ElMessage({
    //     type: 'error',
    //     message: `批量操作已达最大数量${maxSelections}`,
    //     grouping: true
    //   })
    //   // if (index + 1 === this.paginationData.pageSize) {
    //   //   // this.$message.info(`批量操作的最大数量为${this.maxSelections}`)
    //   //   if (this.tipsNum === 0) {
    //   //     this.$message.info(`批量操作已达最大数量${this.maxSelections}`)
    //   //     this.tipsNum++
    //   //   }
    //   // }
    //   // if (this.tableSelectionData.some(item => item[this.rowKey] === row[this.rowKey])) {
    //   return false
    // } else {
    //   return true
    // }
    return true
  }
}
// 禁选事件
const isRowDisabled = (row: any) => {
  return attrs?.disabledFn?.(row) || false
}
const instance = getCurrentInstance()!
// 递归获取所有父级组件名称
function getParentNames (instance: any): string[] {
  const whiteName = ['App', 'index', 'BasicDialog']
  const names: string[] = []
  let currentInstance = instance
  while (currentInstance) {
    if (currentInstance.parent) {
      if (
        currentInstance.parent.type.__name &&
        !whiteName.includes(currentInstance.parent.type.__name)
      ) {
        names.push(currentInstance.parent.type.__name)
      }
      currentInstance = currentInstance.parent
    } else {
      break
    }
  }
  return names.reverse() // 确保父级组件名称顺序正确
}
const moduleId =
  getParentNames(instance).join('_') ||
  (instance.parent?.vnode?.key as string)
  ?.replace?.(/\//g, '_')
  ?.replace?.('_', '')
const tableRef = ref()
const emit = defineEmits([
  'register',
  'selectionChange',
  'getTableData',
  'getRefTableData',
  'sortChange',
  'columnLoaded',
  'changePage'
])
function sortChangeFn (column: Recordable) {
  console.log(column, 'column123')
  emit('sortChange', column)
}
const innerPropsRef = ref<Partial<BasicTableProps>>()
const searchInfo = ref({})
const sortConfig = ref<Recordable>({ remote: true, multiple: true })
const getProps = computed(() => {
  return {
    showPagination: true,
    showRowIndex: true,
    resizeAble: true,
    customTableConfig: true,
    ...unref(innerPropsRef),
    searchInfo: { ...unref(searchInfo), ...unref(innerPropsRef)?.searchInfo },
    rowKey: 'id',
    moduleId
  } as BasicTableProps
})
console.log(JSON.stringify(getProps.value), 'oop')
// 分页hooks
const { getPaginationInfo, handleSizeChange, handleCurrentChange, pageSizes } =
  usePagination(getProps)
// 表格数据hooks
const { tableData, reload, total, loading, paginationRes, extraMap } =
  useTableData(getProps, {
    getPaginationInfo,
    emit,
    tableRef,
    defalutLoading: !!attrs.defalutLoading
  })
const { resizableChange, mergeColumnWidth, getFieldWidthValue } =
  useResizeColumn(getProps, loading)

// 批量筛选hooks
const [
  registerTableBatchSelect,
  {
    getBatchSelectIds,
    getBatchSelectPageInfo,
    getBatchSelectIdsList,
    initBatchData
  }
] = useTableBatchSelect({
  startLimit: 1,
  endLimit: 10000
})

// 表头hooks
const { getColumns } = useColumns(getProps)
// 选择项hooks
const { selectionChange, sectionList } = useSection()
const getBindValues = computed(() => {
  const data = unref(tableData)
  return {
    data,
    columns: unref(getColumns)
  }
})

// 固定列list
const fixedArr = ref<Recordable[]>([])
// 自定义color列表
const customColorArr = ref<Recordable[]>([])

// 递归拍平并提取key值
function flattenAndExtractKeys (a: any[]): string[] {
  return a.flatMap((item) => [
    ...(item.key !== undefined ? [item.key] : []),
    ...(item.children && Array.isArray(item.children)
      ? flattenAndExtractKeys(item.children)
      : [])
  ])
}
// const refreshVisible = ref(true)
async function refreshColumns () {
  // eslint-disable-next-line
  let { collectColumn: a } = tableRef.value?.getTableColumn?.()
  let b = flattenAndExtractKeys(getBindValues.value.columns)
  // 获取字段索引的函数
  function getFieldIndex (b: any[], field: string): number {
    const index = b.findIndex((item) => item === field)

    // 序号放在最前面
    if (field === 'customSeq') {
      return -1
    }

    // 未找到的放最后
    if (index === -1) {
      return Infinity
    }

    return index
  }
  // 递归排序函数
  function recursiveSort (a: any[], b: any[]): any[] {
    if (!a || !Array.isArray(a)) {
      return a
    }

    // 对当前数组进行排序
    a.sort((item1: any, item2: any) => {
      return getFieldIndex(b, item1.field!) - getFieldIndex(b, item2.field)
    })

    // 递归对子数组进行排序
    a.forEach((item) => {
      if (item.children && Array.isArray(item.children)) {
        item.children = recursiveSort(item.children, b)
      }
    })

    return a
  }
  recursiveSort(a, b)

  await mergeColumnWidth(a)
  console.log('refreshColumns', a)

  tableRef.value?.loadColumn?.(a)
}

function getSyncData (columns: ColumnType[]) {
  columns.forEach((column) => {
    const api = column.syncData?.api
    if (api) {
      api(column.syncData.params).then((res) => {
        column.syncData.data = res
      })
    }
  })
}

watch(
  () => unref(getBindValues).columns,
  (val) => {
    // refreshVisible.value = false
    nextTick(() => {
      // refreshVisible.value = true
      refreshColumns()
      getSyncData(val)
      // 处理表格选中回显
      if (sectionList.value && sectionList.value.length > 0) {
        setTimeout(() => {
          batchTableSelectCheck(sectionList.value)
        }, 0)
      }
    })
  }
)

// function getColumnBindingValue (i: ColumnType) {
//   const { fixed, title, key, width, sortable } = i
//   const data: Recordable = {
//     title,
//     prop: key,
//     field: key,
//     minWidth: 80,
//     'show-header-overflow': true
//   }
//   // 移动端只固定right
//   !config.layout.shrink && fixed && (data.fixed = fixed)
//   width && (data.width = width)
//   sortable && (data.sortable = sortable)
//   return data
// }

function setProps (props: Partial<BasicTableProps>) {
  console.log(props, 'props')
  innerPropsRef.value = { ...unref(innerPropsRef), ...props }
  // 后置合并column
  mergeColumnWidth(innerPropsRef.value.columns)
  if (props.data) {
    // 处理表格数据更新重新加载
    const $table = tableRef.value
    if ($table) {
      $table.loadData(props.data)
    }
  }

  // 设置固定列
  nextTick(
    () =>
      !config.layout.shrink &&
      fixedArr.value?.length &&
      setColumnFixed(fixedArr.value)
  )
  props?.total && (total.value = props?.total)
}
// 设置请求参数
function setSearchInfo (data: Recordable) {
  searchInfo.value = { ...data }
}
function getSearchInfo () {
  return JSON.parse(JSON.stringify(unref(getProps).searchInfo))
}
// 获取已选中项
function getSectionData (): Recordable[] {
  return JSON.parse(JSON.stringify(unref(sectionList)))
}

// 获取已选中项ID
function getSectionDataIds (): Recordable[] {
  const ids: any[] = []
  sectionList.value.forEach((item) => {
    ids.push(item.id)
  })
  return ids
}

// 清空所有选中
function clearSelection () {
  const $table = tableRef.value
  if ($table) {
    $table.clearCheckboxRow()
    $table.clearCheckboxReserve()
    selectionChange([])
    emit('selectionChange')
    initBatchData({
      startLimit: 1,
      endLimit: 10000
    })
  }
}
// 获取表格数据
function getTableData (): TableData {
  return {
    total: unref(total),
    list: JSON.parse(JSON.stringify(unref(tableData))),
    extraMap: toRaw(unref(extraMap))
  }
}

// 复选框表头全选
function toggleAllCheckboxEvent () {
  const $table = tableRef.value
  console.log($table, '$table')
  if ($table) {
    $table.toggleAllCheckboxRow()
    setSelectionData()
  }
}

// 复选框选中
function toggleCheckboxEvent (row: Recordable) {
  const $table = tableRef.value
  if ($table) {
    $table.toggleCheckboxRow(row)
    setSelectionData()
  }
}

function setSelectionData () {
  const $table = tableRef.value
  const records = $table.getCheckboxRecords()
  const reserveRecords = $table.getCheckboxReserveRecords()
  selectionChange(records.concat(reserveRecords))
  emit('selectionChange')
}

function setSortConfig (data: Recordable) {
  sortConfig.value = { ...sortConfig.value, ...data }
  console.log(sortConfig, 'sortConfig123')
}

function getPaginationData () {
  return {
    pageNum: getPaginationInfo.value.pageNum,
    pageSize:
      Number(paginationRes?.value?.value) || getPaginationInfo.value.pageSize
  }
}

const tableAction: TableActionType = {
  setProps,
  setSearchInfo,
  reload,
  getSectionData,
  getTableData,
  clearSelection,
  getSearchInfo,
  getSectionDataIds,
  setSortConfig,
  getPaginationData,
  setColumnFixed
}
emit('register', tableAction)

async function handleSizeChangeFn (val: number) {
  await handleSizeChange(val)
  emit('changePage', { key: 'size', val })
  reload()
}
function handleCurrentChangeFn (val: number) {
  handleCurrentChange(val)
  emit('changePage', { key: 'currentPage', val })
  reload()
}
function dealShowVal (val: any) {
  if (isNullOrUndefOrEmpty(val)) {
    return '-'
  }
  return val
}

// 批量选择确定获取ID
async function tableBatchSelectSure (batchData: BatchData) {
  const { pageSize, pageNum } = getPaginationInfo.value
  await getBatchSelectIds({
    ...getProps.value.searchInfo,
    ...batchData,
    pageSize,
    pageNum
  })
  // 定位到起始页
  const batchSelectPageInfo = getBatchSelectPageInfo(
    batchData,
    getPaginationInfo.value.pageSize
  )
  if (
    getPaginationInfo.value.pageNum !== batchSelectPageInfo.tableBatchStartPage
  ) {
    handleCurrentChangeFn(batchSelectPageInfo.tableBatchStartPage)
  }
  const records: Recordable<any>[] = []
  getBatchSelectIdsList().map((item: any) => {
    records.push({
      id: item
    })
  })
  console.log(records, 'recordsrecords')
  // // 处理表格数据选中
  batchTableSelectCheck(records)
}

// 处理批量筛选表格选中
function batchTableSelectCheck (row: Recordable) {
  const $table = tableRef.value
  if ($table) {
    $table.clearCheckboxRow()
    selectionChange([])
    $table.setCheckboxRow(row, true)
    selectionChange(row)
    emit('selectionChange')
  }
}

// 列配置项弹窗事件派发
function columnLoadedFunc (
  data: ColumnType[],
  otherActionObj: ColumnSettingEmitType
) {
  fixedArr.value = otherActionObj.fixedItemArr || []
  customColorArr.value = otherActionObj.customColorArr || []
  emit('columnLoaded', data, otherActionObj)
}

// 设置固定列(支持分组表头)
function setColumnFixed (fixedItemArr: Recordable[], key = 'key') {
  fixedItemArr.forEach((item) => {
    nextTick(() => tableRef.value?.setColumnFixed(item[key], item.fixed))
  })
}
// 列样式
function cellStyle ({ column, ...rest }: Recordable, key: string) {
  let style = {}
  if (unref(customColorArr).includes(column.field)) {
    style = {
      color: '#F44D4D'
    }
  }
  // 合并外部传入的 cell-style 样式
  const cellStyleFromAttrs = attrs[key]
  if (isObj(cellStyleFromAttrs)) {
    Object.assign(style, cellStyleFromAttrs)
  } else if (isFunc(cellStyleFromAttrs)) {
    const result = cellStyleFromAttrs({ column, ...rest }) || {}
    Object.assign(style, result)
  }
  return style
}
defineExpose({ batchTableSelectCheck, tableRef })
</script>
<style lang="scss" scoped>
.group-btnBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  row-gap: 16px;
}

.custom-table {
  :deep(.vxe-table--render-default) {
    .is--disabled.vxe-cell--checkbox {
      .vxe-checkbox--icon {
        background-color: #edf2fc;
      }
    }
  }
}

.isShowSetting {
  height: calc(100% - 48px);
}

.header-content {
  @include flex-center(row, normal);
  .title-text {
    font-size: 14px;
    font-weight: 700;
    font-style: normal;
    display: inline-block;
    max-width: calc(100% - 16px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 22px;
  }
  .desc-icon {
    font-size: 12px;
    color: #999999;
    flex-shrink: 0;
    margin-left: 8px;
    cursor: pointer;
    margin-top: 6px;
  }
}
.operate {
  display: flex;
  position: relative;
  .setting-btn {
    position: absolute;
    right: -10px;
    top: -9px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-left: 1px solid #ebeef5;
    cursor: pointer;
    .icon {
      font-size: 16px;
      color: #666666;
    }
  }
}

.batchSelect {
  @include flex-center(row, space-between, center);
}
:deep(.vxe-footer--row) {
  background-color: #f5f7fa;
}

:deep(.vxe-footer--column) {
  .vxe-cell {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
</style>
