<template>
  <el-switch v-model="open" :disabled="disabled" style="--el-switch-on-color: #5687FF; --el-switch-off-color: #DCDFE6" />
</template>

<script lang="ts" setup>
import { computed, watch } from 'vue'
const props = defineProps({
  modalValue: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modalValue'])
const open = computed({
  get () {
    return props.modalValue
  },
  set (value) {
    emit('update:modalValue', value)
  }
})
watch(() => open.value, (e) => {
  console.log(e, '1111')
  
})
</script>