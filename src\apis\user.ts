import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

// 登录接口
export const loginApi = (data?: Recordable) =>
  request.post('/admin/oauth/token', data, jsonHeaderConfig)
// 登录接口
export const getCodeApi = (
  data?: Recordable,
  params?: Recordable,
  config?: any
) => {
  params = { tenantId: 0, ...params }
  return request.post('/admin/oauth/sms', { data, params }, jsonHeaderConfig, config)
}

// 获取用户信息
export const getUserInfo = () => request.get('/admin/user/current', null)

// 获取当前用户权限菜单
export const getAuthMenuApi = (data?: Recordable) =>
  request.get('/admin/menu/current/tree', data, jsonHeaderConfig)

// 获取用户列表
export const getUserList = (data?: Recordable) =>
  request.post('/admin/user/page', data, jsonHeaderConfig)

// 获取树形部门列表
export const getDepartmentTree = (data?: Recordable) =>
  request.post('/admin/department/tree', data)

// 登出
export const logoutApi = () => request.post('/admin/oauth/logout', null)

// * 获取用户列表(不走权限，全部返回)
export const getUserListNoAuth = (params: any) => {
  return request.post('/admin/user/loadUser', params, jsonHeaderConfig)
}

// 修改密码
export const updatePassword = (data?: Recordable) =>
  request.post('/admin/user/profile/password', data, jsonHeaderConfig)

// 获取安全密码
export const getSafetyCode = () => {
  return request.get('/admin/user/getSafetyCode', null)
}
// 设置安全密码
export const setSafetyCode = (data: any) => {
  return request.post('/admin/user/security', data)
}
// 安全密码登录
export const loginSafetyCode = (data: {
  username: string
  safetyCode: string
}) => {
  // 需要转义 get请求中 如( #）的字符串作为参数传递，需要对这些特殊字符进行 URL 编码，以确保不会被错误解析
  return request.post(
    `/admin/oauth/token/security?username=${encodeURIComponent(data.username)}&safetyCode=${encodeURIComponent(data.safetyCode)}`,
    null
  )
}
