<template>
  <div class="dynamicParameters">
    <div class="dynamicParameters-item">
      <el-checkbox v-model="dynamicParameters"
                   @change="change"
                   :true-label="1"
                   :false-label="0">当前新增/修改数据的用户所属的部门</el-checkbox>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, unref, type PropType } from 'vue'

const emit = defineEmits(['update:dynamicParameters', 'updateParameter'])
const props = defineProps({
  data: {
    type: Number as PropType<Recordable>,
    default: () => {}
  }
})
const dynamicParameters = computed({
  get: () => unref(props.data),
  set: (newValue) => {
    console.log(111)
    emit('update:dynamicParameters', newValue )
  }
})

function change (val: any) {
  emit('updateParameter', val)
}

</script>

<style lang="scss" scoped>
.dynamicParameters {
  height: 100%;
  padding: 16px 0 0 16px;
  &-item {
    display: block;
    width: 100%;
  }
}
</style>