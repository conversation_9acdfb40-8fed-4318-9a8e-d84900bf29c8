<template>
  <div class="organizational">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索部门"
        clearable
        :prefix-icon="Search"/>
      <div class="treeList" :style="{height: leaveUserList.length > 0 ? 'calc(100% - 72px)' : 'calc(100% - 28px)'}">
        <el-tree
          ref="treeRef"
          :data="treeData"
          node-key="id"
          :filter-node-method="filterNode"
          :check-strictly="true"
          :expand-on-click-node="false"
          :props="defaultProps"
          @node-expand="nodeToggle"
          @node-collapse="nodeToggle"
          @node-click="nodeClick">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="custom-tree-node-item" :class="data.id === treeData?.[0].id ? 'treetop' : ''">
                <div class="custom-tree-node-item-block">
                  <span @click.stop>
                    <el-checkbox
                      v-model="data.checked"
                      :disabled="!data.len"
                      :indeterminate="data.indeterminate"
                      style="margin-right: 8px;"
                      @change="(val:boolean) => changeDepartCheck(val, data)"
                    />
                  </span>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="`${data.name}(${data.len || 0})`"
                    placement="top-start"
                  >
                    <span>{{data.name}}({{data.len || 0}})</span>
                  </el-tooltip>
                </div>
                <!-- 只在第一层展示 -->
                <span class="treeoperate" v-if="data.id === treeData?.[0].id" @click.stop="handleToggle">
                  {{treeStatue ? '收起' : '展开'}}子部门
                </span>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      <!-- 离职员工 -->
      <div class="leaveUser">
        <div>
          <i class="iconfont icon-warning"/>
          <span>离职员工（{{ leaveUserList.length }}人）</span>
        </div>
        <div class="showLeaveUser" @click="setLeaveUser">{{ !leaveUserStatus ? '显示' : '隐藏' }}</div>
      </div>
    </div>
    <vxe-list height="350"
              :data="userList"
              :scroll-y="{enabled: true}"
              class="center">
      <template #default="{ items }">
        <div
          class="childNodeItem"
          v-for="item in items"
          :key="item.id">
          <el-checkbox
            :disabled="item?.job === 2"
            v-model="item.checked"
            @change="changeUserCheck(item)"
          >
            {{ item.realName }}<template v-if="item?.job === 2">（离职）</template>
          </el-checkbox>
        </div>
      </template>
    </vxe-list>
    <div class="right">
      <div class="top">
        <span>已选择{{ checkUserList.length }}项</span>
        <span class="clear" @click="clearAll">清空</span>
      </div>
      <div
        class="checkUserList"
        ref="scrollEl">
        <div class="checkUserList-item" v-for="item in showUserList" :key="item.id">
          <span>{{ item.realName }}</span>
          <i v-if="!item?.disable" class="iconfont icon-close" @click="deleteCheckedUser(item)"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useInfiniteScroll } from '@vueuse/core'
import { Search } from '@element-plus/icons-vue'
import { ref, watch, unref, computed, type PropType } from 'vue'
import type { ElTree } from 'element-plus'
import { cloneDeep, recursion } from '@/utils/tools'
import type { DeComponentTreeVO, UserVOList } from '../type'
import { isNullOrUndefOrEmpty } from '@/utils/is'
import { VxeList } from 'vxe-table'
const emit = defineEmits(['update:data', 'updateValue'])
const treeRef = ref<InstanceType<typeof ElTree>>()
const count = ref<number>(10)
const treeStatue = ref<boolean>(false) // 组织架构展开收起状态，默认收起
const userList = ref<UserVOList[]>([])
const checkUserList = ref<UserVOList[]>([])
const leaveUserStatus = ref<boolean>(false) // 显示隐藏离职员工
const currentNodeData = ref<Recordable>({})
const personnelSelectionInput = ref('')
const isIncludeChildValue = ref<boolean>(false)
const scrollEl = ref<HTMLElement>(null)
const defaultProps = {
  disabled: (data: { number: number }) => {
    return !data.number
  }
}

const props = defineProps({
  data: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  },
  userResignList: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  },
  checkedUser: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  },
  orginComponentData: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  }
})
watch(() => props.checkedUser, () => {
  updateCheck(props.checkedUser)
}, {
  immediate: true
})
useInfiniteScroll(
  scrollEl,
  () => {
    // load more
    // data.value.push(...moreData)
    if ( showUserList.value.length >= checkUserList.value.length) return
    count.value += 30
  },
  { distance: 10 }
)
const treeData = computed(() => {
  return unref(props.data)
})
const showUserList = computed(() => {
  return checkUserList.value.slice(0, count.value)
})

const leaveUserList = computed(() => {
  return props.userResignList || []
})

// 节点点击
async function nodeClick (currentNode: Recordable<any>) {

  if(currentNode.len === 0) {
    return
  }
  currentNodeData.value = currentNode
  userList.value = await handleUserList(currentNode.userVOList)
}

/**
 * 复选框点击
 */
async function changeDepartCheck (val: boolean, data: Recordable) {
  currentNodeData.value = data
  userList.value = await handleUserList(data.userVOList)
  const leaveUserMap = new Map(leaveUserList.value.map(user => [user.id, user]))
  const checkUserMap = new Map(checkUserList.value.map(user => [user.id, user]))
  userList.value.forEach((user: UserVOList) => {
    if (user?.job === 1 && !leaveUserMap.has(user.id)) {
      user.checked = val
      if (val) {
        checkUserMap.set(user.id, user)
      } else {
        checkUserMap.delete(user.id)
      }
    }
  })
  checkUserList.value = Array.from(checkUserMap.values())
  emit('updateValue', checkUserList.value)
  checkDepartList()
}

/**
 * 处理树节点选中
 */
function checkDepartList () {
  recursion(treeData.value, item => {
    const checkedUserMap = new Map(checkUserList.value.map(user => [user.id, user]))

    item.userVOList.forEach((user: Recordable) => {
      user.checked = Boolean(checkedUserMap.get(user.id))
    })

    const filteredUsers = item.userVOList.filter((user: { job: number }) => user?.job === 1)
    const allChecked = filteredUsers.every((user: { checked: any }) => user.checked)

    item.checked = allChecked
    item.indeterminate = !allChecked && filteredUsers.some((user: { checked: any }) => user.checked)
  })
}

/**
 * 处理右侧用户列表回显
 */
async function handleUserList (userList: UserVOList[]) {

  console.time('节点点击')
  const data = JSON.parse(JSON.stringify(userList))
  // 是否展示离职员工
  if(leaveUserStatus.value) {
    data?.push(...leaveUserList.value)
  }
  // 处理回显
  const checkUserListClone = [...checkUserList.value]
  // 如果checkUserListClone data中的id就将data的对应项选中
  const checkUserMap = new Map(checkUserListClone.map(user => [user.id, true]))

  data?.forEach((item: Recordable) => {
    item.checked = checkUserMap.has(item.id)
  })
  checkUserList.value = checkUserListClone

  console.timeEnd('节点点击')
  return data
}

/**
 * 显示隐藏离职员工
 */
function setLeaveUser () {
  if(leaveUserStatus.value) {
    userList.value = userList.value?.filter(item => item?.job === 1)
  } else {
    userList.value?.push(...leaveUserList.value)
  }
  leaveUserStatus.value = !leaveUserStatus.value
}

/**
 * 展开收起全部
 */
function handleToggle () {
  treeStatue.value = !treeStatue.value

  let nodesData = treeRef.value?.store.nodesMap
  for (let i in nodesData) {
    if(nodesData[i].childNodes && nodesData[i].childNodes.length > 0) {
      nodesData[i].expanded = treeStatue.value
    }
  }
}

function nodeToggle (data: any, currentNode: any) {
  setTimeout(() => {
    const nodesData = treeRef.value?.store.nodesMap
    let expandedVal = false
    for (let i in nodesData) {
      if(nodesData[i].expanded) {
        expandedVal = true
      }
    }
    treeStatue.value = expandedVal
  }, 0)
}

const filterNode = (value: string, data: Recordable) => {

  if (!value) return true
  return data?.name.includes(value)
}

watch(personnelSelectionInput, (val) => {
  treeRef.value!.filter(val)
})

/**
 * 中间用户选择
 */
function changeUserCheck (itemData:any){
  const index = checkUserList.value.findIndex((select: { id: string }) => select.id === itemData.id)
  if(index !== -1) {
    checkUserList.value?.splice(index, 1)
    // handelTreeNodeCheck(itemData, 1)
  } else {
    checkUserList.value.push(itemData)

  }
  console.log('%c [ checkUserList.value ]-304', 'font-size:13px; background:#71aa9f; color:#b5eee3;', checkUserList.value)

  emit('updateValue', checkUserList.value)
  checkDepartList()
}

/**
 * 删除已选中用户
 */
function deleteCheckedUser (itemData:any) {
  checkUserList.value = checkUserList.value.filter((item) => item.id !== itemData.id)
  const foundUser = userList.value.find(item => item.id === itemData.id)
  if (foundUser) {
    foundUser.checked = false
  }
  emit('updateValue', checkUserList.value)
  checkDepartList()
}

/**
 * 清空选中用户
 */
function clearAll () {
  // 只能清空diable为false
  checkUserList.value = checkUserList.value.filter(item => item.disable)
  recursion(treeData.value, item => {
    item.checked = false
  })
  recursion(userList.value, item => {
    item.checked = false
  })
  emit('updateValue', checkUserList.value)
}

// 父部门不包含子部门
async function handleIncludeChild (val:boolean){

  isIncludeChildValue.value = val
  userList.value = []
  checkDepartList()
}

/**
 * 选中可见选项
 */
async function handleAllCheck (val:boolean){

  if(!val) {
    // 清空
    clearAll()
  }
  const nodesMap = treeRef.value?.store.nodesMap
  if(val && isIncludeChildValue.value) {
    // 选中可见且不包含子部门
    const topNodes = Object.values(nodesMap as Recordable).filter(i => i.level === 1)

    const getExpandNode = (topNode: Recordable[]): Recordable[] => {
      if (!topNode.length) return []
      let nodes:Recordable[] = []
      nodes = topNode.reduce((pre: Recordable[], nex: Recordable) => {
        let expandNodes: Recordable[] = []
        if (nex.expanded) {
          expandNodes = getExpandNode(nex.childNodes)
        }
        return [...pre, nex.data, ...expandNodes]
      }, [])
      return nodes
    }
    const nodes = getExpandNode(topNodes)
    const disabledArr:UserVOList[] = checkUserList.value.filter(item => item.disable)
    const arr:UserVOList[] = []
    nodes.forEach(item => {
      disabledArr.push(...item.userVOList || [])
      arr.push(...item.userVOList || [])
    })
    checkUserList.value = cloneDeep(disabledArr)
    userList.value = await handleUserList(arr)
    emit('updateValue', checkUserList.value)
    checkDepartList()
  }
  if(val && !isIncludeChildValue.value) {
    // 选中可见且包含子部门
    const nodes: DeComponentTreeVO[] = Object.values(nodesMap as Recordable).filter(i => i.level === 1).map(item => item.data)

    const disabledArr:UserVOList[] = checkUserList.value.filter(item => item.disable)
    const arr:UserVOList[] = []
    nodes.forEach((item: DeComponentTreeVO) => {
      arr.push(...item.userVOList || [])
      disabledArr.push(...item.userVOList || [])
    })
    checkUserList.value = cloneDeep(disabledArr)
    userList.value = await handleUserList(arr)
    emit('updateValue', checkUserList.value)
    checkDepartList()
  }
}

function clear (){
  currentNodeData.value = {}
  userList.value = []
}

async function updateCheck (checkedUser: UserVOList[]) {
  checkUserList.value = cloneDeep(checkedUser || [])
  checkDepartList()
  if(!isNullOrUndefOrEmpty(currentNodeData.value) && userList.value?.length > 0) {
    userList.value = await handleUserList(currentNodeData.value.userVOList)
  }
}

defineExpose({
  handleIncludeChild,
  handleAllCheck,
  clear,
  updateCheck
})

</script>

<style lang="scss" scoped>
.organizational {
  @include flex-center(row, normal, normal);
  height: 100%;
  .left {
    width: 250px;
    border-right: 1px solid #EBEEF5;
    padding: 16px 16px 16px 0;
    .custom-tree-node {
      line-height: 12px;
      // display: inline-block;
      // min-width: 100%;
      &-item {
        .treelabel {
          width: 100%;
          :deep(.custom-tooltip-content) {
            text-align: left !important;
          }
        }
        .treeoperate {
          font-size: $font-size-mini;
          color: $primary-color;
          margin-right: 16px;
        }
        &-block {
          @include flex-center(row, space-between, center);
        }
      }
      .treetop {
        @include flex-center(row, space-between, center);
        width: 180px;
      }
    }
    .treeList {
      overflow-y: auto;
      height: calc(100% - 32px);
      :deep(.el-tree-node.is-expanded>.el-tree-node__children) {
        display: block;
        min-width: 100%;
      }
    }
  }
  .leaveUser {
    @include flex-center(row, space-between, center);
    border-radius: 2px;
    background: #EEF3FF;
    padding: 5px 12px;
    font-size: $font-size-small;
    color: #303133;
    .icon-warning {
      color: $primary-color;
      margin-right: 8px;
    }
    .showLeaveUser {
      cursor: pointer;
      color: $primary-color;
    }
  }
  .center {
    width: 200px;
    border-right: 1px solid #EBEEF5;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    :deep(.vxe-list--virtual-wrapper) {
      padding: 0px 16px;
    }
  }
  .right {
    flex: 1;
    padding: 16px 0 16px 16px;
    overflow-y: auto;
    font-size: $font-size-mini;
    color: $main-text-color;
    .top {
      @include flex-center(row, space-between, center);
      margin-bottom: 12px;
      .clear {
        color: $primary-color;
        cursor: pointer;
      }
    }
    .checkUserList {
      height: calc(100% - 30px);
      overflow-y: auto;
      &-item {
        display: inline-block;
        padding: 2px 6px 2px 10px;
        border-radius: 2px;
        background: #EEF3FF;
        margin-right: 8px;
        margin-bottom: 8px;
        .icon-close {
          cursor: pointer;
          font-size: $font-size-mini;
          margin-left: 8px;
        }
      }
    }
  }
  .personnelSelectionInput {
    border-radius: 4px;
    :deep(.el-input__wrapper) {
      box-shadow: none;
      background: #F4F4F5;
    }
    margin-bottom: 13px;
  }
}
</style>
