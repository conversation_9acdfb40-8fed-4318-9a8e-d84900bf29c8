import { isEmptyObject } from '@/utils/is'
import { saveColumnWidthApi, getColumnWidthApi } from '@/apis/common'
import type { BasicTableProps, ResizeBasicColumnType, ResizeColumnType } from '../type'
import { onBeforeUnmount, ref, type Ref } from 'vue'
// 列请求对象
/**
 * 
 * @param props {Ref<BasicTableProps>} 不使用tableCode的场景(1.一个页面只有一个表格 2. 一个页面有多个表格 但多个表格filed字段没有重叠)
 * @param loading  {Ref<boolean>} 传递loading状态
 * @returns   
 * @example
 * const {resizableChange, getColumnWidth, mergeColumnWidth} = useResizeColumn(props, loading)
 */
export const useResizeColumn = (props: Ref<BasicTableProps>, _loading: Ref<boolean>) => {
  const mapOfField: Ref<Recordable<number>> = ref({})
  // 列宽变化
  async function resizableChange (scope: Recordable<any>, _callback?: (columnMap?:Recordable<number>) => void) {
    // loading.value = true
    try {
      if(!props.value.customTableConfig) return
      const { column, resizeWidth } = scope
      // 传递服务器保存配置
      await saveColumnWidthApi({ columnKey: column.field, value: resizeWidth, businessKey: props.value?.tableCode })
      mapOfField.value[column.field || column.key] = resizeWidth
      // loading.value = false
    } catch (error) {
      // 如果请求失败，则恢复到之前保存的列
      // callback && callback()
      // loading.value = false
    }
  }
  // 合并列宽
  async function mergeColumnWidth (targetColumns?: ResizeBasicColumnType[] | ResizeColumnType[]) {
    if (!targetColumns || targetColumns.length === 0) return
  
    if (isEmptyObject(mapOfField.value)) {
      await getColumnWidth()
    }
  
    // 如果没有保存过列宽，则不合并
    if (isEmptyObject(mapOfField.value)) return
  
    function traverseColumns (columns: (ResizeBasicColumnType | ResizeColumnType)[]): void {
      columns.forEach((item) => {
        if (mapOfField.value[item.field || item.key]) {
          item.width = mapOfField.value[item.field || item.key]
          // 回退接口异常处理
          item.resizeWidth = 0
        }
  
        if (item.children && item.children.length > 0) {
          traverseColumns(item.children)
        }
      })
    }
  
    traverseColumns(targetColumns)
  }
  async function getColumnWidth () {
    if(!props.value.customTableConfig) return
    const res = await getColumnWidthApi({ businessKey: props.value?.tableCode })
    res?.forEach?.((item: any) => {
      mapOfField.value[item.columnKey] = item.value
    })
    return res
  }
  function getFieldWidthValue (key: string) {
  // 获取指定列宽字段对应的width
    return mapOfField.value[key]
  }
  onBeforeUnmount(() => {
    mapOfField.value = {}
  })
  return {
    resizableChange,
    getColumnWidth,
    mergeColumnWidth,
    getFieldWidthValue
  }
}
