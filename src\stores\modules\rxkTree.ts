import { defineStore } from 'pinia'

interface IRxkTreeStore {
  expands: { [key in string]: string[] }
}

export const useRxkTreeStore = defineStore({
  id: 'RxkTreeStore',
  state: (): IRxkTreeStore => ({
    expands: {}
  }),
  actions: {
    getExpands (key: string) {
      return this.expands[key]
    },
    setExpands (key: string, value: string | string[]) {
      const valueArr = [value].flat()
      const oldArr = this.getExpands(key)
      if (oldArr) {
        this.expands[key] = [...new Set([...oldArr, ...valueArr])]
      } else {
        this.expands[key] = [value].flat()
      }
    },
    removeExpands (key: string) {
      delete this.expands[key]
    }
  },
  persist: true
})