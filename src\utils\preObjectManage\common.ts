import type { BaseDataType, ComponentType } from '@/types/preObjectManage'
import { getFieldBaseDataFn } from '@/utils/preObjectManage/utils'
import type { OptionType } from '@/types/preObjectManage'
import { GenNonDuplicateID } from '@/utils/tools'

export class FieldValidateResult {
  public fieldBaseData: BaseDataType
  public msg: string
  public success: boolean
  constructor (data: BaseDataType) {
    this.fieldBaseData = data
    this.msg = ''
    this.success = true
  }
}

// 校验名称必填
// export const validateName = (result:ValidNameType) => {
//   const fieldName = result.fieldBaseData.fieldName
//   if (isNullOrUndefOrEmpty(fieldName)) {
//     result.msg = '字段名称不能为空'
//     result.success = false
//   } else {
//     result.msg = ''
//     result.success = true
//   }
//   return result
// }

export default class BaseData {
  public tId: string // 前端自定义唯一标识
  public fieldTypeName: string // 前端自定义字段名称
  public fieldBaseData: ComponentType
  public fieldTypeOwnTableType: number
  public isTable: boolean
  public moduleSerial: string
  constructor (data: ComponentType, options: OptionType, fieldTypeName: string) {
    this.tId = GenNonDuplicateID(3)
    this.fieldBaseData = getFieldBaseDataFn(data)
    // 字段属于哪个类型下 (自定义,预定义), 分栏,子表
    this.fieldTypeOwnTableType = options.tableColumnType || 0
    this.fieldTypeName = fieldTypeName
    this.isTable = options.isTable || false
    this.moduleSerial = options.moduleSerial || ''
  }

  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

export function getOperate (code: string, data: Recordable, options: Recordable, value: boolean) {
  return {
    code,
    componentId: 0,
    defineType: 20,
    moduleId: data.moduleId || 0,
    moduleType: options.tableColumnType || 0,
    tableCode: data.tableCode || 0,
    value
  }
}
