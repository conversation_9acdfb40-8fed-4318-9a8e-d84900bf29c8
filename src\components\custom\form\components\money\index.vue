<template>
  <div class="isTxt" v-if="isTxt">
    {{innerValue || '-'}}
  </div>
  <RxkInput
    v-else
    style="width: 100%;"
    v-model="innerValue"
    type="text"
    :disabled="renderConfig.disabled"
    :placeholder="renderConfig.placeholder"
    @handleInput="handleInput"
  >
    <template #suffix>
      <span>{{data.unit}}</span>
    </template>
  </RxkInput>
  <div>
    {{  innerValue ? numToCapital(String(reset(innerValue))) : '' }}
  </div>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkInput } from '@/components/common/RxkInput'
import { numToCapital } from '@/utils/tools'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})

function handleInput (value: any) {
  let num = reset(value)
  // 处理千分位
  innerValue.value = numFormat(num)
  console.log(innerValue.value, 'innerValue.value')
}
function reset (value: number) {
  return parseFloat(String(value).replace(/,/g, ''))// 如果是小数用parseFloat
}
function numFormat (num: number){
  return num.toString().replace(/\d+/, function (n) { // 先提取整数部分
    return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
      return $1 + ','
    })
  })
}
</script>