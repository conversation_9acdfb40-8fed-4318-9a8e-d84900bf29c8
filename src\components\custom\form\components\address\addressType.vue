<template>
  <div class="area-linkage-container">
    <RxkSelect
      placeholder="选择省份"
      v-model="localValue.province"
      :list="provinceList"
      @change="changeProvince"/>
    <RxkSelect
      v-if="showCity"
      placeholder="选择城市"
      v-model="localValue.city"
      :list="transToArr(pcaa[localValue.province])"
      @change="changeCity"/>
    <RxkSelect
      v-if="showArea"
      placeholder="选择区/县"
      v-model="localValue.area"
      :list="transToArr(pcaa[localValue.city])"
      @change="changeArea"/>
  </div>
</template>

<script lang="ts" setup>
import pcaa from './area-data.json'
import { RxkSelect } from '@/components/common/RxkSelect'
import { computed, ref, watchEffect } from 'vue'
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  showCity: { // 展示市
    type: Boolean,
    default: true
  },
  showArea: { // 展示区
    type: Boolean,
    default: true
  }
})
const emits = defineEmits(['update:modelValue'])
// 因为传入的props是只读，所以需要通过ref转化为响应式对象，并绑定到页面
const localValue = computed(() => {
  return props.modelValue as Recordable
})
const updateValue = (newValue:Recordable) => {
  console.log(newValue, 'newValue123')
  emits('update:modelValue', newValue)
}

watchEffect(() => {
  // 监听 localValue 的变化，一旦发生变化，更新父组件的 modelValue
  updateValue(localValue.value)
})

function transToArr (obj: Recordable){
  const res = []
  for (const i in obj) {
    res.push({ label: obj[i], value: i })
  }
  console.log(res, 'res111')
  return res
}

// 省
const provinceList = computed(() => {
  return transToArr(pcaa['86'])
})
function changeProvince (val: string) {
  localValue.value.city = ''
  localValue.value.area = ''
  const data = pcaa['86']
  const pKey = val as keyof typeof data
  localValue.value.pName = data[pKey] || ''
  localValue.value.cName = ''
}
function changeCity (val: string) {
  localValue.value.area = ''
  const data = pcaa['86']
  const pKey = localValue.value.province as keyof typeof data
  const pData = pcaa[pKey]
  localValue.value.cName = pData ? pData[val as keyof typeof pData] : ''
}
function changeArea (val: string) {
  localValue.value.area = val
  const data = pcaa['86']
  const cKey = localValue.value.city as keyof typeof data
  const cData = pcaa[cKey]
  localValue.value.aName = cData ? cData[val as keyof typeof cData] : ''
}

</script>

<style lang="scss" scoped>
.area-linkage-container {
  width: 100%;
  display: flex;
  .select {
    flex: 1;
    margin-right: 15px;
    &:last-of-type {
      margin-right: 0;
    }
  }
}
</style>
