import type { App, Component } from 'vue'
import { RxkButton } from '@/components/common/RxkButton'
import RouterViewCache from '@/components/custom/RouterViewCache/index.vue'
import { registerGlobalFormComp } from './custom/form/components/registerFormComponent'
const components: {
  [propName: string]: Component;
} = {
  RxkButton,
  RouterViewCache
}
/**
 * @author: 张胜
 * @desc：注册全局组件
 * */
export default {
  install: (Vue: App) => {
    registerGlobalFormComp(Vue)
    for (const key in components) {
      Vue.component(key, components[key])
    }
  }
}