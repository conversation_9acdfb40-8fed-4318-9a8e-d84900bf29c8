<template>
  <RxkDialog
    v-model:modelValue="show"
    :showFooter="false"
    title="单日明细"
    width="1200"
    class="customTransfer"
  >
    <SearchFilter 
      @register="registerSetting"
      @search="search"
      class="search-box"
    />

    <div class="btn-box">
      <RxkButton @click="handleDown">导出</RxkButton>
    </div>
                  
    <div class="table_box tw-p-t-[16px]">
      <RxkVTable 
        class="custorm-hasTable-footer"
        max-height="400px"
        :defalutLoading="true"
        @register="registerTable"
        ref="dataTableRef"
        show-footer
        showHeaderSummary
        :footer-method="footerMethod"
      />
    </div>
  </RxkDialog>
</template>

<script setup lang="ts">
import { RxkDialog } from '@/components/common/RxkDialog'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { unref, computed, onMounted, ref } from 'vue'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { channelDayStatistics } from '../../data'
import { excelExport } from '@/utils/index'
import { fetchChannelStatisticsListExport, fetchGetChannelStatisDayDetailList, fetchGetDataStatisticsTotal } from '@/apis/carlife'
import timeUtils from '@/utils/libs/time'
import type { ColumnType } from '@/types/table'
import { settlementWayOption } from '@/enums/carlife'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  channelId: {
    type: String,
    default: ''
  }
})
const sumTableData = ref<Recordable>({})

const dataTableRef = ref()
const emits = defineEmits(['update:visible'])
const show = computed({
  get: () => props.visible,
  set: (visible: boolean) => emits('update:visible', visible)
})

// 搜索表单数据
const [registerSetting] = useSearch({
  schemas: unref(channelDayStatistics.searchFormData)
})

const [transStartTime, transEndTime] = timeUtils.transTypeToTime(60, 'yyyy-MM-dd')
const searchInfo:Record<string, any> = {
  model: {
    dayStart: transStartTime,
    dayEnd: transEndTime,
    channelId: props.channelId,
    isChannelGroup: true,
    isDayGroup: true
  }
}

const [registerTable, { reload, setSearchInfo, getSearchInfo, getPaginationData }] = useTable({
  api: fetchGetChannelStatisDayDetailList,
  columns: unref(channelDayStatistics.columns),
  searchInfo: searchInfo,
  immediate: true // 是否立刻请求
})

const search = (val:{ [k: string]: any }) => {
  Object.assign(searchInfo.model, val)

  searchInfo.model.dayStart = val.timeRange?.[0] ?? ''
  searchInfo.model.dayEnd = val.timeRange?.[1] ?? ''
  delete searchInfo.model.timeRange

  setSearchInfo(searchInfo)
  getSummaryData()
  reload()
}
const handleDown = () => {
  const params = { ...getSearchInfo(), ...getPaginationData() }
  excelExport(fetchChannelStatisticsListExport, params, 'POST')
}

function getSummaryData () {
  fetchGetDataStatisticsTotal(searchInfo).then((res) => {
    sumTableData.value = res
    unref(unref(dataTableRef).tableRef)?.updateFooter?.()
  })
}
//  总计
function footerMethod ({ columns }: { columns: ColumnType[] }) {
  return [
    columns.map((column, columnIndex) => {
      if (columnIndex === 0) {
        return '总计'
      }
      
      if(column.field === 'settleType'){
        return settlementWayOption.find((item) => item.value === sumTableData.value.settleType)?.label
      }

      const arr = column.field?.split(',') || []
      return arr.map((item) => {
        return unref(sumTableData)[item] ?? ''
      })?.filter(i => i !== '').join(' | ')
    })
  ]
}

onMounted(() => {
  getSummaryData()
})
</script>

<style lang="scss" scoped>
.search-box {
  :deep(.custom-search-item){
    width: 35% !important;
  }
}
.btn-box {
  padding: 0 16px 4px;
  display: flex;
  justify-content: flex-end;
}

:deep(.operate) {
  > span {
    &:first-child {
      opacity: 0;
    }
    &.setting-btn {
      opacity: 1;
    }
  }
}
</style>
