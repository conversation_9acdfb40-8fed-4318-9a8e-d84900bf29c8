<template>
  <RxkDialog
    v-model:modelValue="show"
    title="配置示例"
    width="480"
    custom-class="api-config-dialog"
    @sure="submit"
    @close="close"
  >
    <el-form ref="formRef"
             :model="formData"
             :rules="rules"
             v-if="show"
             label-width="80px"
             class="api-config-form">
      <el-form-item label="撞库提醒" prop="supportFilterNotice">
        <RxkSelect v-model="formData.supportFilterNotice" :list="supportFilterNoticeEnum" />
      </el-form-item>
      <el-form-item label="对接类型" prop="dockingType">
        <RxkSelect v-model="formData.dockingType" :list="dockingTypeEnum" />
      </el-form-item>
      <el-form-item label="加密方式" prop="filterType">
        <RxkSelect v-model="formData.filterType" :list="filterTypeEnum" />
      </el-form-item>
      <el-form-item label="对接代码">
        <RxkInput
          v-model="formData.defaultConfig"
          placeholder="请输入"
          type="textarea"
          :autosize="{ minRows: 8, maxRows: 10 }"
        />
      </el-form-item>
    </el-form>
  </RxkDialog>
</template>

<script setup lang="ts">
import { ref, unref, watch, computed } from 'vue'
import { RxkDialog } from '@/components/common/RxkDialog/index'
import { RxkInput, RxkSelect } from '@/components/common'
import type { FormRules } from 'element-plus'
import { supportFilterNoticeEnum, dockingTypeEnum, filterTypeEnum } from '../data'
import { updateApiFieldReflectTemplateConfigDemo } from '@/apis/systemBasicConfig'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})
const emits = defineEmits(['update:visible', 'refresh'])
const show = computed({
  get () {
    return props.visible
  },
  set (val) {
    emits('update:visible', val)
  }
})
watch(() => show.value, (val) => {
  if (val) {
    formData.value.id = props.data.id
    formData.value.defaultConfig = props.data.defaultConfig
    formData.value.supportFilterNotice = props.data.supportFilterNotice
    formData.value.dockingType = props.data.dockingType
    formData.value.filterType = props.data.filterType
  }
})
const formData = ref<Recordable>({
  defaultConfig: '',
  supportFilterNotice: '',
  dockingType: '',
  filterType: ''
})
const rules = ref<FormRules>({
  supportFilterNotice: { required: true, message: '请选择撞库提醒', trigger: 'change' },
  dockingType: { required: true, message: '请选择对接类型', trigger: 'change' },
  filterType: { required: true, message: '请选择加密方式', trigger: 'change' }
})
const formRef = ref()
function close () {}
function submit () {
  if (!formRef.value) return
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      updateApiFieldReflectTemplateConfigDemo(unref(formData)).then(() => {
        ElMessage.success('操作成功')
        show.value = false
        emits('refresh')
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.api-config-form {
  padding-top: 16px;
  padding-left: 24px;
  padding-right: 24px;
}
</style>
<style lang="scss">
.api-config-dialog {
  .el-dialog__body {
    overflow-y: hidden;
    height: auto;
  }
  .footer {
    border-top: none;
  }
}
</style>
