<script lang="tsx">
import { h, PropType, toRaw } from 'vue'
import type { renderFn } from '@/types/common'
export default {
  name: 'table-header-render',
  props: {
    data: {
      // 框架自带的数据scope
      type: Object,
      default: () => ({})
    },
    columnConfig: {
      // 当前列的配置
      type: Object,
      default: () => ({})
    },
    renderFn: {
      type: Function as PropType<renderFn>,
      default: () => {}
    },
    rowIndex: {
      type: Number,
      default: 0
    }
  },
  setup (props: Recordable) {
    return () => {
      // return props.renderFn(toRaw(props.data), props.columnConfig,)
      return props.renderFn({
        cellData: toRaw(props.data),
        columnConfig: props.columnConfig,
        rowIndex: props.rowIndex,
        cellRefData: props.data
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
