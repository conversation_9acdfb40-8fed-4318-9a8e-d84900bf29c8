// 检验最大值,最小值, 及默认值
import type { ComponentType, OptionType } from '@/types/preObjectManage'
import BaseData, { getOperate } from '@/utils/preObjectManage/common'
import { fieldTypeConfig, fieldTypeEnum, operateListEnum } from '@/enums/preObjectManage'
import type { componentOperateListType } from '@/types/preObjectManage'
import { isNullOrUndefOrEmpty, isString } from '@/utils/is'

// 分栏
export class SubField extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '分栏'
    // 字段默认数据
    const defaultData = {
      ...data
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.subField
  }
  // @todo
  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 子表单
export class SubList extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '子表单'
    // 字段默认数据
    const defaultData = {
      ...data
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.subList
  }
  // @todo
  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// table标签页
export class SubTable extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = 'table标签页'
    // 字段默认数据
    const defaultData = {
      ...data
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.subTable
  }
  // @todo
  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 分割线
export class PartingLine extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '分割线'
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.partingLine,
        fieldType: data.columnInfo.fieldType || fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.partingLine
  }
  // @todo
  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 关联查询
export class AssociationQuery extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '关联查询'
    const columnOperateList = [
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: false
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: false
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: false
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: false
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      }
    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.associationQuery,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value))
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.associationQuery
  }
  // @todo
  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
// 关联表单
export class AssociationForm extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '关联表单'
    const layout = data.layout ? isString(data.layout) ? JSON.parse(data.layout) : data.layout : {}
    layout.proportion = 6
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        layout,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.associationForm
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.associationForm
  }
  // @todo
  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
// 按钮
export class Button extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '按钮'
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.button
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.button
  }
  // @todo
  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
// 签名
export class Sign extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '签名'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.SHOW,
        value: true
      }
    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.sign,
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.sign
  }
  // @todo
  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
