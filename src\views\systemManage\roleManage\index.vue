<template>
  <div class="main-user-manage">
    <SearchFilter @register="registerSetting" @search="search" />
    <div class="btn-box">
      <RxkButton @click="handleAdd" type="primary" v-if="getAuth('ROLE_ADD')">新增</RxkButton>
    </div>
    <div class="table-box">
      <RxkVTable @register="registerTable" @selectionChange="selectionChange" />
    </div>
  </div>
  <RxkDialog
    v-model="addRoleVisible"
    :title="isAdd?'新增角色':'编辑角色'"
    width="480px"
    @sure="addSure">
    <div class="add-edit">
      <el-form
        ref="formDataDom"
        label-position="top"
        require-asterisk-position="right"
        :model="formData"
        :rules="addRules">
        <el-form-item label="角色名称" prop="name">
          <RxkInput maxlength="10" placeholder="请输入角色名称" v-model="formData.name" />
        </el-form-item>
        <el-form-item label="角色描述" prop="remarks">
          <RxkInput rows="2"
                    maxlength="30"
                    type="textarea"
                    resize="none"
                    placeholder="请输入角色描述"
                    v-model="formData.remarks" />
        </el-form-item>
      </el-form>
    </div>
  </RxkDialog>
</template>

<script setup lang="tsx">
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import { RxkDialog } from '@/components/common/RxkDialog'
import { RxkInput } from '@/components/common/RxkInput'
import { reactive, ref, unref } from 'vue'
import { addRoleApi, deleteRoleListApi, editRoleApi, editRoleStateApi, getRoleListApi } from '@/apis/roleManage'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { SearchFilter } from '@/components/business/searchFilter'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ColumnType } from '@/types/table'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import type { Role } from '@/apis/interface/roleManage'
import { useResource } from '@/hooks/useResource'
import { usePageCache } from '@/hooks/usePageCache'
const { getCache, updateCache } = usePageCache()
const { getAuth } = useResource()
const searchCache = getCache()?.baseSearch || {}

const formDataDom = ref()
const formData = reactive<Role.ReqAddRoleParams>({
  code: 1,
  name: '',
  remarks: '',
  roleType: 'ADMIN'
})

const addRules = ref({
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
})

const handleAdd = () => {
  isAdd.value = true
  formData.name = ''
  formData.remarks = ''
  addRoleVisible.value = true
}

const edit = (data: Role.ReqGetRoleReturn) => {
  isAdd.value = false
  addRoleVisible.value = true
  formData.id = data.id
  formData.name = data.name
  formData.remarks = data.remarks
}

const changeState = async (data: Role.ReqGetRoleReturn) => {
  try {
    currentChangeId.value = data.id
    await editRoleStateApi({ id: data.id, state: data.state.name === 'ENABLED' ? 'DISABLED' : 'ENABLED' })
    await reload()
    ElMessage.success('切换成功')
  } catch {
    ElMessage.error('切换失败')
  } finally {
    currentChangeId.value = ''
  }
}

const deleteRole = (data: Role.ReqGetRoleReturn) => {
  ElMessageBox.confirm('删除后数据无法恢复，确定要删除吗？', '警告', {
    type: 'error',
    beforeClose: async (action, instance, done) => {
      done()
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        try {
          await deleteRoleListApi([data.id])
          await reload()
          ElMessage.success('删除成功')
        } catch {
          ElMessage.error('删除失败')
        } finally {
          instance.confirmButtonLoading = false
        }
      }
    }
  })
}

const currentChangeId = ref<string>('')
const addRoleVisible = ref<boolean>(false)
const isAdd = ref<boolean>(false)
const searchInfo = {
  count: false,
  model: {
    roleName: searchCache.roleName || '',
    state: ''
  },
  orderBys: [],
  searches: ''
}
// 自定义表单数据
const searchFormData = ref<FormSchema[]>([
  {
    fieldName: '角色名称',
    component: 'Input',
    key: 'roleName',
    val: searchInfo.model.roleName
  }
])
const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})
const columns = ref<ColumnType[]>([
  { key: 'id', title: '角色ID', width: 200 },
  { key: 'name', title: '角色名称' },
  { key: 'remarks', title: '角色描述' },
  {
    key: 'state',
    title: '状态',
    render: ({ cellData }) => {
      return <span>{cellData?.state?.value || '-'}</span>
    }
  },
  { key: 'createTime', title: '添加时间', width: 200 },
  { key: 'updateTime', title: '最近更新时间', width: 200 },
  {
    key: 'operate',
    title: '操作',
    fixed: 'right',
    width: 140,
    render: ({ cellData }) => {
      return (
        <>
          {(getAuth('ROLE_UPDATE') && cellData?.roleType?.name !== 'SUPER') ? <RxkButton class="table-action-btn" onClick={() => edit(cellData)} text>编辑</RxkButton> : ''}
          {(getAuth('ROLE_ENABLE') && cellData?.roleType?.name !== 'SUPER') ?
            <el-popconfirm title={`是否确定要${cellData.state.code === 1 ? '禁用' : '启用'}?`} onConfirm={() => changeState(cellData)}>
              {{
                reference: () =>
                  <RxkButton class="table-action-btn" text loading={cellData.id === currentChangeId.value}>
                    {cellData.state.code === 1 ? '禁用' : '启用'}
                  </RxkButton>
              }}
            </el-popconfirm> : ''}
          {(getAuth('ROLE_DELETE') && cellData?.roleType?.name !== 'SUPER') ? <RxkButton class="table-action-btn" text onClick={() => deleteRole(cellData)} >删除</RxkButton> : ''}
        </>
      )
    }
  }
])

const addSure = () => {
  formDataDom.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const request = isAdd.value ? addRoleApi : editRoleApi
        await request(formData)
        await reload()
        ElMessage.success(isAdd.value ? '新增成功' : '编辑成功')
        addRoleVisible.value = false
      } finally {
        //
      }
    }
  })
}

const getBasicColumns = () => unref(columns)

const [registerTable, { reload, setSearchInfo }] = useTable({
  api: getRoleListApi,
  columns: getBasicColumns(),
  searchInfo: searchInfo,
  immediate: true // 是否立刻请求
})

const search = (val: { [k: string]: any }) => {
  searchInfo.model.roleName = val.roleName
  updateCache({ baseSearch: { ...val } })
  setSearchInfo(searchInfo)
  reload()
}
const selectionChange = (val: { [k: string]: any }) => {
  console.log('val', val)
}

</script>

<style scoped lang="scss">
.add-edit {
  padding: 16px 24px;
}

.main-user-manage {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .el-form--inline .el-form-item {
    margin-right: 40px;
  }

  .btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 16px;
  }

  .table-box {
    width: 100%;
    flex: 1;
    overflow-y: hidden;
  }
}
</style>
