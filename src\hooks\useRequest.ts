import { ref, onMounted } from 'vue'

interface Options {
  manual: boolean
}

export function useRequest<T> (requestFn: () => Promise<T>, options: Partial<Options> = {}) {
  const { manual = false } = options

  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)

  const run = async () => {
    loading.value = true
    error.value = null
    
    try {
      data.value = await requestFn()
      return data.value
    } catch (err) {
      error.value = err
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    if (!manual) {
      run()
    }
  })

  return {
    data,
    loading,
    error,
    run
  }
}