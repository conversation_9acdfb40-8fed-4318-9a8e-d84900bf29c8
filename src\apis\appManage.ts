import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'

const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

/** 版本列表 */
export const appVersionSelectByPage = (params: any) => request.post('/admin/appVersion/selectByPage', params, jsonHeaderConfig)
/** 添加版本 */
export const appVersionAdd = (params: any) => request.post('/admin/appVersion/add', params, jsonHeaderConfig)
/** 更新版本 */
export const appVersionUpdate = (params: any) => request.post('/admin/appVersion/update', params, jsonHeaderConfig)

/** 版本详情 */
export const appVersionDetail = (params: any) => request.post('/admin/appVersion/detail', params, jsonHeaderConfig)

/** 删除版本 */
export const appVersionDelete = (params: any) => request.post('/admin/appVersion/delete', params, jsonHeaderConfig)