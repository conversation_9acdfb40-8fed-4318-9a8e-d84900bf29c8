import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
import type { Role } from '@/apis/interface/roleManage'

/**
 * @name 角色管理
 */

const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

// 新增添加角色
export const addRoleApi = (params: Role.ReqAddRoleParams) => {
  return request.post('/admin/role/add', params, jsonHeaderConfig)
}
// 编辑添加角色
export const editRoleApi = (params: Role.ReqAddRoleParams) => {
  return request.post('/admin/role/update', params, jsonHeaderConfig)
}
// 编辑角色状态
export const editRoleStateApi = (params: Role.ReqEditRoleStateParams) => {
  return request.post(`/admin/role/enable?id=${params.id}&state=${params.state}`, null, jsonHeaderConfig)
}
// 获取角色列表
export const getRoleListApi = (params: Role.ReqGetRoleParams) => {
  return request.post('/admin/role/page', params, jsonHeaderConfig)
}
// 删除角色列表
export const deleteRoleListApi = (params: string[]) => {
  return request.post('/admin/role/delete', params, jsonHeaderConfig)
}
// 查询角色列表（不分页）
export const getRoleArrayApi = () => {
  return request.post('/admin/role/list', {}, jsonHeaderConfig)
}