<template>
  <el-form :inline="inline" :label-width="labelWidth" :model="formInline">
    <el-form-item v-for="item in config" :key="item.code" :label="item.label">
      <el-input
        v-if="item.type === 'input'"
        v-model="formInline[item.code]"
        :placeholder="item.placeholder"
        v-bind="item.itemConfig"
      ></el-input>
      <el-select
        v-if="item.type === 'select'"
        v-model="formInline[item.code]"
        :placeholder="item.placeholder"
        v-bind="item.itemConfig"
      >
        <el-option
          v-for="em in item.source"
          :label="item.sourceLabel ? em[item.sourceLabel] : em.label"
          :value="item.sourceKey ? em[item.sourceKey] : em.key"
          :key="em.key"
        />
      </el-select>
      <el-radio-group
        v-if="item.type === 'radio'"
        v-model="formInline[item.code]"
        v-bind="item.itemConfig"
      >
        <el-radio v-for="em in item.source" :label="em.label" :key="em.key" />
      </el-radio-group>
      <el-switch
        v-if="item.type === 'switch'"
        v-model="formInline[item.code]"
        v-bind="item.itemConfig"
      />
      <el-checkbox-group
        v-if="item.type === 'checkbox'"
        v-model="formInline[item.code]"
        v-bind="item.itemConfig"
      >
        <el-checkbox
          v-for="em in item.source"
          :label="em.label"
          :key="em.key"
        />
      </el-checkbox-group>
      <el-date-picker
        v-if="item.type === 'date-picker'"
        v-model="formInline[item.code]"
        type="date"
        :placeholder="item.placeholder"
        v-bind="item.itemConfig"
      />
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { type SearchConfig } from './type'
const props = defineProps<{
  config: SearchConfig[]
  formInline: { [k: string]: any }
  inline?: boolean
  labelWidth?: string
}>()
</script>

<style></style>
