## 项目架构

此项目基于容易客进行删减代码得到，使用了 Vue3.x TypeScript Vite4.x element-plus 技术栈。

## node版本

16+

## 安装

npm i

## 运行

npm run serve

## 项目目录

* build -- vite工具的一些配置，不改动
* src
  * apis -- 不同接口地址url，分模块
  * assets -- 静态资源，样式styles，图标
  * components -- 组件
  * configs -- 全局配置文件，包含域名分类等
  * enums -- 枚举
  * hooks -- 封装和复用有状态逻辑的函数
  * router -- 路由
  * stores -- 全局状态管理
  * types -- 业务类型声明文件
  * utils -- 工具函数
  * views -- 视图页面
* types -- 全局类型声明

## 请求

在`src/utils/axios/axiosCancel.ts`中，有一个axiosCancel类，用于管理请求的取消操作。

如果相同的请求以及参数连续请求两次，那么axiosCancel会取消第二次请求，同时会进入axios的响应拦截器，但响应拦截器中会进入到没有响应的错误中。

如果需要一个请求同时进行多次，那么可以在请求中添加`{ ignoreCancelToken: true }`，这样就不会取消请求，也不会进入到没有响应的错误中。

```ts
request.post('/admin/dataDetail/list', info, jsonHeaderConfig, { ignoreCancelToken: true }),
```

还有一种处理方式则是使用`storeRequest`将接口进行缓存，那么相同的请求只会请求一次，并且每个请求都会得到数据。

```ts
const { data } = storeRequest({
  api: (info: any) => request.post('/admin/dataDetail/list', info, jsonHeaderConfig),
  data: data,
  cacheName: 'dataDetailList',
  storeMaxTime: 3000
})

`cacheName`是缓存的名称（如果是匿名函数，则该参数为唯一且必传），`storeMaxTime`是缓存的最大时间，单位为ms。

`Content-Type`：

* `application/json`：请求体为json格式
* `application/x-www-form-urlencoded`：请求体为formData格式
* `multipart/form-data`：请求体为formData格式

默认为`application/x-www-form-urlencoded`，但实际项目中大多数接口都是`application/json`，所以很多接口都需要手动设置请求头为`application/json`。

## 长时间未操作安全验证

在`src/components/business/systemSafe/index.vue`中，有一个长时间未操作安全验证的组件，当用户长时间未操作时，会自动弹出安全验证的弹窗，用户需要输入安全密码才能继续操作。

`useIdle`是用来检测用户是否长时间未操作，如果长时间未操作，如果倒计时结束，那么会将弹窗设置为`true`。

理论上只要不引用该组件，就不会触发长时间验证功能。
