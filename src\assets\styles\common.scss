.reset-form-label {
  .el-form-item__label {
    padding-right: 0;
  }
}
.table-action-btn {
  padding: 0 !important;
  margin: 0 16px 0 0 !important;
  background: transparent !important;
}

// 全局覆盖el-message-box得样式
.el-message-box {
  .el-message-box__btns {
    .el-button--primary {
      background: #5687ff;
      border: none;
      &:hover {
        background: #89abff;
        color: #ffffff;
      }
      &:active {
        background: #4470db;
      }
      &:focus {
        color: #ffffff;
      }
      &.is-disabled {
        background: #aac3ff;
        color: #fff;
        &:hover {
          background: #aac3ff;
        }
      }
    }
  }
}
.el-cascader__collapse-tags {
  height: 400px;
  overflow-y: auto;
}
// // 全局的搜索下拉多选/虚拟多选
// .customer-filter-select {
//   .el-select-v2__selected-item:nth-child(1) {
//     max-width: 60%;
//     span {
//       overflow: hidden;
//     }
//     .el-select-v2__tags-text {
//       text-overflow: ellipsis;
//       white-space: nowrap;
//       width: 100%;
//     }
//   }
//   .el-select-tags-wrapper {
//     width: 90%;
//     .el-tag:nth-child(1) {
//       max-width: 60%;
//       span {
//         overflow: hidden;
//       }
//       .el-select__tags-text {
//         text-overflow: ellipsis;
//         white-space: nowrap;
//         width: 100%;
//       }
//     }
//   }
// }
// 输入过滤筛选选项-多选样式统一化
.custom-filter-select {
  .el-select__tags {
    flex-wrap: nowrap;
    .el-select-tags-wrapper.has-prefix {
      display: inline-flex;
      width: 80%;
      overflow: hidden;
      > span {
        width: 20%;
        &.is-closable {
          width: 80%;
          justify-content: flex-start;
          overflow: hidden;
          .el-tag__content {
            width: 100%;
            overflow: hidden;
            .el-select__tags-text {
              width: 100%;
            }
          }
        }
      }
    }
  }
}

.city-selector .el-cascader__search-input {
  width: 14px;
  min-width: auto;
}

.el-drawer {
  max-width: 100% !important;
}

.layout-shrink {
  .operate-content,
  .search-block,
  .left,
  .right {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  .el-date-range-picker__content {
    float: none;
    width: 100%;
  }
  .el-date-range-picker {
    width: 100%;
    .el-picker-panel__body {
      height: 50vh;
      overflow: auto;
      min-width: 90vw;
    }
  }
  
  .main-header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 11;
  }
  .table-box {
    // min-height: 200px;
    .custom-pagination {
      height: auto;
      padding: 10px 0;
    }
  }
  .pagination-box {
    flex-wrap: wrap;
    .el-pagination__jump {
      margin-left: 0;
    }
    .el-pagination .el-select .el-input {
      width: 96px;
    }
    .el-pagination .el-input__inner {
      height: 24px;
      line-height: 20px;
      font-size: 12px;
    }
  }
  .el-container {

    .main-container {
      padding: 64px 4px 4px!important;
      height: auto!important;
      overflow: auto;
      .main-page {
        margin-top: 44px;
        height: auto;
      }
  }
  }
  .top-nav {
    position: fixed;
    top: 64px;
    z-index: 11;
  }
  .base-table-page {
    padding: 0;
    &.reHeight {
      height: calc(100% - 50px);
    }
  }
  .el-pagination .btn-prev {
    margin: 0;
  }
  .center-tabs {
    overflow-x: scroll !important;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .search-block .export-btn {
    margin-left: 0 !important;
  }
  .el-dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 97.2vw;
    margin:0;
    .el-form {
      max-width: 100%;
    }
    .el-dialog__body {
      max-height: 70vh!important;
    }
  }
  
  .operate_btns {
    overflow-x: auto;
    gap: 4px;
  }
  .base-Info {
    .info-item {
      width: 100% !important;
    }
  }
  .customObject-page .formDesign-absolute {
    position: fixed!important;
  }
}
.import-notification {
  padding:0;
  width: 400px;
  border-radius: 0;
  .el-notification__title {
    padding: 10px;
    border-bottom: 1px solid#EBEEF5;

  }
  .el-notification__group{
    margin: 0;
    width: 100%;
  }
  .import-notification__footer {
    text-align: right;
    border-top: 1px solid#EBEEF5;
    padding: 10px 14px;
  }
}