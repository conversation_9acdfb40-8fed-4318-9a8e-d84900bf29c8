import { settingCenter } from '@/router/modules/settingCenter'

const list:any[] = [
  // { code: 'SettingCenter', name: '配置中心', path: '/settingCenter', icon: 'setting-center', parentId: '', systemCode: 'BASE', dataPermission: true }
]

export function getList (routes: any[], parentId = undefined, treeGrade = 1){
  let grade = treeGrade
  routes.map(route => {
    list.push({
      code: route.name,
      name: route.meta.title,
      path: route.path,
      icon: route.meta.icon,
      grade: grade,
      systemCode: 'BASE',
      parentId: parentId,
      dataPermission: parentId ? 1 : 0
    })
    if(route?.children?.length){
      getList(route.children, route.name, grade++)
    }
  })
}

getList(settingCenter)
console.log(list)

export const menuList = []
const str = `INSERT INTO sys_menu (
  create_by,
  tenant_id,
  NAME,
  tree_grade,
  parent_id,
  update_by,
  CODE,
  icon,
  path,
  data_permission,
  system_code
)
VALUES ${getValueListStr(menuList)};`

function getValueListStr (list:any[]){
  const strArr = list.map(el => {
    return `('1','0','${el.name}',${el.grade},${el.parentId},'1','${el.code}','${el.icon}','${el.path}',${el.dataPermission}, '${el.systemCode}')`
  })
  return strArr.join(',')
}

console.log(str)

const buttonList = {
  '公海管理配置': [
    { name: '创建公海', code: 'ADD' },
    { name: '编辑公海', code: 'UPDATE' },
    { name: '删除公海', code: 'DELETE' }
  ],
  '员工自动分配': [
    { name: '创建规则', code: 'ADD' },
    { name: '编辑规则', code: 'UPDATE' },
    { name: '删除规则', code: 'DELETE' },
    { name: '启用禁用规则', code: 'ENABLE' },
    { name: '查看规则详情', code: 'DETAIL' },
    { name: '开启关闭通知', code: 'OPEN_NOTICE' }
  ],
  '客户分类配置': [
    { name: '添加客户分类', code: 'ADD' },
    { name: '隐藏客户分类', code: 'HIDDEN' }
  ],
  '客户阶段配置': [
    { name: '创建阶段', code: 'ADD' },
    { name: '删除阶段', code: 'DELETE' }
  ],
  '客户标签配置': [
    { name: '创建标签', code: 'ADD' },
    { name: '删除标签', code: 'DELETE' },
    { name: '编辑标签', code: 'UPDATE' },
    { name: '启用禁用标签', code: 'ENABLE' }
  ],
  '公海自动分配': [
    { name: '创建规则', code: 'ADD' },
    { name: '设置优先级', code: 'UPDATE' }
  ],
  '回收规则': [
    { name: '新建规则', code: 'ADD' },
    { name: '编辑规则', code: 'UPDATE' },
    { name: '启用禁用规则', code: 'ENABLE' },
    { name: '复制规则', code: 'COPY' },
    { name: '删除规则', code: 'DELETE' },
    { name: '设置生效范围', code: 'SETTING' }
  ],
  '系统全局配置': [
    { name: '保存', code: 'SAVE' }
  ],
  '员工派单开关': [
    { name: '查看详情', code: 'DETAIL' }
  ],
  '部门与员工': [
    { name: '新增用户', code: 'ADD' },
    { name: '编辑用户', code: 'UPDATE' },
    { name: '启用/禁用用户', code: 'ENABLE' },
    { name: '用户离职', code: 'RESIGN' },
    { name: '新增部门', code: 'ADD_DEPARTMENT' },
    { name: '编辑部门', code: 'UPDATE_DEPARTMENT' },
    { name: '删除部门', code: 'DELETE_DEPARTMENT' }
  ],
  '分公司管理': [
    { name: '添加分公司', code: 'ADD' },
    { name: '编辑分公司', code: 'UPDATE' },
    { name: '启用/禁用分公司', code: 'ENABLE' }
  ],
  '角色管理': [
    { name: '新增角色', code: 'ADD' },
    { name: '编辑角色', code: 'UPDATE' },
    { name: '启用/禁用角色', code: 'ENABLE' },
    { name: '删除角色', code: 'DELETE' }
  ]
}

const buttonStr = `INSERT INTO \`sys_resource\`(code,request_method,name,tree_grade,uri,parent_id,state,remark) 
VALUES ${getButtonStr(buttonList)};`
// ('', 'GET', '自动生成用户名和密码', null, '/admin/user/createUserPassword', null, 1)
function getButtonStr (buttons:any){
  let str = ''
  for(const key in buttons){
    buttons[key].forEach((btn:any) => {
      str += `('${btn.code}', '','${btn.name}', null, '', null, 1, '${key}'),`
    })
  }
  return str
}

console.log(buttonStr)
