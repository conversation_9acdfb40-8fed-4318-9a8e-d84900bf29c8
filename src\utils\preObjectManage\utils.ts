// 对象基础字段
import { conditionEnum, fieldTypeConfig, moduleType, fieldTypeEnum } from '@/enums/preObjectManage'
import type { ComponentType, dragFieldData, FieldType, libraryListType, SubType } from '@/types/preObjectManage'
import allFieldDataClass from './index'
import { _randomInt, GenNonDuplicateID, getUuid } from '@/utils/tools'
import type { List } from '@/types/common'
import { customCompNameEnums } from '@/components/custom/form/components/registerFormComponent'
import { isArray, isEmpty, isNullOrUnDef, isNullOrUndefOrEmpty, isString } from '@/utils/is'
import { getTime } from '@/utils/date'
import { idCardReg, phoneIsDesensitization, emailReg } from '@/utils/regExp'
import { Mapping, Quote, QuoteMapping } from '@/utils/render/quoteMapping'
import { unref } from 'vue'

/**
 * @author：张胜1
 * @desc：列表的默认列表
 * */
export const defaultList = [
  { value: '选项1', label: '选项1' },
  { value: '选项2', label: '选项2' },
  { value: '选项3', label: '选项3' }
]
/**
 * @author：张胜
 * @desc：获取对象字段信息
 * */
export function getFieldBaseDataFn (data: ComponentType):ComponentType {
  return {
    id: data.id || 0,
    tableCode: data.tableCode || 0,
    positionId: data.positionId || 0,
    containerId: data.containerId || 0,
    moduleId: data.moduleId || 0,
    columnId: data.columnId || 0,
    columnSerial: data.columnSerial || '',
    layout: data.layout || '',
    tId: data.tId || '',
    columnInfo: {
      id: data.columnInfo?.id || 0,
      tableCode: data.columnInfo?.tableCode || '',
      tableField: data.columnInfo?.tableField || '',
      tableAlias: data.columnInfo?.tableAlias || '',
      tableName: data.columnInfo?.tableName || '',
      code: data.columnInfo?.code || '',
      defineType: data.columnInfo?.defineType || 0,
      name: data.columnInfo?.name || '',
      prompt: data.columnInfo?.prompt || '',
      desc: data.columnInfo?.desc || '',
      setting: data.columnInfo?.setting || '',
      fieldCode: data.columnInfo?.fieldCode || '',
      fieldType: !isNullOrUndefOrEmpty(data.columnInfo?.fieldType) ? data.columnInfo?.fieldType : '',
      fieldIdentity: data.columnInfo?.fieldIdentity || getUuid(),
      dataType: data.columnInfo?.dataType || '',
      defaultMark: data.columnInfo?.defaultMark || 0,
      defaultValue: data.columnInfo?.defaultValue || '',
      precisions: data.columnInfo?.precisions || '',
      minValue: data.columnInfo?.minValue || '',
      maxValue: data.columnInfo?.maxValue || '',
      minLength: data.columnInfo?.minLength || '',
      maxLength: data.columnInfo?.maxLength || '',
      encryptType: data.columnInfo?.encryptType || 0,
      columnOperateList: data.columnInfo?.columnOperateList || [],
      columnQuoteInfo: data.columnInfo?.columnQuoteInfo || null,
      conditionList: data.columnInfo?.conditionList || [],
      defaultValueConfigInfo: data.columnInfo?.defaultValueConfigInfo || {},
      fieldSerial: data.columnInfo?.fieldSerial || '',
      columnDataList: data.columnInfo?.columnDataList || [],
      dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : '',
      visibleRuleList: data.columnInfo?.visibleRuleList || [],
      unit: data.columnInfo?.unit || '',
      unionTableColumnInfo: data.columnInfo?.unionTableColumnInfo || null,
      columnConfig: data.columnInfo?.columnConfig || null,
      fieldApi: data.columnInfo?.fieldApi || ''
    }
  }
}

export function getContainerBlankData (subData: SubType) {
  console.log(subData, 'subData1111')
  return {
    id: 0,
    parentId: 0,
    positionId: subData.positionId,
    title: '空容器',
    code: 'BLANK',
    defineType: 20,
    layout: '{}',
    children: [],
    modules: [
      {
        id: subData.id || 0,
        tableCode: subData.tableCode,
        positionId: subData.positionId,
        containerId: 0,
        title: subData.title || '',
        desc: '',
        moduleType: subData.moduleType || 0,
        defineType: 20,
        layout: subData.moduleType === moduleType.subForm ? JSON.stringify({ position: { x: 0 }, formStyle: 1 }) : JSON.stringify({ position: { x: 0 } }),
        componentList: [],
        buttonList: [],
        operateList: [],
        group: String(_randomInt(19)),
        tId: GenNonDuplicateID(3),
        columnTableCode: subData.columnTableCode,
        moduleSerial: String(_randomInt(19))
      }
    ],
    tId: GenNonDuplicateID(3),
    tableCode: subData.tableCode
  }
}
export function getContainerTableData (data: any) {
  console.log(data, '11111')
  return {
    id: 0,
    parentId: 0,
    positionId: data.positionId,
    title: '空容器',
    code: 'TABLE',
    defineType: 20,
    layout: '{}',
    children: [
      {
        id: 0,
        parentId: 0,
        title: 'table1',
        code: 'MODULE',
        defineType: 20,
        layout: '{}',
        children: null,
        modules: [],
        tId: GenNonDuplicateID(3),
        tableCode: data.tableCode,
        positionId: data.positionId
      },
      {
        id: 0,
        parentId: 0,
        title: 'table2',
        code: 'MODULE',
        defineType: 20,
        layout: '{}',
        children: null,
        modules: [],
        tId: GenNonDuplicateID(3),
        tableCode: data.tableCode,
        positionId: data.positionId
      },
      {
        id: 0,
        parentId: 0,
        title: 'table3',
        code: 'MODULE',
        defineType: 20,
        layout: '{}',
        children: null,
        modules: [],
        tId: GenNonDuplicateID(3),
        tableCode: data.tableCode,
        positionId: data.positionId
      }
    ],
    modules: [],
    tId: GenNonDuplicateID(3),
    tableCode: data.tableCode
  }
}

export function getTableData (subData: SubType) {
  console.log(subData, 'subData')
  return {
    id: 0,
    tableCode: subData.tableCode,
    containerId: subData.id || 0,
    positionId: subData.positionId,
    title: subData.title || '',
    desc: '',
    moduleType: subData.moduleType || 0,
    defineType: 20,
    layout: '{}',
    componentList: [],
    group: String(_randomInt(19)),
    tId: GenNonDuplicateID(3),
    columnTableCode: subData.columnTableCode,
    moduleSerial: String(_randomInt(19)),
    buttonList: []
  }
}

export function getBtnData (data: Recordable) {
  return {
    desc: '',
    tId: GenNonDuplicateID(3),
    actionCode: '',
    actionValue: '',
    alertCode: 'NEW_TAB',
    code: String(_randomInt(19)),
    defineType: data.defineType, // 按钮是自定义还是预定义
    id: 0,
    moduleSerial: data.moduleSerial,
    reqType: '',
    sceneType: data.sceneType, // 使用场景，按钮配在不同的位置会变化的，分栏右边用11
    sort: 0,
    tableCode: data.tableCode,
    tenantId: data.tenantId || 0,
    title: '按钮',
    url: '',
    value: true,
    extraSetting: '{}',
    buttonSerial: String(_randomInt(19)),
    visibleList: []
  }
}

/**
 * @author：张胜
 * @desc：控制默认配置是否显示
 * */
export const defaultShowFlagConfig = {
  showImport: true, // 是否导入
  showExport: true, // 是否导出
  showTableList: true, // 是否列表展示
  showLayoutWidth: true, // 是否展示字段占比
  showTip: true, // 是否展示提示文字
  showDefault: true, // 是否展示默认值
  showRequired: true, // 是否必填
  showDesensitization: false, // 是否展示脱敏
  showRepeat: false, // 是否展示 是否允许重复
  showTime: false // 是否展示 是否显示时间
}

// 获取布局的场景
export const columnUseSceneEnums = {
  listPage: 1, // 列表页面字段标题
  addPage: 2, // 新增数据页面
  editPage: 3, // 编辑数据页面
  detailPage: 4, // 详情数据页面
  addLayout: 5, // 新增布局页面
  detailLayout: 6, // 详情布局页面
  processBranch: 7, // 流程分支条件
  processAssist: 8, // 流程详情字段
  processDetailPage: 9, // 流程详情数据详情页面
  pageTitle: 10, // 表格表头字段
  pageSearch: 11 // 快捷搜索字段
}

// ("使用场景 0: 其他人员部门组件场景; 1：客户管理移交; 2：公海列表分配; 3: paas表单; 4: 回收规则; 5: 自定义返回人员部门; 6: 协助人; 7: 个人账户授权")
// 人员组件使用场景
export const useSceneTypeEnums = {
  other: 0,
  transfer: 1,
  highSea: 2,
  paas: 3,
  recycle: 4,
  custom: 5,
  helper: 6,
  revoke: 7
}

/**
 * @author：张胜
 * @desc：字段库
 * @todo dataClass记得处理
 * */
export const libraryList: libraryListType[] = [
  {
    title: '输入框组件',
    children: [
      {
        value: fieldTypeConfig.singleLineText,
        label: '单行文本',
        icon: 'leixingshanxingwenbenactiveno',
        dataClass: allFieldDataClass.SingleLineText,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.multilineText,
        label: '多行文本',
        icon: 'leixingduoxingwenbenactiveno',
        dataClass: allFieldDataClass.MultilineText,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.money,
        label: '金额',
        icon: 'leixingjineactiveno',
        dataClass: allFieldDataClass.Money,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.percentage,
        label: '百分比',
        icon: 'leixingmofenbiactiveno',
        dataClass: allFieldDataClass.Percent,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.number,
        label: '数字',
        icon: 'leixingshuziactiveno',
        dataClass: allFieldDataClass.Numbers,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.phone,
        label: '手机',
        icon: 'leixingshoujiactiveno',
        dataClass: allFieldDataClass.Phone,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.creditCode,
        label: '统一社会信用代码',
        icon: 'leixingxinyongdaimaactiveno',
        dataClass: allFieldDataClass.CreditCode,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'fullLine'
      },
      {
        value: fieldTypeConfig.idCard,
        label: '身份证号',
        icon: 'leixingshenfenzhengactiveno',
        dataClass: allFieldDataClass.IdCard,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.email,
        label: '邮箱地址',
        icon: 'leixingyouxiangdezhiactiveno',
        dataClass: allFieldDataClass.Email,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.landline,
        label: '座机号',
        icon: 'leixingzuojiactiveno',
        dataClass: allFieldDataClass.Landline,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.serial,
        label: '流水号',
        icon: 'leixingyouzhengbianmaactiveno',
        dataClass: allFieldDataClass.Serial,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      }
      // {
      //   value: fieldTypeConfig.postalCode,
      //   label: '邮政编码',
      //   icon: 'icon-wenben',
      //   dataClass: allFieldDataClass.PostalCode,
      //   layoutShow: true,
      //   dropSize: 'default',
      //   widthSize: 'halfLine'
      // }
    ]
  },
  {
    title: '日期',
    children: [
      {
        value: fieldTypeConfig.date1,
        label: '日期',
        icon: 'leixingrijishijianactiveno',
        dataClass: allFieldDataClass.Dates1,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.date2,
        label: '日期区间',
        icon: 'leixingrijishijianactiveno',
        dataClass: allFieldDataClass.Dates2,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.date3,
        label: '时间',
        icon: 'leixingtimeactiveno',
        dataClass: allFieldDataClass.Dates3,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      }
    ]
  },
  {
    title: '选择器',
    children: [
      {
        value: fieldTypeConfig.radio,
        label: '单选按钮',
        icon: 'leixingradioactiveno',
        dataClass: allFieldDataClass.SingleRadio,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.checkbox,
        label: '复选按钮',
        icon: 'leixingfuxuanactiveno',
        dataClass: allFieldDataClass.MultipleCheckbox,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.select,
        label: '下拉单选框',
        icon: 'leixingxialashanxuanactiveno',
        dataClass: allFieldDataClass.Select,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.multipleChoice,
        label: '下拉复选框',
        icon: 'leixingxialafuxuanactiveno',
        dataClass: allFieldDataClass.MultipleChoice,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.department,
        label: '部门单选',
        icon: 'leixingbumenshanxuanactiveno',
        dataClass: allFieldDataClass.Department,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.multipleDepartment,
        label: '部门多选',
        icon: 'leixingbumenduoxuanactiveno',
        dataClass: allFieldDataClass.MultipleDepartment,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.person,
        label: '成员单选',
        icon: 'leixingchengyunshanxuanactiveno',
        dataClass: allFieldDataClass.Person,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.personMultiple,
        label: '成员多选',
        icon: 'leixingchengyunduoxuanactiveno',
        dataClass: allFieldDataClass.PersonMultiple,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.cascader,
        label: '多级联动',
        icon: 'duojiliandong',
        dataClass: allFieldDataClass.Cascader,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.address,
        label: '地址',
        icon: 'leixingdezhiactiveno',
        dataClass: allFieldDataClass.Address,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      }
    ]
  },
  {
    title: '上传组件',
    children: [
      {
        value: fieldTypeConfig.picture,
        label: '上传图片',
        icon: 'leixingtupianactiveno',
        dataClass: allFieldDataClass.Picture,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.enclosure,
        label: '上传附件',
        icon: 'leixingbujianactiveno',
        dataClass: allFieldDataClass.Enclosure,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      }
    ]
  },
  {
    title: '高级组件',
    children: [
      {
        value: fieldTypeConfig.subField,
        label: '分栏',
        icon: 'fenlan',
        dataClass: allFieldDataClass.SubField,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.subList,
        label: '子表单',
        icon: 'leixingzibiaoshanactiveno',
        dataClass: allFieldDataClass.SubList,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.subTable,
        label: 'table标签页',
        icon: 'leixingtabactiveno',
        dataClass: allFieldDataClass.SubTable,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      // {
      //   value: fieldTypeConfig.partingLine,
      //   label: '分割线',
      //   icon: 'icon-wenben',
      //   dataClass: allFieldDataClass.PartingLine,
      //   layoutShow: true,
      //   dropSize: 'default',
      //   widthSize: 'halfLine'
      // },
      {
        value: fieldTypeConfig.associationQuery,
        label: '关联查询',
        icon: 'leixingguanlianzhaxunactiveno',
        dataClass: allFieldDataClass.AssociationQuery,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.associationForm,
        label: '关联表单',
        icon: 'leixingguanlianzhaxunactiveno',
        dataClass: allFieldDataClass.AssociationForm,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.button,
        label: '按钮',
        icon: 'leixinganniuactiveno',
        dataClass: allFieldDataClass.Button,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.sign,
        label: '签名',
        icon: 'qianming',
        dataClass: allFieldDataClass.Sign,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      }
    ]
  }

]

/**
 * @author：张胜
 * @desc：获取控件库显示字段
 * */

export function layoutFieldConfigTypeList (): libraryListType[] {
  libraryList.forEach(item => {
    item.children = item.children.filter(child => child.layoutShow)
  })
  return libraryList
}

export function layoutHighConfigTypeList ():libraryListType[] {
  const list:libraryListType[] = [{
    title: '高级组件',
    children: [
      {
        value: fieldTypeConfig.subField,
        label: '分栏',
        icon: 'icon-wenben',
        dataClass: allFieldDataClass.SubField,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.subTable,
        label: 'table标签页',
        icon: 'icon-wenben',
        dataClass: allFieldDataClass.SubTable,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      // {
      //   value: fieldTypeConfig.partingLine,
      //   label: '分割线',
      //   icon: 'icon-wenben',
      //   dataClass: allFieldDataClass.PartingLine,
      //   layoutShow: true,
      //   dropSize: 'default',
      //   widthSize: 'halfLine'
      // },
      {
        value: fieldTypeConfig.associationForm,
        label: '关联表单',
        icon: 'icon-wenben',
        dataClass: allFieldDataClass.AssociationForm,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      },
      {
        value: fieldTypeConfig.button,
        label: '按钮',
        icon: 'icon-wenben',
        dataClass: allFieldDataClass.Button,
        layoutShow: true,
        dropSize: 'default',
        widthSize: 'halfLine'
      }
    ]
  }]
  list.forEach(item => {
    item.children = item.children.filter(child => child.layoutShow)
  })
  return list
}

export function layoutFieldPropertyToFieldDataClass () {
  const map: Record<string, any> = {}
  libraryList.forEach(item => {
    console.log(item, 'item123333')
    if (item.children.length) {
      item.children.forEach(sub => {
        map[sub.value] = sub.dataClass
      })
    }
    // 添加业务组件
    map[fieldTypeConfig.tags] = allFieldDataClass.Tags
    // 添加业务组件
    map[fieldTypeConfig.referralPerson] = allFieldDataClass.ReferralPerson
    // 添加业务组件
    map[fieldTypeConfig.referralPersonSource] = allFieldDataClass.ReferralPersonSource
  })
  console.log(map, 'map999')
  return map
}

/**
 * @author：张胜
 * @desc：根据字段类型渲染组件
 * @todo
 * */
const renderFieldComp: Record<string, Fn> = {}
export const fieldCompFnMap = (renderFieldComp: Record<string, Fn>) => {
  [fieldTypeConfig.singleLineText, fieldTypeConfig.multilineText].forEach(i => {
    renderFieldComp[i] = () => 'InputConfig'
  });

  [fieldTypeConfig.money].forEach(i => {
    renderFieldComp[i] = () => 'MoneyConfig'
  });
  [fieldTypeConfig.percentage, fieldTypeConfig.number].forEach(i => {
    renderFieldComp[i] = () => 'PercentageConfig'
  });
  [fieldTypeConfig.phone].forEach(i => {
    renderFieldComp[i] = () => 'PhoneConfig'
  });

  [fieldTypeConfig.creditCode, fieldTypeConfig.idCard, fieldTypeConfig.email, fieldTypeConfig.landline].forEach(i => {
    renderFieldComp[i] = () => 'CreditCodeConfig'
  });
  [fieldTypeConfig.serial].forEach(i => {
    renderFieldComp[i] = () => 'SerialConfig'
  });
  [fieldTypeConfig.date1, fieldTypeConfig.date2, fieldTypeConfig.date3].forEach(i => {
    renderFieldComp[i] = () => 'DateConfig'
  });
  [fieldTypeConfig.radio].forEach(i => {
    renderFieldComp[i] = () => 'RadioConfig'
  });
  [fieldTypeConfig.checkbox].forEach(i => {
    renderFieldComp[i] = () => 'CheckboxConfig'
  });
  [fieldTypeConfig.select].forEach(i => {
    renderFieldComp[i] = () => 'SelectConfig'
  });
  [fieldTypeConfig.multipleChoice].forEach(i => {
    renderFieldComp[i] = () => 'MultipleChoiceConfig'
  });
  [fieldTypeConfig.department].forEach(i => {
    renderFieldComp[i] = () => 'DepartmentConfig'
  });
  [fieldTypeConfig.picture].forEach(i => {
    renderFieldComp[i] = () => 'PictureConfig'
  });
  [fieldTypeConfig.enclosure].forEach(i => {
    renderFieldComp[i] = () => 'EnclosureConfig'
  });
  [fieldTypeConfig.associationQuery].forEach(i => {
    renderFieldComp[i] = () => 'AssociationQueryConfig'
  });
  // [fieldTypeConfig.associationForm].forEach(i => {
  //   renderFieldComp[i] = () => 'AssociationFormConfig'
  // });
  [fieldTypeConfig.subField].forEach(i => {
    renderFieldComp[i] = () => 'SubFieldConfig'
  });
  [fieldTypeConfig.address].forEach(i => {
    renderFieldComp[i] = () => 'AddressConfig'
  });
  [fieldTypeConfig.sign].forEach(i => {
    renderFieldComp[i] = () => 'SignConfig'
  });
  [fieldTypeConfig.cascader].forEach(i => {
    renderFieldComp[i] = () => 'CascaderConfig'
  });
  [fieldTypeConfig.person].forEach(i => {
    renderFieldComp[i] = () => 'PersonConfig'
  });
  [fieldTypeConfig.partingLine].forEach(i => {
    renderFieldComp[i] = () => 'PartingLineConfig'
  });
  [fieldTypeConfig.tags].forEach(i => {
    renderFieldComp[i] = () => 'TagsConfig'
  });
  [fieldTypeConfig.personMultiple].forEach(i => {
    renderFieldComp[i] = () => 'PersonMultipleConfig'
  });
  [fieldTypeConfig.multipleDepartment].forEach(i => {
    renderFieldComp[i] = () => 'MultipleDepartmentConfig'
  });
  [fieldTypeConfig.referralPerson].forEach(i => {
    renderFieldComp[i] = () => 'ReferralPersonConfig'
  });
  [fieldTypeConfig.referralPersonSource].forEach(i => {
    renderFieldComp[i] = () => 'ReferralPersonSourceConfig'
  })
  return renderFieldComp
}

export const formTypeRelatePropertyStrategy = fieldCompFnMap(renderFieldComp)
const defaultFormTypeRelatePropertyStrategy = () => 'InputConfig'
// 获取应展示组件的类型
export const getComponentType = (data:dragFieldData): string => {
  let res = ''
  const fieldProperty = data.fieldProperty
  const strategy = formTypeRelatePropertyStrategy[fieldProperty] || defaultFormTypeRelatePropertyStrategy
  res = strategy()
  return res
}

// 字段初始值
// export const initData = {
//   id: 0,
//   tableCode: 0,
//   positionId: 0,
//   containerId: 0,
//   moduleId: 0,
//   columnId: 0,
//   code: '',
//   moduleType: 0,
//   defineType: 0,
//   name: '',
//   prompt: '',
//   desc: '',
//   setting: '',
//   layout: '',
//   columnInfo: {},
//   componentOperateList: [], // 如果是接口返回，那我自定义字段，这个配置咋配，明天来问
//   tId: GenNonDuplicateID(3)
// }
export const initData = () => {
  return {
    id: 0,
    tableCode: 0,
    positionId: 0,
    containerId: 0,
    moduleId: 0,
    columnId: 0,
    code: '',
    moduleType: 0,
    defineType: 0,
    name: '',
    prompt: '',
    desc: '',
    setting: '',
    layout: '',
    columnInfo: {},
    componentOperateList: [], // 如果是接口返回，那我自定义字段，这个配置咋配，明天来问
    tId: GenNonDuplicateID(3)
  }
}

/**
 * @author：张胜
 * @desc：自增标号时间转换成名字
 * */
export const timeTurnName = (str:any) => {
  if (str) {
    str = str.replace(/\{yyyy\}/g, () => {
      return new Date().getFullYear()
    })
    str = str.replace(/\{mm\}/g, () => {
      const month = new Date().getMonth() + 1
      return month >= 10 ? month : '0' + month
    })
    str = str.replace(/\{dd\}/g, () => {
      const day = new Date().getDate()
      return day >= 10 ? day : '0' + day
    })
    return str
  }
  return ''
}
// 自增编号的重新计算规则选项列表
export const recalculationRuleList = [
  { value: 0, label: '不重新计算' },
  { value: 1, label: '按年重新计算' },
  { value: 2, label: '按年,月重新计算' },
  { value: 3, label: '按年,月,日重新计算' }
]
// 过滤筛选计算规则
export function filterRuleList (value: any): List[] {
  let res
  const str = String(value)
  const yearStr = '{yyyy}'
  const monthStr = '{mm}'
  const dayStr = '{dd}'
  if (str.includes(yearStr) && str.includes(monthStr) && str.includes(dayStr)) { // 年, 月, 日
    res = recalculationRuleList.slice()
  } else if (str.includes(yearStr) && str.includes(monthStr)) { // 年, 月
    res = recalculationRuleList.slice(0, 3)
  } else if (str.includes(yearStr)) { // 年 (年, 日)
    res = recalculationRuleList.slice(0, 2)
  } else {
    res = recalculationRuleList.slice(0, 1)
  }
  return res
}
const conditions: Record<string, Fn> = {}
export function getCondition (conditions: Record<string, Fn>) {
  [fieldTypeConfig.singleLineText,

    fieldTypeConfig.phone,
    fieldTypeConfig.idCard,
    fieldTypeConfig.creditCode,
    fieldTypeConfig.email,
    fieldTypeConfig.landline,
    fieldTypeConfig.serial,
    fieldTypeConfig.multilineText].forEach(i => {
    conditions[i] = () => [
      {
        label: '等于',
        value: conditionEnum.equal
      },
      {
        label: '不等于',
        value: conditionEnum.unequal
      },
      {
        label: '包含',
        value: conditionEnum.contain
      },
      {
        label: '不包含',
        value: conditionEnum.notin
      },
      {
        label: '为空',
        value: conditionEnum.isnull
      },
      {
        label: '不为空',
        value: conditionEnum.notnull
      }
    ]
  });
  [fieldTypeConfig.number,
    fieldTypeConfig.money,
    fieldTypeConfig.percentage,
    fieldTypeConfig.date1,
    fieldTypeConfig.date2,
    fieldTypeConfig.date3].forEach(i => {
    conditions[i] = () => [
      {
        label: '等于',
        value: conditionEnum.equal
      },
      {
        label: '不等于',
        value: conditionEnum.unequal
      },
      {
        label: '大于',
        value: conditionEnum.gt
      },
      {
        label: '小于',
        value: conditionEnum.lt
      },
      {
        label: '大于等于',
        value: conditionEnum.gte
      },
      {
        label: '小于等于',
        value: conditionEnum.lte
      },
      {
        label: '为空',
        value: conditionEnum.isnull
      },
      {
        label: '不为空',
        value: conditionEnum.notnull
      }
    ]
  });
  [fieldTypeConfig.address].forEach(i => {
    conditions[i] = () => [
      // {
      //   label: '等于',
      //   value: conditionEnum.equal
      // },
      // {
      //   label: '不等于',
      //   value: conditionEnum.unequal
      // },
      {
        label: '包含',
        value: conditionEnum.contain
      },
      {
        label: '不包含',
        value: conditionEnum.exclusive
      },
      {
        label: '为空',
        value: conditionEnum.isnull
      },
      {
        label: '不为空',
        value: conditionEnum.notnull
      }
    ]
  });
  [fieldTypeConfig.cascader].forEach(i => {
    conditions[i] = () => [
      {
        label: '匹配',
        value: conditionEnum.match
      },
      {
        label: '不匹配',
        value: conditionEnum.mismatch
      },
      {
        label: '为空',
        value: conditionEnum.isnull
      },
      {
        label: '不为空',
        value: conditionEnum.notnull
      }
    ]
  });
  [fieldTypeConfig.radio,
    fieldTypeConfig.select,
    fieldTypeConfig.person,
    fieldTypeConfig.department].forEach(i => {
    conditions[i] = () => [
      {
        label: '包含',
        value: conditionEnum.in
      },
      {
        label: '不包含',
        value: conditionEnum.notin
      },
      {
        label: '为空',
        value: conditionEnum.isnull
      },
      {
        label: '不为空',
        value: conditionEnum.notnull
      }
    ]
  });
  [fieldTypeConfig.checkbox,
    fieldTypeConfig.multipleChoice,
    fieldTypeConfig.personMultiple,
    fieldTypeConfig.multipleDepartment].forEach(i => {
    conditions[i] = () => [
      {
        label: '包含',
        value: conditionEnum.in_set
      },
      {
        label: '不包含',
        value: conditionEnum.not_in_set
      },
      {
        label: '为空',
        value: conditionEnum.isnull
      },
      {
        label: '不为空',
        value: conditionEnum.notnull
      }
    ]
  });
  [fieldTypeConfig.tags].forEach(i => {
    conditions[i] = () => [
      {
        label: '包含',
        value: conditionEnum.json_inset
      },
      {
        label: '不包含',
        value: conditionEnum.json_not_inset
      },
      {
        label: '为空',
        value: conditionEnum.isnull
      },
      {
        label: '不为空',
        value: conditionEnum.notnull
      }
    ]
  })
  return conditions
}

export const formTypeStrategyCondition = getCondition(conditions)
const defaultFormTypeStrategyCondition = () => []
// 获取应展示组件的类型
export const getConditionType = (code: string):List[] => {
  let res = []
  const strategy = formTypeStrategyCondition[code] || defaultFormTypeStrategyCondition
  res = strategy()
  return res
}

export function getFieldType () {
  // 字段类别 0-原表已有字段
  // 10-系统引用字段
  // 11-自定义关联字段
  // 20-系统映射字段
  // 21-自定义映射字段
  // 50-子表原有字段
  // 51-子表自定义字段
  // 60-子表系统关联字段
  // 61-子表自定义关联字段
  // 70-子表系统映射字段
  // 71-子表自定义映射字段
  // 100-表单字段
  // 1000-自定义扩展字段
}

// 是否显示删除图标
export function showDelete (data: FieldType) {
  return (data.fieldType === 11 ||
    data.fieldType === 21 ||
    data.fieldType === 51 ||
    data.fieldType === 61 ||
    data.fieldType === 71 ||
    data.fieldType === 1000)
}
export const getTableColumnWidthByCode = (key: string) => {
  switch (key) {
    case fieldTypeConfig.date3:
    case fieldTypeConfig.number:
    case fieldTypeConfig.money:
      return 160
    case fieldTypeConfig.date1:
      return 200
    case fieldTypeConfig.date2:
      return 350
    case fieldTypeConfig.address:
      return 220
    default:
      return 0
  }
}

// 快捷筛选，高级筛选
export function createFilterOperateConfig (states: Recordable) {
  [
    fieldTypeConfig.singleLineText, // 单行文本
    fieldTypeConfig.multilineText, // 多行文本
    fieldTypeConfig.phone, // 手机号
    fieldTypeConfig.landline, // 座机号
    fieldTypeConfig.idCard, // 身份证号
    fieldTypeConfig.creditCode, // 统一社会信用代码
    fieldTypeConfig.email // 邮箱地址
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      console.log(field, 'field2221')
      return {
        compName: customCompNameEnums.input,
        maxlength: 50,
        class: 'filter-search-input',
        placeholder: field.prompt || '请选择',
        val: field.val || ''
      }
    }
  });
  [
    fieldTypeConfig.date1 // 日期
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      console.log(field, 'eeee')
      let format = 'YYYY-MM-DD HH:mm:ss'
      let type = 'datetime'
      const val = ''
      if (field.precisions === 1 || field.precisions === 0) {
        format = 'YYYY-MM-DD HH:mm:ss'
        type = field.condition === 'interval' ? 'datetimerange' : 'datetime'
      }
      if (field.precisions === 2) {
        format = 'YYYY-MM-DD'
        type = field.condition === 'interval' ? 'daterange' : 'date'
      }
      if (field.precisions === 3) {
        format = 'YYYY-MM'
        type = field.condition === 'interval' ? 'monthrange' : 'month'
      }
      if (field.precisions === 4) {
        format = 'YYYY'
        type = 'year'
      }
      return {
        compName: field.condition === 'interval' ? customCompNameEnums.date2 : customCompNameEnums.date1,
        format,
        type,
        placeholder: '请选择',
        val: field.val || val
      }
    }
  });
  [
    fieldTypeConfig.date2 // 日期区间
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      console.log(field, 'eeee')
      let format = 'YYYY-MM-DD HH:mm:ss'
      let type = 'datetimerange'
      const val = ''
      if (field.precisions === 1 || field.precisions === 0) {
        format = 'YYYY-MM-DD HH:mm:ss'
        type = 'datetimerange'
      }
      if (field.precisions === 2) {
        format = 'YYYY-MM-DD'
        type = 'daterange'
      }
      if (field.precisions === 3) {
        format = 'YYYY-MM'
        type = 'monthrange'
      }
      return {
        compName: customCompNameEnums.date2,
        format,
        type,
        placeholder: '请选择',
        val: field.val || val
      }
    }
  });
  [
    fieldTypeConfig.date3 // 时间
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      console.log(field, 'eeee')
      let format = 'HH:mm:ss'
      const val = ''
      if (field.precisions === 1 || field.precisions === 0) {
        format = 'HH:mm:ss'
      }
      if (field.precisions === 2) {
        format = 'HH:mm'
      }
      if (field.precisions === 3) {
        format = 'YYYY-MM'
      }
      return {
        compName: customCompNameEnums.date3,
        format,
        placeholder: '请选择',
        val: field.val || val
      }
    }
  });
  [
    fieldTypeConfig.select, // 下拉单选
    fieldTypeConfig.radio // 单选按钮
    // fieldTypeConfig.multipleChoice
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.singleSelect,
        placeholder: field.prompt || '请选择',
        multiple: false,
        dataList: field.dataList || [],
        val: field.val || ''
      }
    }
  });
  [
    fieldTypeConfig.multipleChoice, // 下拉多选
    fieldTypeConfig.checkbox // 复选框
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.multipleSelect,
        placeholder: field.prompt || '请选择',
        multiple: true,
        dataList: field.dataList || [],
        val: field.val || []
      }
    }
  });
  [
    fieldTypeConfig.number, // 数字
    fieldTypeConfig.money, // 金额
    fieldTypeConfig.percentage // 百分比
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      console.log(field, 'field1112')
      return {
        compName: field.condition === conditionEnum.interval ? customCompNameEnums.inputGai : customCompNameEnums.input,
        maxlength: 50,
        class: 'filter-search-input',
        placeholder: field.prompt || '请选择',
        val: field.val || (field.condition === conditionEnum.interval ? [] : '')
      }
    }
  });
  [
    fieldTypeConfig.cascader // 多级联动
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.cascade,
        placeholder: field.prompt || '请选择',
        multiple: false,
        dataList: field.dataList || [],
        val: field.val || ''
      }
    }
  });
  [
    fieldTypeConfig.personMultiple // 人员多选
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      console.log(field, 'field1112')
      return {
        compName: customCompNameEnums.personMultiple,
        class: 'filter-search-input',
        val: field.val || [],
        placeholder: field.prompt || '请选择'
      }
    }
  });
  [
    fieldTypeConfig.person // 人员单选
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      console.log(field, '人员单选field')
      return {
        compName: customCompNameEnums.person,
        class: 'filter-search-input',
        val: field.val || [],
        placeholder: field.prompt || '请选择'
      }
    }
  });
  [
    fieldTypeConfig.multipleDepartment // 部门多选
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.multipleDepartment,
        class: 'filter-search-input',
        val: field.val || [],
        placeholder: field.prompt || '请选择'
      }
    }
  });
  [
    fieldTypeConfig.department // 部门单选
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.department,
        class: 'filter-search-input',
        val: field.val || [],
        placeholder: field.prompt || '请选择'
      }
    }
  });
  [
    fieldTypeConfig.tags // 客户标签
  ].forEach(key => {
    states[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.tags,
        val: field.val || [],
        placeholder: field.prompt || '请选择'
      }
    }
  })
  return states
}
const searchObj: Recordable = {}
export const filterOperateConfig = createFilterOperateConfig(searchObj)

const filterObj: Recordable = {}
function createFilterLabelStrategy (filterObj: Recordable) {
  // 下拉单选, 下拉单选(系统自带可以编辑), 下拉单选(系统自带只能修改名称),
  [
    fieldTypeConfig.select
  ].forEach(key => {
    filterObj[key] = (data: Recordable, form: Recordable, code: string) => {
      const value = form[code]
      const tempValueArr = value ? value?.split?.(',')?.map?.((i: any) => String(i)) : []
      const dataList = data.renderConfig.dataList || []
      const tempArr: any[] = []
      dataList.forEach((i: List) => {
        const index = tempValueArr.indexOf(String(i.value))
        if (index > -1) {
          tempArr.push(i.label)
        }
      })
      return tempArr.join(',')
    }
  });
  [
    fieldTypeConfig.multipleChoice
  ].forEach(key => {
    filterObj[key] = (data: Recordable, form: Recordable, code: string) => {
      const value = form[code]
      const tempValueArr = value ? value?.split?.(',')?.map((i: any) => String(i)) : []
      console.log(tempValueArr, 'tempValueArr')
      const dataList = data.renderConfig.dataList || []
      const tempArr: any[] = []
      dataList.forEach((i: List) => {
        const index = tempValueArr.indexOf(String(i.value))
        console.log(index, 'index111')
        if (index > -1) {
          tempArr.push(i.label)
        }
      })
      return tempArr.join(',')
    }
  })
  return filterObj
}
export const advancedFilterLabelStrategy = createFilterLabelStrategy(filterObj)

// 处理快捷筛选传给后端的值
export function createFastSearchValueStrategy (fasterObj:Recordable) {
  // 下拉多选
  [
    fieldTypeConfig.multipleChoice
  ].forEach(key => {
    fasterObj[key] = {
      getVal: (value: any) => {
        return value ? (isArray(value) ? value.map(val => String(val)) : value?.split?.(',')) : []
      },
      clearVal: (field: Recordable) => {
        field.renderConfig.val = []
      }
    }
  });
  // 日期区间
  [
    fieldTypeConfig.date2
  ].forEach(key => {
    fasterObj[key] = {
      getVal: (value: any) => {
        return value ? (isArray(value) ? value.map(val => String(val)).join(',') : value ) : []
      },
      clearVal: (field: Recordable) => {
        field.renderConfig.val = []
      }
    }
  });
  // 时间
  [
    fieldTypeConfig.date3
  ].forEach(key => {
    fasterObj[key] = {
      getVal: (value: any) => {
        return value ? getTime(value) : []
      },
      clearVal: (field: Recordable) => {
        field.renderConfig.val = ''
      }
    }
  });
  // 金额
  [
    fieldTypeConfig.money
  ].forEach(key => {
    fasterObj[key] = {
      getVal: (value: any) => {
        console.log(value, 'value21')
        return value ? [value[0] || null, value[1] || null] : []
      },
      clearVal: (field: Recordable) => {
        field.renderConfig.val = []
      }
    }
  })
  return fasterObj
}
const fasterObj: Recordable = {}
export const dealFastSearchValueConfig = createFastSearchValueStrategy(fasterObj)

const tableObj = {}
// 处理表格回显值
export function createTableValueStrategy (tableObj: Recordable) {
  // 单选下拉框，单选按钮
  [
    fieldTypeConfig.select,
    fieldTypeConfig.radio,
    fieldTypeConfig.person
  ].forEach(key => {
    tableObj[key] = (filed: Recordable, value: any) => {
      const list = filed.dataList || []
      return filterEnums(String(value), list).label
    }
  });
  // 多选下拉，复选按钮
  [
    fieldTypeConfig.multipleChoice,
    fieldTypeConfig.checkbox,
    fieldTypeConfig.personMultiple,
    fieldTypeConfig.department,
    fieldTypeConfig.multipleDepartment
  ].forEach(key => {
    tableObj[key] = (filed: Recordable, value: any) => {
      const list = filed.dataList || []
      const tempArrValue: any[] = !isNullOrUndefOrEmpty(value) ? String(value).split(',').map((val: any) => String(val)) : []
      let resultArr:Recordable[] = []
      resultArr = list.filter((item: Recordable) => {
        if (tempArrValue?.includes(String(item.value))) {
          return item
        }
      })
      if (resultArr) {
        return resultArr.map((item: Recordable) => item.label).join(',')
      } else {
        return '-'
      }
    }
  });
  // 单行文本，多行文本，手机号，邮箱，邮政编码，描述文字，身份证, 统一社会信用代码，流水号，座机号，金额，数字，百分比，日期
  [
    fieldTypeConfig.singleLineText,
    fieldTypeConfig.multilineText,
    fieldTypeConfig.phone,
    fieldTypeConfig.email,
    fieldTypeConfig.postalCode,
    fieldTypeConfig.description,
    fieldTypeConfig.idCard,
    fieldTypeConfig.creditCode,
    fieldTypeConfig.serial,
    fieldTypeConfig.landline,
    fieldTypeConfig.money,
    fieldTypeConfig.number,
    fieldTypeConfig.percentage
  ].forEach(key => {
    tableObj[key] = (filed: Recordable, value: any) => {
      return value
    }
  })
  return tableObj
}

export const dealTableValueConfig = createTableValueStrategy(tableObj)
// 过滤枚举
export const filterEnums = (value: any, enums: Recordable[]) => {
  if (isNullOrUnDef(value) || isEmpty(value) || isNullOrUnDef(enums) || isEmpty(enums)) {
    return {
      value: '-',
      label: '-'
    }
  }
  const tempEnum = enums.find((item: Recordable) => {
    if (String(item.value) === String(value)) return item
  })
  if (tempEnum) {
    return tempEnum
  } else {
    return {
      value: '-',
      label: '-'
    }
  }
}
const formObj = {}
// 处理表单要怎么显示
export function createFormStrategy (formObj: Recordable) {
  // 单行文本
  [
    fieldTypeConfig.singleLineText, // 单行文本
    fieldTypeConfig.multilineText // 多行文本
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      const obj: Recordable = {
        compName: customCompNameEnums.input,
        maxlength: 50,
        placeholder: field.prompt || '请输入',
        inputType: 'text'
      }
      switch (field.code) {
        case fieldTypeConfig.multilineText:
          obj.inputType = 'textarea'
      }
      return obj
    }
  });
  // 下拉单选
  [
    fieldTypeConfig.select
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => ({
      compName: customCompNameEnums.singleSelect,
      placeholder: field.prompt || '请选择',
      dataList: field.columnDataList || []
    })
  });
  // 单选按钮
  [
    fieldTypeConfig.radio
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => ({
      compName: customCompNameEnums.radio,
      placeholder: field.name || '请选择',
      dataList: field.columnDataList || []
    })
  });
  // 多选按钮
  [
    fieldTypeConfig.checkbox
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => ({
      compName: customCompNameEnums.checkbox,
      placeholder: field.name || '请选择',
      dataList: field.columnDataList || []
    })
  });
  // 下拉多选
  [
    fieldTypeConfig.multipleChoice
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => ({
      compName: customCompNameEnums.multipleSelect,
      placeholder: field.prompt || '请选择',
      dataList: field.columnDataList || []
    })
  });
  // 日期
  [
    fieldTypeConfig.date1
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      let format = 'YYYY-MM-DD HH:mm:ss'
      let type = 'datetime'
      if (field.precisions === 1) {
        format = 'YYYY-MM-DD HH:mm:ss'
        type = 'datetime'
      }
      if (field.precisions === 2) {
        format = 'YYYY-MM-DD'
        type = 'date'
      }
      if (field.precisions === 3) {
        format = 'YYYY-MM'
        type = 'month'
      }
      if (field.precisions === 4) {
        format = 'YYYY'
        type = 'year'
      }
      return {
        compName: customCompNameEnums.date1,
        placeholder: field.name || '请选择',
        format,
        type
      }
    }
  });
  // 日期区间
  [
    fieldTypeConfig.date2
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      let format = 'YYYY-MM-DD HH:mm:ss'
      let type = 'datetimerange'
      if (field.precisions === 1) {
        format = 'YYYY-MM-DD HH:mm:ss'
        type = 'datetimerange'
      }
      if (field.precisions === 2) {
        format = 'YYYY-MM-DD'
        type = 'daterange'
      }
      if (field.precisions === 3) {
        format = 'YYYY-MM'
        type = 'monthrange'
      }
      return {
        compName: customCompNameEnums.date2,
        placeholder: field.name || '请选择',
        format,
        type
      }
    }
  });
  // 时间
  [
    fieldTypeConfig.date3
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      let format = 'HH:mm:ss'
      if (field.precisions === 1) {
        format = 'HH:mm:ss'
      }
      if (field.precisions === 2) {
        format = 'HH:mm'
      }
      if (field.precisions === 3) {
        format = 'YYYY-MM'
      }
      return {
        compName: customCompNameEnums.date3,
        placeholder: field.name || '请选择',
        format
      }
    }
  });
  // 级联
  [
    fieldTypeConfig.cascader
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => ({
      compName: customCompNameEnums.cascade,
      placeholder: field.name || '请选择',
      dataList: field.columnDataList || []
    })
  });
  // 流水号
  [
    fieldTypeConfig.serial
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => ({
      compName: customCompNameEnums.input,
      placeholder: '自动生成无需填写',
      disabled: true
    })
  });
  // 上传图片
  [
    fieldTypeConfig.picture
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => ({
      compName: customCompNameEnums.uploadImg,
      placeholder: field.name || '请选择'
    })
  });
  // 上传附件
  [
    fieldTypeConfig.enclosure
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => ({
      compName: customCompNameEnums.uploadFile,
      placeholder: field.name || '请选择'
    })
  });
  // 引用字段
  [
    fieldTypeConfig.quote
  ].forEach(key => {
    formObj[key] = ( field: Recordable, options?: Recordable, quoteMappingConfig?: QuoteMapping) => {
      const fieldClass = new Quote(field, options)
      quoteMappingConfig?.setQuoteFieldList(field)
      return {
        compName: customCompNameEnums.quote,
        placeholder: field.name || '请选择',
        fieldClass
      }
    }
  });
  // 映射字段
  [
    fieldTypeConfig.mapping
  ].forEach(key => {
    formObj[key] = ( field: Recordable, options?: Recordable, quoteMappingConfig?: QuoteMapping ) => {
      const fieldClass = new Mapping(field, options)
      quoteMappingConfig?.setMappingFieldList(field)
      return {
        compName: customCompNameEnums.mapping,
        placeholder: field.name || '请选择',
        fieldClass
      }
    }
  });
  // 关联已有数据
  [
    fieldTypeConfig.associatedData
  ].forEach(key => {
    formObj[key] = ( field: Recordable ) => {
      return {
        compName: customCompNameEnums.associatedData,
        placeholder: field.prompt || '请选择'
      }
    }
  });
  // 关联已有数据
  [
    fieldTypeConfig.tags
  ].forEach(key => {
    formObj[key] = ( field: Recordable ) => {
      return {
        compName: customCompNameEnums.tags,
        placeholder: field.prompt || '请选择'
      }
    }
  });
  // 地址
  [
    fieldTypeConfig.address
  ].forEach(key => {
    formObj[key] = ( field: Recordable ) => {
      return {
        compName: customCompNameEnums.address,
        placeholder: field.prompt || '请选择'
      }
    }
  });
  // 手机号
  [
    fieldTypeConfig.phone
  ].forEach(key => {
    formObj[key] = ( field: Recordable ) => {
      return {
        compName: customCompNameEnums.phone,
        placeholder: field.prompt || '请输入'
      }
    }
  });
  // 金额
  [
    fieldTypeConfig.money
  ].forEach(key => {
    formObj[key] = ( field: Recordable ) => {
      return {
        compName: customCompNameEnums.money,
        placeholder: field.prompt || '请输入'
      }
    }
  });
  // 座机号
  [
    fieldTypeConfig.landline
  ].forEach(key => {
    formObj[key] = ( field: Recordable ) => {
      return {
        compName: customCompNameEnums.landline,
        placeholder: field.prompt || '请输入'
      }
    }
  });
  [
    fieldTypeConfig.personMultiple // 人员多选
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.personMultiple,
        val: field.val || []
      }
    }
  });
  [
    fieldTypeConfig.person // 人员单选
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.person,
        val: field.val || []
      }
    }
  });
  [
    fieldTypeConfig.multipleDepartment // 部门多选
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.multipleDepartment,
        val: field.val || []
      }
    }
  });
  [
    fieldTypeConfig.department // 部门单选
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.department,
        val: field.val || []
      }
    }
  });
  [
    fieldTypeConfig.tags // 标签
  ].forEach(key => {
    formObj[key] = ( field: Recordable) => {
      return {
        compName: customCompNameEnums.tags,
        val: field.val || []
      }
    }
  })

  return formObj
}

export const dealFormConfig = createFormStrategy(formObj)

const formValObj = {}
// 处理表单回显值和设置值
export function createFormValueStrategy (formValObj: Recordable) {
  [
    fieldTypeConfig.select // 下拉单选
  ].forEach(key => {
    formValObj[key] = {
      getValue: (field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        const value = !isNullOrUndefOrEmpty(form[fieldCode]) ? form[fieldCode] : ''
        if (fieldCode === 'sociaScop') {
          console.log(value, 'value123', form)
        }
        return value !== '' ? String(value) : '' // 统一处理成字符类型
      },
      setValue: (value: any[], field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        form[fieldCode] = value
      }
    }
  });
  [
    fieldTypeConfig.checkbox, // 复选框
    fieldTypeConfig.multipleChoice, // 下拉多选
    fieldTypeConfig.tags
  ].forEach(key => {
    formValObj[key] = {
      getValue: (field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        const value = form[fieldCode] || []
        // 返回数组
        return value ? (isString(value) ? value?.split?.(',') : value) : []
      },
      setValue: (value: any[], field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        // form[fieldCode] = value ? value.join(',') : []
        form[fieldCode] = value
      }
    }
  });
  [
    fieldTypeConfig.cascader // 级联
  ].forEach(key => {
    formValObj[key] = {
      getValue: (field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        const value = form[fieldCode] || []
        // 返回数组
        return value ? (isString(value) ? value?.split?.(',') : value) : []
      },
      setValue: (value: any[], field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        // form[fieldCode] = value ? value.join(',') : []
        form[fieldCode] = value
      }
    }
  });
  [
    fieldTypeConfig.picture // 图片
  ].forEach(key => {
    formValObj[key] = {
      getValue: (field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        const value = form[fieldCode] || []
        // 返回数组
        return value ? (isString(value) ? value?.split?.(',') : value) : []
      },
      setValue: (value: any[], field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        form[fieldCode] = value
      }
    }
  });
  [
    fieldTypeConfig.enclosure // 上传附件
  ].forEach(key => {
    formValObj[key] = {
      getValue: (field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        const value = form[fieldCode] || []
        // 返回数组
        return value ? (isString(value) ? value?.split?.(',') : value) : []
      },
      setValue: (value: any[], field: Recordable, form: Recordable) => {
        const { fieldCode } = field
        form[fieldCode] = value
      }
    }
  })
  return formValObj
}

export const dealFormValConfig = createFormValueStrategy(formValObj)

// 客户按钮权限
export const customerBtnCode = {
  // 详情
  DETAIL_EDIT: 'DETAIL_EDIT', // 编辑客户
  DETAIL_WRITE_FOLLOW_RECORD: 'DETAIL_WRITE_FOLLOW_RECORD', // 写跟进
  DETAIL_DELETE_FOLLOW_RECORD: 'DETAIL_DELETE_FOLLOW_RECORD', // 删除跟进记录
  DETAIL_COMMENTS: 'DETAIL_COMMENTS', // 评论
  DETAIL_DELETE_COMMENT: 'DETAIL_DELETE_COMMENT', // 删除评论
  DETAIL_SET_STAGE: 'DETAIL_SET_STAGE', // 设置阶段
  DETAIL_CALL: 'DETAIL_CALL', // 打电话
  DETAIL_SET_LEVEL: 'DETAIL_SET_LEVEL', // 设置等级
  DETAIL_EDIT_TAGS: 'DETAIL_EDIT_TAGS', // 编辑标签
  DETAIL_CONCERN: 'DETAIL_CONCERN', // 关注客户
  DETAIL_HANDOVER_OVER: 'DETAIL_HANDOVER_OVER', // 移交客户
  DETAIL_MOVE_INTO_OPEN_SEA: 'DETAIL_MOVE_INTO_OPEN_SEA', // 移入公海
  DETAIL_APPOINTMENT_VISIT: 'DETAIL_APPOINTMENT_VISIT', // 预约上门
  
  DETAIL_ADD_ASSISTANTS: 'DETAIL_ADD_ASSISTANTS', // 添加协助人
  DETAIL_REMOVE_ASSISTANTS: 'DETAIL_REMOVE_ASSISTANTS', // 移除协助人
  DETAIL_BASIC_INFORMATION: 'DETAIL_BASIC_INFORMATION', // 基本信息
  DETAIL_CONTACT: 'DETAIL_CONTACT', // 联系人
  DETAIL_ADD_CONTACT: 'DETAIL_ADD_CONTACT', // 添加联系人
  DETAIL_DELETE_CONTACT: 'DETAIL_DELETE_CONTACT', // 删除联系人
  DETAIL_CONNECT_CUSTOMERS: 'DETAIL_CONNECT_CUSTOMERS', // 关联客户
  DETAIL_ADD_CONNECT_CUSTOMERS: 'DETAIL_ADD_CONNECT_CUSTOMERS', // 添加关联客户
  DETAIL_REMOVE_CONNECT_CUSTOMERS: 'DETAIL_REMOVE_CONNECT_CUSTOMERS', // 移除关联客户

  DETAIL_CUSTOMER_ORDER_FEEDBACK: 'DETAIL_CUSTOMER_ORDER_FEEDBACK', // 客单反馈
  DETAIL_NUMBER_VERIFICATION: 'DETAIL_NUMBER_VERIFICATION', // 号码校验
  DETAIL_DELETE: 'DETAIL_DELETE', // 删除客户
  // 客户详情end
  // 客户列表start
  LIST_WRITE_FOLLOW_RECORD: 'LIST_WRITE_FOLLOW_RECORD', // 写跟进
  LIST_CALL: 'LIST_CALL', // 打电话
  LIST_SET_STAGE: 'LIST_SET_STAGE', // 设置阶段
  LIST_SET_LEVEL: 'LIST_SET_LEVEL', // 设置等级
  LIST_EDIT_TAGS: 'LIST_EDIT_TAGS', // 编辑标签
  // 客户列表end
  // 客户列表全局start
  CREATE: 'CREATE', // 创建客户
  SET_STAGE: 'SET_STAGE', // 设置阶段
  SET_LEVEL: 'SET_LEVEL', // 设置等级
  EDIT_TAGS: 'EDIT_TAGS', // 编辑标签
  CONCERN: 'CONCERN', // 关注客户
  IMPORT: 'IMPORT', // 导入客户
  EXPORT: 'EXPORT', // 导出客户
  HAND_OVER: 'HAND_OVER', // 移交客户
  MOVE_INTO_OPEN_SEA: 'MOVE_INTO_OPEN_SEA', // 移入公海
  BATCH_CUSTOMER_ORDER_FEEDBACK: 'BATCH_CUSTOMER_ORDER_FEEDBACK', // 客单反馈
  DELETE: 'DELETE', // 删除客户
  // 客户列表全局end
  // 列表批量
  BATCH_DELETE: 'BATCH_DELETE' // 批量删除
}
// 公海按钮权限
export const seaBtnCode = {
  // 详情start
  DETAIL_RECEIVE: 'DETAIL_RECEIVE', // 领取客户
  DETAIL_ASSIGNMENT: 'DETAIL_ASSIGNMENT', // 分配客户
  DETAIL_BASIC_INFORMATION: 'DETAIL_BASIC_INFORMATION', // 基本信息
  DETAIL_CONTACT: 'DETAIL_CONTACT', // 联系人
  DETAIL_CONNECT_CUSTOMERS: 'DETAIL_CONNECT_CUSTOMERS', // 关联客户
  DETAIL_DELETE: 'DETAIL_DELETE', // 删除客户
  // 详情end
  // 列表全局start
  IMPORT: 'IMPORT', // 导入客户
  RECEIVE: 'RECEIVE', // 领取客户
  ASSIGNMENT: 'ASSIGNMENT', // 分配客户
  TRANSFER: 'TRANSFER', // 转移客户
  DELETE: 'DELETE', // 删除客户
  EXPORT: 'EXPORT', // 导出客户
  // 列表全局end
  // 列表批量start
  BATCH_DELETE: 'BATCH_DELETE' // 批量删除
}
// 自定义应用按钮权限
export const customerPageBtnCode = {
  CREATE: 'CREATE', // 新建客户
  SAVE: 'SAVE', // 保存
  CANCEL: 'CANCEL', // 取消
  RECEIVE: 'RECEIVE', // 领取
  ASSIGNMENT: 'ASSIGNMENT', // 分配
  DELETE: 'DELETE', // 删除
  APPROVAL: 'APPROVAL', // 审批
  HEADER_CONFIG: 'HEADER_CONFIG' // 表头配置
}
// 员工自动分配
export const staffAutoAssignConfigBtnCode = {
  UPDATE: 'UPDATE', // 更新规则
  BATCH_NOTICE_SWITCH: 'BATCH_NOTICE_SWITCH', // 批量开启关闭通知
  ADD: 'ADD', // 新增
  SET_PRIORITY: 'SET_PRIORITY', // 设置优先级
  ENABLE_OR_DISABLE: 'ENABLE_OR_DISABLE', // 批量启用禁用
  DELETE: 'DELETE' // 删除
}

// 公海自动分配
export const HighSeaAutoAssignConfigBtnCode = {
  SET_PRIORITY: 'SET_PRIORITY', // 设置优先级
  UPDATE: 'UPDATE', // 更新
  DELETE: 'DELETE', // 删除
  ADD: 'ADD', // 新增
  BATCH_SWITCH: 'BATCH_SWITCH' // 批量启用禁用
}

// 自动回收规则
export const recyclingRulesConfigBtnCode = {
  UPDATE: 'UPDATE', // 更新规则
  SET: 'SET', // 设置
  ADD: 'ADD', // 新增
  ENABLE_OR_DISABLE: 'ENABLE_OR_DISABLE', // 启用禁用
  DELETE: 'DELETE', // 删除
  COPY: 'COPY' // 复制
}

// 标签配置
export const customerTagsConfigtnCode = {
  MOVE: 'MOVE', // 移动标签组
  ADD: 'ADD', // 新增
  ENABLE_OR_DISABLE: 'ENABLE_OR_DISABLE', // 启用禁用
  DELETE: 'DELETE', // 删除
  UPDATE: 'UPDATE' // 更新
}
// 客户阶段配置
export const customerStageConfigtnCode = {
  DELETE_ORDER: 'DELETE_ORDER', // 删除有客单
  MOVE: 'MOVE', // 移动标签组
  ADD: 'ADD', // 新增
  ENABLE_OR_DISABLE: 'ENABLE_OR_DISABLE', // 启用禁用
  DELETE: 'DELETE', // 删除
  UPDATE: 'UPDATE' // 更新
}

const createRuleStrategy = (strategys: any) => {
  // 数字,金额, 百分数
  [fieldTypeConfig.number, fieldTypeConfig.money, fieldTypeConfig.percentage].forEach(key => {
    strategys[key] = (data, onlyErrorMsg) => { // /^(\d+|\d+\.\d{1,2})$/, '请输入正确的数字，且最多输入两位小数' , /^(\d)*$/, '请输入正确的数字'
      const precisions = data.precisions || 0
      const str = `^(\\d+|\\d+\\.\\d{0,${precisions}})$`
      const reg = precisions > 0 ? new RegExp(str) : /^(\d)*$/
      const minValue = isNullOrUndefOrEmpty(data.minValue) ? '' : Number(data.minValue)
      const maxValue = isNullOrUndefOrEmpty(data.maxValue) ? '' : Number(data.maxValue)
      const validateFn = (rule, value, callback) => {
        if (isNullOrUndefOrEmpty(value)) { // 必填选项在外面判断
          callback()
        } else if (!reg.test(value)) {
          callback(new Error(`小数点不能超过${precisions}位`))
        } else if (!isNullOrUndefOrEmpty(minValue) && value < minValue) {
          callback(new Error(`不能低于最小值${minValue}`))
        } else if (!isNullOrUndefOrEmpty(maxValue) && value > maxValue) {
          callback(new Error(`不能超过最大值${maxValue}`))
        } else {
          callback()
        }
      }
      return onlyErrorMsg
        ? {
          reg,
          errMsg: `小数点不能超过${precisions}位`,
          validateFn: (value) => {
            let result = ''
            if (isNullOrUndefOrEmpty(value)) { // 必填选项在外面判断
              result = ''
            } else if (!reg.test(value)) {
              result = `小数点不能超过${precisions}位`
            } else if (!isNullOrUndefOrEmpty(minValue) && value < minValue) {
              result = `不能低于最小值${minValue}`
            } else if (!isNullOrUndefOrEmpty(maxValue) && value > maxValue) {
              result = `不能超过最大值${maxValue}`
            } else {
              result = ''
            }
            return result
          }
        }
        : { validator: validateFn, trigger: 'blur' }
    }
  });
  // 身份证
  [fieldTypeConfig.idCard].forEach(key => {
    strategys[key] = (data, onlyErrorMsg) => {
      const reg = idCardReg
      const validateFn = (rule, value, callback) => {
        if (isNullOrUndefOrEmpty(value)) { // 必填选项在外面判断
          callback()
        } else if (!reg.test(value)) {
          callback(new Error('请输入正确的身份证号'))
        } else {
          callback()
        }
      }
      return onlyErrorMsg
        ? { reg: reg, errMsg: '请输入正确的身份证号' }
        : { validator: validateFn, trigger: 'blur' }
    }
  });
  // 手机
  [fieldTypeConfig.phone].forEach(key => {
    strategys[key] = (data, onlyErrorMsg) => {
      const reg = /^1\d{10}$/
      const validateFn = (rule, value, callback) => {
        if (isNullOrUndefOrEmpty(value)) { // 必填选项在外面判断
          callback()
        } else if (!phoneIsDesensitization(value) && !reg.test(value)) {
          callback(new Error('请输入正确的手机号'))
        } else {
          callback()
        }
      }
      return onlyErrorMsg
        ? { reg, errMsg: '请输入正确的手机号' }
        : { validator: validateFn, trigger: 'blur' }
    }
  });
  // 邮箱
  [fieldTypeConfig.email].forEach(key => {
    strategys[key] = (data, onlyErrorMsg) => {
      const reg = emailReg
      const validateFn = (rule, value, callback) => {
        if (isNullOrUndefOrEmpty(value)) { // 必填选项在外面判断
          callback()
        } else if (!reg.test(value)) {
          callback(new Error('请输入正确的邮箱'))
        } else {
          callback()
        }
      }
      return onlyErrorMsg
        ? { reg, errMsg: '请输入正确的邮箱' }
        : { validator: validateFn, trigger: 'blur' }
    }
  })
  return strategys
}
export const ruleStrategy = createRuleStrategy({})

// 打开方式
export const openStyles = [
  {
    label: '打开弹窗',
    value: 'ALERT'
  },
  {
    label: '打开新标签页',
    value: 'NEW_TAB'
  }
]

// 按钮关联动作
export const btnActives = [
  {
    value: 'CREATOR_TABLE',
    label: '新增表单'
  },
  {
    value: 'UPDATE_NOW_TABLE_DATA',
    label: '更新当前表单数据'
  },
  {
    value: 'OPEN_URL',
    label: '打开新链接'
  }
  // {
  //   value: 'UPDATE_LINK_TABLE_DATA',
  //   label: '更新他表数据'
  // }
]

// 获取当前表单中处理结构之后的所有字段
export function getPositionsAllField (list: Recordable[]) {
  const arr: Recordable[] = []
  list.forEach(item => {
    if(item?.code === 'TABLE') {
      item?.children?.forEach(item => {
        if (item.modules) {
          item.modules.forEach(item => {
            if((item.moduleType === moduleType.subField || item.moduleType === moduleType.subList) && item.componentList) {
              item.componentList.forEach(item => {
                arr.push({ ...item.fieldBaseData.columnInfo })
              })
            }
          })
        }
      })
    } else {
      item?.modules?.forEach(item => {
        // 只取分栏和子表
        if((item.moduleType === moduleType.subField || item.moduleType === moduleType.subList) && item.componentList) {
          item.componentList.forEach(item => {
            arr.push({ ...item.fieldBaseData.columnInfo })
          })
        }
      })
    }
  })
  return arr
}

// 获取当前表单中处理结构之前的所有字段
export function getPositionsAllFieldForApi (list: Recordable[]) {
  const arr: Recordable[] = []
  list.forEach(item => {
    if(item.code === 'TABLE') {
      item.children.forEach(item => {
        if (item.modules) {
          item.modules.forEach(item => {
            if((item.moduleType === moduleType.subField || item.moduleType === moduleType.subList) && item.componentList) {
              item.componentList.forEach(item => {
                arr.push({ ...item.columnInfo })
              })
            }
          })
        }
      })
    } else {
      item.modules.forEach(item => {
        // 只取分栏和子表
        if((item.moduleType === moduleType.subField || item.moduleType === moduleType.subList) && item.componentList) {
          item.componentList.forEach(item => {
            arr.push({ ...item.columnInfo })
          })
        }
      })
    }
  })
  return arr
}

export const isQuote = fieldType =>
  fieldType === fieldTypeEnum.SYS_QUOTE ||
  fieldType === fieldTypeEnum.CUSTOM_QUOTE ||
  fieldType === fieldTypeEnum.SON_SYS_QUOTE ||
  fieldType === fieldTypeEnum.SON_CUSTOM_QUOTE
// 映射字段
export const isMapping = fieldType =>
  fieldType === fieldTypeEnum.SYS_MAPPING ||
  fieldType === fieldTypeEnum.CUSTOM_MAPPING ||
  fieldType === fieldTypeEnum.SON_SYS_MAPPING ||
  fieldType === fieldTypeEnum.SON_CUSTOM_MAPPING

// 是预定字段
export const isPreField = fieldType =>
  fieldType === fieldTypeEnum.ORIGINAL ||
  fieldType === fieldTypeEnum.SYS_QUOTE ||
  fieldType === fieldTypeEnum.SYS_MAPPING ||
  fieldType === fieldTypeEnum.SON_ORIGINAL_FIELD ||
  fieldType === fieldTypeEnum.SON_SYS_QUOTE ||
  fieldType === fieldTypeEnum.SON_SYS_MAPPING

// 获取当前表单中的所有分栏字段
export function getPositionsSubField (list: Recordable[]) {
  const arr: Recordable[] = []
  list.forEach(item => {
    if(item.code === 'TABLE') {
      item.children.forEach(item => {
        if (item.modules) {
          item.modules.forEach(item => {
            if((item.moduleType === moduleType.subField) && item.componentList) {
              item.componentList.forEach(item => {
                arr.push({ ...item.fieldBaseData.columnInfo })
              })
            }
          })
        }
      })
    } else {
      item.modules.forEach(item => {
        // 只取分栏和子表
        if((item.moduleType === moduleType.subField) && item.componentList) {
          item.componentList.forEach(item => {
            arr.push({ ...item.fieldBaseData.columnInfo })
          })
        }
      })
    }
  })
  return arr
}

// 是子表字段
export const isSubList = (fieldType: number) => (
  fieldType === fieldTypeEnum.SON_ORIGINAL_FIELD ||
  fieldType === fieldTypeEnum.SON_CUSTOM_FIELD ||
  fieldType === fieldTypeEnum.SON_SYS_QUOTE ||
  fieldType === fieldTypeEnum.SON_CUSTOM_QUOTE ||
  fieldType === fieldTypeEnum.SON_SYS_MAPPING ||
  fieldType === fieldTypeEnum.SON_CUSTOM_MAPPING)

// 是主表字段
export const isField = (fieldType: number) => (
  fieldType === fieldTypeEnum.ORIGINAL ||
  fieldType === fieldTypeEnum.SYS_QUOTE ||
  fieldType === fieldTypeEnum.CUSTOM_QUOTE ||
  fieldType === fieldTypeEnum.SYS_MAPPING ||
  fieldType === fieldTypeEnum.CUSTOM_MAPPING ||
  fieldType === fieldTypeEnum.CUSTOM_EXTEND
)

// 判断当前分栏是否在布局当中
export function findSubField (moduleSerial: string, list: Recordable[]) {
  let flag = false
  list.forEach(item => {
    if(item.code === 'TABLE') {
      item.children.forEach(item => {
        if (item.modules) {
          const cur = item.modules.find(module => module.moduleSerial === moduleSerial)
          if (cur) {
            flag = true
          }
        }
      })
    } else {
      const cur = item.modules.find(module => module.moduleSerial === moduleSerial)
      if (cur) {
        flag = true
      }
    }
  })
  return flag
}