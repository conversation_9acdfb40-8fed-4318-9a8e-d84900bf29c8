export interface ShowValueData {
  id?: string;
  realName?: string;
  departmentVO?: {
    id?: string
  };
  [x: string]: any;
}

export interface UserVOList {
  id: string;
  realName?: string;
  username?: string;
  departmentVO: DeComponentTreeVO[];
  jobStatus?: Recordable
}

export interface DeComponentTreeVO {
  id: string;
  name?: string; // 部门名称
  parentId?: string;
  children?: Recordable[];
  userVOList?: UserVOList[];
}

export interface RoleVOList {
  userVOList?: UserVOList[];
  id: string;
  name: string
}

export interface ComponentData {
  deComponentTreeVO?: DeComponentTreeVO[]; // 组织架构
  userVOList?: UserVOList[]; // 人员集合
  userResignVOList?: UserVOList[]; // 离职人员集合
  roleVOList?: RoleVOList[]; // 角色集合
  showUserList?: UserVOList[]; // 回显人员集合
}
export interface BasicProps {
  isIncludeChild?: boolean; // 父部门不包含子部门员工
  custom?: boolean; // 是否自定义触发内容
  showValueData?: UserVOList[]; // 回显的值
  visible?: boolean;
  paramsData?: any; // 接口需要的参数
  activeName?: string; // panelName
  configuration?: boolean; // 是否需要参数管理配置
  dynamicParameters?: Recordable; // 动态参数
  data?: ComponentData;// 数据
  api?: (...arg: any) => Promise<any>;// 获取数据接口
  placeholder?: string;
  visibleBeforeFn?: () => Promise<any>; // 显示组件之前
  placement?: string;
  disabled?: boolean;
  menuId?: string; // 菜单权限
  [key: string]: any
}
