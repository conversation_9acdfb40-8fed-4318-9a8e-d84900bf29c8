<template>
  <div class="base-table-page" :class="{'reHeight': getProps.showPagination}">
    <el-table
      class="custom-table"
      :data="getBindValues.data"
      border
      style="width: 100%;height: 100%"
      header-cell-class-name="custom-header"
      @selection-change="selectionChangeFn"
    >
      <el-table-column type="selection" width="55" v-if="getProps.showSelection" />
      <template v-for="i in getBindValues.columns" :key="i.key">
        <el-table-column v-bind="getColumnBindingValue(i)">
          <template
            v-if="i.render"
            v-slot="scope"
          >
            <TableColumnRender
              :data="scope.row"
              :column-config="i"
              :render-fn="i.render"
            />
          </template>
          <template
            v-else-if="i.slot"
            v-slot="{row, $index}"
          >
            <slot
              :name="i.slot"
              :row="row"
              :index="$index"
              :column="i"
            />
          </template>
          <template
            v-else
            v-slot="scope"
          >
            {{ dealShowVal(scope.row[i.key]) }}
          </template>
        </el-table-column>
      </template>
    </el-table>

  </div>
  <div class="pagination-box" v-if="getProps.showPagination">
    <div>
      <slot name="leftSlot" />
    </div>
    <el-pagination
      :class="['custom-pagination', {'custom-pagination-mini': config.layout.shrink}]"
      :current-page="getPaginationInfo.pageNum"
      :page-size="getPaginationInfo.pageSize"
      :page-sizes="pageSizes"
      :layout="config.layout.shrink ? 'total, prev, next, jumper' : 'total, sizes, prev, pager, next, jumper'"
      :total="total"
      @size-change="handleSizeChangeFn"
      @current-change="handleCurrentChangeFn"
    />
  </div>

</template>
<script lang="ts" setup>
import './style.scss'
import { computed, unref, ref, toRaw } from 'vue'
import TableColumnRender from './TableColumnRender.vue'
import { useTableData } from './hooks/useTableData'
import type { BasicTableProps } from './type'
import { useColumns } from './hooks/useColumns'
import type { TableActionType } from './type'
import { usePagination } from './hooks/usePagination'
import { isNullOrUndefOrEmpty } from '@/utils/is'
import { ColumnType } from '@/types/table'
import { useSection }from './hooks/useSection'
import { TableData } from './type'

import { useConfig } from '@/stores/modules/config'
const emit = defineEmits(['register', 'selectionChange'])
const innerPropsRef = ref<Partial<BasicTableProps>>()
const searchInfo = ref({})
const config = useConfig()
const getProps = computed(() => {
  return { showPagination: true, searchInfo: { ...unref(searchInfo) }, ...unref(innerPropsRef) } as BasicTableProps
})

// 分页hooks
const { getPaginationInfo, handleSizeChange, handleCurrentChange, pageSizes } = usePagination(getProps)
// 表格数据hooks
const { tableData, reload, total } = useTableData(getProps, { getPaginationInfo })
// 表头hooks
const { getColumns } = useColumns(getProps)
// 选择项hooks
const { selectionChange, sectionList } = useSection()
const getBindValues = computed(() => {
  const data = unref(tableData)
  console.log(getColumns, 'getColumn111s会执行吗')
  return {
    data,
    columns: unref(getColumns)
  }
})

function selectionChangeFn (data:any) {
  selectionChange(data)
  emit('selectionChange')
}

function getColumnBindingValue (i: ColumnType){
  const { fixed, title, key, width } = i
  const data: Recordable = {
    label: title,
    prop: key,
    minWidth: 100
  }
  fixed && (data.fixed = fixed)
  width && (data.width = width)
  return data
}

function setProps (props: Partial<BasicTableProps>) {
  innerPropsRef.value = { ...props, ...unref(innerPropsRef) }
}
// 设置请求参数
function setSearchInfo (data: Recordable) {
  searchInfo.value = data
}
// 获取已选中项
function getSectionData (): Recordable[] {
  return JSON.parse(JSON.stringify(unref(sectionList)))
}
// 获取表格数据
function getTableData ():TableData{
  return {
    total: unref(total),
    list: JSON.parse(JSON.stringify(unref(tableData)))
  }
}

const tableAction: TableActionType = {
  setProps,
  setSearchInfo,
  reload,
  getSectionData,
  getTableData
}
emit('register', tableAction)

function handleSizeChangeFn (val: number) {
  handleSizeChange(val)
  reload()
}
function handleCurrentChangeFn (val: number) {
  handleCurrentChange(val)
  reload()
}
function dealShowVal (val: any) {
  if (isNullOrUndefOrEmpty(val)) {
    return '-'
  }
  return val
}
</script>
