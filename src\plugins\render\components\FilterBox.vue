<template>
  <div class="filter_box">
    <el-checkbox-group
      v-model="data.option.values"
      class="tw-flex tw-flex-col filter_container"
      @change="handleChange"
    >
      <template v-for="item in list" :key="item.value">
        <el-checkbox :label="item.value" :value="item.value" />
      </template>
    </el-checkbox-group>
    <div class="filter_footer">
      <RxkButton type="primary" @click="confirmEvent">确定</RxkButton>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PropType, ref, reactive } from 'vue'
import { VxeGlobalRendererHandles } from 'vxe-table'
const props = defineProps({
  params: {
    type: Object as PropType<VxeGlobalRendererHandles.RenderFilterParams>
  }
})
const data = reactive({
  option: null as any
})
const list = ref([])
const handleChange = (value: any) => {
  const { params } = props
  const { option } = data
  if (params) {
    const { $panel } = params
    $panel.changeOption(null, true, option)
  }
}
const confirmEvent = () => {
  const { params } = props
  if (params) {
    const { $panel } = params
    $panel.confirmFilter()
  }
}
const load = () => {
  const { params } = props
  if (params) {
    const { column } = params
    const option = column.filters[0]
    data.option = option
    list.value = option.data
  }
}
load()
</script>
<style lang="scss" scoped>
.filter_box {
  padding: 8px 0 8px 12px;

  display: flex;
  flex-direction: column;
  .filter_container {
    height: calc(32px * 8);
    overflow: scroll;
    :deep(.el-checkbox) {
      line-height: 32px;
      flex-shrink: 0;
    }
  }

  .filter_footer {
    border-top: 1px solid #ebeef5;
    padding-top: 8px;
    text-align: right;
    padding-right: 12px;
    margin-left: -12px;
  }
}
</style>
