<template>
  <div class="main-user-manage">
    <div class="left-content">
      <div class="tlt">用户管理</div>
      <rxk-button
        v-auth="'DEPARTMENT_ADD'"
        class="add-btn"
        plain
        @click="addDepartment"
      >添加部门</rxk-button>
      <RxkTree
        name="userManagementDepartmentTree"
        highlight-current
        :data="state.dataSource"
        node-key="id"
        :default-expanded-keys="treeExpandKeys"
        :current-node-key="checkedKey"
        :props="{ label: 'name' }"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node" @mouseover="showOptions(data, true)" @mouseleave="showOptions(data, false)">
            <span>{{ data.name }}</span>
            <div @click.stop>
              <el-dropdown placement="bottom-start"
                           trigger="click"
                           @command="handleCommand"
                           @visible-change="handleChange($event, data)"
                           v-if="node.level !== 1">
                <div class="custom-drop iconfont icon icon-hoverno" v-show="data.showIcon"/>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="getAuth('DEPARTMENT_ADD')" :command="{type: 'add', data}">
                      <a> 添加子部门 </a>
                    </el-dropdown-item>
                    <el-dropdown-item  v-if="getAuth('DEPARTMENT_UPDATE')" :command="{type: 'edit', data}">
                      <a> 编辑 </a>
                    </el-dropdown-item>
                    <el-dropdown-item  v-if="getAuth('DEPARTMENT_DELETE')" :command="{type: 'remove', data}">
                      <a> 删除 </a>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </span>
        </template>
      </RxkTree>
    </div>
    <EditDepartmentDialog
      v-if="showDialog"
      :tree-data="state.dataSource"
      :departmentInfo="currentDepartment"
      :parentId="parentId"
      @handleClose="() => (showDialog = false)"
      @handleSubmit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, nextTick } from 'vue'
import EditDepartmentDialog from './EditDepartmentDialog/index.vue'
// import type Node from 'element-plus/es/components/tree/src/model/node'
import { getDepartmentTree } from '@/apis/userManage'
import { ElMessage, ElMessageBox } from 'element-plus'
import { deleteDepartment } from '@/apis/departmentManage'
import { RxkTree } from '@/components/common/RxkTree'
import { useResource } from '@/hooks/useResource'
import { usePageCache } from '@/hooks/usePageCache'
const { getAuth } = useResource()
const { getCache, updateCache } = usePageCache()
const treeExpandKeys = ref<string[]>(getCache()?.treeExpendKeys || [])

const emits = defineEmits(['handleNodeClick', 'changeDepartment'])

interface Tree {
  id: number
  label?: string
  children?: Tree[]
  [key: string]: any
}

const showDialog = ref(false)
const state:any = reactive({
  dataSource: [],
  selectTreeNode: {}
})
const checkedKey = ref(getCache()?.treeCheckKey || '')

const currentDepartment = ref<any>({})

const parentId = ref<any>(undefined)

onMounted(async () => {
  await getDepartment()
})

const getDepartment = async () => {
  state.dataSource = await getDepartmentTree()
}

const handleNodeClick = (data: Tree) => {
  checkedKey.value = data.id
  updateCache({ treeCheckKey: data.id })
  emits('handleNodeClick', data)
}

const handleCommand = ({ type, data }:any) => {
  console.log(data)
  if(type === 'add'){
    appendDepartment(data)
  }
  if(type === 'edit'){
    editDepartment(data)
  }
  if(type === 'remove'){
    remove(data)
  }
}

const addDepartment = () => {
  currentDepartment.value = null
  parentId.value = undefined
  showDialog.value = true
}

const appendDepartment = (data: Tree) => {
  parentId.value = data.id
  currentDepartment.value = null
  showDialog.value = true
}

const editDepartment = (data: Tree) => {
  console.log(data)
  currentDepartment.value = data
  parentId.value = undefined
  showDialog.value = true
}

const remove = async (data: Tree) => {
  try {
    await ElMessageBox.confirm('删除后数据无法恢复，确定要删除吗？', '警告', { type: 'error' })
    await deleteDepartment(data.id + '')
    await getDepartment()
    ElMessage.success('删除成功')
  } catch {
    // ElMessage.error('删除失败')
  }
}

const handleSubmit = () => {
  getDepartment()
  emits('changeDepartment')
}

function showOptions (data: Recordable, show: boolean){
  if(!show && data.visibleDropdown) return
  data.showIcon = show
}

function handleChange (val: boolean, data:Recordable){
  data.visibleDropdown = val
  nextTick(() => {
    data.showIcon = val
  })
}
</script>

<style scoped lang="scss">
@import url(./index.scss);
</style>
