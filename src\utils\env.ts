import pkg from '../../package.json'
// const pkg = require("../../package.json");

/**
 * 获取当前构建模式
 * 本地开发代码运行的是development
 * 打包production，test，dev，self, pressure根据打不同的包展示
 * */
export function getEnvMode (): string {
  return import.meta.env.MODE
}

/**
 * @author：张胜
 * @desc：是否处于正在开发的阶段
 * */
export function isDevMode (): boolean {
  return import.meta.env.ENV
}

/**
 * @author：张胜
 * @desc：是否处于生产环境阶段
 * */
export function isProdMode (): boolean {
  import.meta.env.VITE_APP_TITLE
  return import.meta.env.PROD
}

/**
 * @author：张胜
 * @desc：获取环境变量文件配置的变量
 * */
export function getAppEnvConfig (): ViteEnv {
  const ENV = import.meta.env 
  const {
    VITE_PORT,
    VITE_TITLE,
    VITE_DROP_CONSOLE,
    VITE_GLOB_APP_TITLE
  } = ENV
  return {
    VITE_PORT,
    VITE_TITLE,
    VITE_DROP_CONSOLE,
    VITE_GLOB_APP_TITLE
  }
}

/**
 * @author：张胜
 * @desc：获取本地缓存所需的前缀key
 * 如'RXK__TEST__1.0.0__'
 * */
export function getStoragePrefix () {
  return `${getCommonPrefix()}__${pkg.version}__`.toUpperCase()
}
/**
 * @author：张胜
 * @desc：获取通用项目名称+环境变量前缀
 * 如 'RXK__TEST'
 * */
export function getCommonPrefix () {
  const { VITE_TITLE } = getAppEnvConfig()
  return `${VITE_TITLE}__${getEnvMode()}`.toUpperCase()
}
