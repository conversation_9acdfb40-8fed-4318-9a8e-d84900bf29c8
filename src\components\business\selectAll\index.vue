<template>
  <el-select
    v-model="selectVal"
    multiple
    :props="propsVal"
    :clearable="clearable"
    :disabled="disabled"
    filterable
    :filter-method="filterFunction"
    collapse-tags
    :placeholder="placeholder"
    :key="slectAllKey"
    :max-collapse-tags="1"
    size="default"
    style="width: 100%;"
    @change="handleChange"
    @visible-change="visibleChangeFn"
  >
    <el-option v-for="item in options"
               :key="item[propsVal.value]"
               :value="item[propsVal.value]"
               :label="item[propsVal.label]"/>
    <template #header>
      <el-checkbox
        v-model="checkAll"
        :indeterminate="indeterminate"
        :disabled="checkAllDisabled"
        @change="handleCheckAll"
      >
        全选
      </el-checkbox>
    </template>
  </el-select>
</template>

<script lang="ts" setup>
import { ref, watch, computed, defineEmits, defineProps, onMounted, type PropType } from 'vue'
import type { CheckboxValueType } from 'element-plus'
import { randomNum } from '@/utils/util'
const checkAll = ref(false)
const indeterminate = ref(false)

const props = defineProps({
  list: {
    type: Array as PropType<Array<Recordable>>,
    required: true
  },
  modelValue: {
    type: Array as PropType<CheckboxValueType[]>
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  propsObj: {
    type: Object as PropType<{label: string, value: string}>,
    default: () => ({ label: 'label', value: 'value' })
  }
})
const propsVal = ref({ ...props.propsObj })
const slectAllKey = ref(randomNum(1, 99999))
const options = ref<Array<Recordable>>(props.list)
const emit = defineEmits(['update:modelValue', 'change'])
function handleChange (val: any) {
  emit('change', val)
}
const selectVal = computed({
  get () {
    return props.modelValue || []
  },
  set (value) {
    emit('update:modelValue', value)
  }
})
const checkAllDisabled = computed(() => !options.value.length)
watch(() => selectVal.value, (val) => {
  checkBoxHandler(val)
}, {
  immediate: true
})

function containsAll (arr: string[], subset: string[]) {
  return subset?.every(item => arr?.includes(item))
}
function contains (arr: string[], subset: string[]) {
  return subset?.some(item => arr?.includes(item))
}
function checkBoxHandler (val: CheckboxValueType[]) {
  const allValList = options.value.map(item => item[props.propsObj.value])
  if (val?.length === 0) {
    checkAll.value = false
    indeterminate.value = false
  } else if (!options.value.length) {
    checkAll.value = false
    indeterminate.value = false
  } else if (containsAll(val as string[], allValList)) {
    checkAll.value = true
    indeterminate.value = false
  } else if (!contains(val as string[], allValList)){
    indeterminate.value = false
  } else {
    checkAll.value = false
    indeterminate.value = true
  }
}

function visibleChangeFn (val: boolean) {
  if (!val) {
    slectAllKey.value += 1
    options.value = props.list
  }
}

watch(() => props.list, (val) => {
  options.value = val
  // selectVal.value = selectVal.value.filter(i => options.value.findIndex(k => k[props.propsObj.value] === i) !== -1)
  if (val.length) {
    checkBoxHandler(selectVal.value)
  }
})

const handleCheckAll = (val: CheckboxValueType) => {
  indeterminate.value = false
  if (val) {
    const allValList = options.value.map((_) => _[props.propsObj.value])
    selectVal.value = [...new Set([...selectVal.value, ...allValList])]
  } else {
    const allValList = options.value.map((_) => _[props.propsObj.value])
    selectVal.value = selectVal.value.filter(i => !allValList.includes(i))
  }
  setTimeout(() => {
    handleChange(selectVal.value)
  }, 0)
}

function filterFunction (keyword: string) {
  if (keyword) {
    options.value = props.list.filter(item => item[props.propsObj.label].includes(keyword))
  } else {
    options.value = props.list
  }
  checkBoxHandler(selectVal.value)
}

onMounted(() => {
  if (!props.modelValue) {
    selectVal.value = []
  }
})
</script>

<style lang="scss">
.select-all-checkbox {
  padding: 0 10px;
  border-bottom: 1px solid var(--el-border-color-light);
}
</style>