CanvasRenderingContext2D.prototype.wrapText = function (
  text,
  x,
  y,
  lineHeight
) {
  if (
    typeof text !== 'string' ||
    typeof x !== 'number' ||
    typeof y !== 'number'
  ) {
    return
  }

  const context = this
  const canvas = context.canvas

  if (typeof lineHeight === 'undefined') {
    lineHeight =
      (canvas && parseInt(window.getComputedStyle(canvas).lineHeight)) ||
      parseInt(window.getComputedStyle(document.body).lineHeight)
  }

  const arrText = text.split(',')
  context.fillText(arrText[0], x, y)
  y += lineHeight
  context.fillText(arrText[1], x, y)
}

const watermark = {}

let maskDiv = null

const setWatermark = (str, dom) => {
  const id = dom

  if (document.getElementById(id) !== null && maskDiv) {
    removeMaskDiv()
  }

  const can = document.createElement('canvas')
  can.width = 400
  can.height = 200

  const cans = can.getContext('2d')
  cans.rotate((-20 * Math.PI) / 180)
  cans.font = '14px Vedana'
  cans.fillStyle = 'rgba(180, 180, 180, 0.25)'
  cans.textAlign = 'left'
  cans.textBaseline = 'Middle'
  cans.wrapText(str, 0, can.height / 1.2)

  maskDiv = document.createElement('div')
  maskDiv.id = id
  maskDiv.style.pointerEvents = 'none'
  maskDiv.style.inset = '50px'
  maskDiv.style.position = 'fixed'
  maskDiv.style.zIndex = '100000'
  // maskDiv.style.width = document.getElementById(dom)?.clientWidth + 200 + 'px'
  maskDiv.style.height = document.getElementById(dom)?.clientHeight + 'px'
  maskDiv.style.background =
    'url(' + can.toDataURL('image/png') + ') left top repeat'
  document.getElementById(dom)?.appendChild(maskDiv)
  return id
}

// const Monitor = (str, dom) => {
//   const body = document.getElementById(dom);
//   const options = {
//     childList: true
//   };
//   const callback = (mutations, observer) => {
//     if (mutations[0].removedNodes[0] && mutations[0].removedNodes[0].id === dom) {
//       setWatermark(str, dom);
//     }
//   };
//   const observer = new MutationObserver(callback);
//   observer.observe(body, options);
// };

const removeMaskDiv = () => {
  maskDiv?.remove()
}

watermark.set = (str, dom) => {
  setWatermark(str, dom)
  // Monitor(str, dom);
  // window.onresize = () => {
  //   setWatermark(str, dom);
  // };
}

export default watermark
