// 对接类型枚举
export const dockingTypeEnum = [
  { label: '按我方文档', value: 0 },
  { label: '按对方文档', value: 1 }
]

// 加密方式枚举
export const 	filterTypeEnum = [
  { label: 'md5加密', value: 0 },
  { label: '7位掩码', value: 1 },
  { label: '8位掩码', value: 2 },
  { label: '9位掩码', value: 3 },
  { label: '不撞库', value: 4 },
  { label: '特殊撞库', value: 5 }
]

export const supportFilterNoticeEnum = [
  { label: '支持', value: 1 },
  { label: '不支持', value: 0 }
]