<template>
  <div class="personnelSelection">
    <!-- 自定义触发内容 -->
    <div v-if="custom" style="width: inherit" ref="customRef">
      <slot name="custom" />
    </div>
    <div v-else style="width: inherit">
      <!-- 默认触发内容 -->
      <el-select
        style="width: 100%"
        v-model="showValue"
        ref="selectRef"
        clearable
        @visible-change="visibleChange"
        @focus="focus"
        @clear="clearData"
        :disabled="disabled"
        v-bind="$attrs"
        placeholder="请选择"
      >
        <el-option
          v-for="(item, index) in personelTree.data"
          :key="index"
          :label="item.realName"
          :value="item.id"
        />
      </el-select>
    </div>

    <el-popover
      :visible="custom ? innerVisible : undefined"
      :virtual-ref="virtualRef"
      ref="popoverRef"
      trigger="click"
      title=""
      popper-class="personnelSelectionPopper"
      width="600"
      destroy-on-close
      :popper-options="{
        modifiers: [{ enabled: true }],
        strategy: 'fixed',
        placement: 'auto'
      }"
      virtual-triggering
    >
      <SelectPanel
        @registerPanel="registerPanel"
        @update="update"
        @close="close"
        @confirm="confirm"
      >
        <template #dynamic>
          <slot name="dynamic"/>
        </template>
      </SelectPanel>
    </el-popover>
  </div>
</template>

<script lang="ts" setup>
import { ref, unref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import SelectPanel from './selectPanel.vue'
import type { BasicProps, UserVOList } from './type'
import { isFunc } from '@/utils/is'
import { usePanel } from './hooks/usePanel'

const emit = defineEmits([
  'update:visible',
  'sure',
  'update:showValueData',
  'getUserList'
])
const personelTree = reactive<any>({
  data: {}
})

const popoverRef = ref()
const selectRef = ref()
const customRef = ref()
const virtualRef = ref()
const showPanel = ref<boolean>(false)
const showValue = ref<string>('')
const loadNum = ref<number>(0)

const innerVisible = computed({
  get: () => props.visible,
  set: (visible: boolean) => emit('update:visible', visible)
})

const props = withDefaults(defineProps<BasicProps>(), {
  custom: false,
  value: '',
  visible: false,
  activeName: 'members',
  paramsData: {},
  configuration: false,
  placement: 'bottom',
  disabled: false
})

const [
  registerPanel,
  {
    init,
    updateValue,
    getCheckedUser,
    getContainChildren,
    getParameter,
    reload,
    handleCheckedValue
  }
] = usePanel()

watch(() => props, () => {
  console.log(props.showValueData, '组件已渲染BB', loadNum.value, props.visible)
  if (!props.disabled) {
    virtualRef.value = props.custom ? customRef.value : selectRef.value
  } else {
    unref(popoverRef)?.hide?.()
    virtualRef.value = ''
  }
  const fn = () => {
    nextTick(() => {
      init(unref(props))
      loadNum.value++
    })
  }
  if (loadNum.value === 0) {
    if ((props.showValueData && props.showValueData.length > 0) || props.data || props.visible) {
      fn()
    }
  } else {
    if (props.data && !props.api) {
      fn()
    } else {
      handleCheckedValue(props.showValueData || [])
    }
  }
}, {
  deep: true,
  immediate: true
})

function focus () {
  selectRef.value.blur()
}

async function visibleChange () {
  selectRef.value.blur()
  if (props.visibleBeforeFn && isFunc(props.visibleBeforeFn)) {
    const flag = await props.visibleBeforeFn()
    if (!flag) {
      close()
    }
  } else {
    if (loadNum.value === 0) {
      init(props)
      loadNum.value++
    } else {
      reload(props)
    }
  }
  unref(popoverRef).popperRef?.delayHide?.()
  showPanel.value = true
}

function close () {
  if (props.custom) {
    innerVisible.value = false
  } else {
    unref(popoverRef).hide?.()
  }
  showPanel.value = false
}

function getData () {
  return {
    userInfoList: getCheckedUser(),
    dynamicParameters: getParameter(),
    containChildren: getContainChildren()
  }
}

function getUser () {
  return {
    userInfoList: personelTree.data
  }
}

function confirm () {
  personelTree.data = getCheckedUser() || []
  showValue.value = getCheckedUser()?.[0]?.id || ''
  emit('sure', getData())
  close()
}

function clearData () {
  updateValue([], 'clear')
  emit('sure', getData())
}

function update (data: UserVOList[]) {
  console.log('updetelaaa', data)
  personelTree.data = data || []
  showValue.value = data?.[0]?.id || ''
  emit('getUserList', data)
}

defineExpose({
  getUser
})

onMounted(async () => {
  if (!props.disabled) {
    virtualRef.value = props.custom ? customRef.value : selectRef.value
  }
})
</script>

<style scoped lang="scss"></style>

<style lang="scss">
.personnelSelectionPopper {
  padding: 0 !important;
  overflow-x: hidden;
  overflow-y: auto;
  height: auto;
}
</style>
