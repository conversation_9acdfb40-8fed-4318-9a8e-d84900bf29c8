import { type DirectiveBinding } from 'vue'
import { useFocusWithin } from '@vueuse/core'

/**
 * @description: 用于<el-input>聚焦状态显示明文，失焦状态脱敏显示
 */
export default {
  beforeUpdate (el: any, binding: DirectiveBinding) {
    // 需特殊处理的脱敏值
    const desensitizeValue = binding.value
    if (!desensitizeValue) return

    // 获取当前元素的聚焦状态
    const { focused } = useFocusWithin(el)
    // 获取真实的input原生组件
    const targetEle = el.getElementsByTagName('input')?.[0]

    // 该生命周期内，仅在初始化更新记录初始值
    if (!el.cacheValue && !el.staticInitValue) {
      el.staticInitValue = targetEle?.value
      el.cacheValue = targetEle?.value
    }
    
    // 值未发生过变更，才进行显示处理(兼容直接点击关闭按钮)
    if (targetEle?.value && el.cacheValue === el.staticInitValue) {
      targetEle.value = focused.value ? el.cacheValue : desensitizeValue
    }
  },
  mounted (el: any) {
    el.input = () => {
      const targetEle = el.getElementsByTagName('input')?.[0]
      el.cacheValue = targetEle?.value
    }

    el.addEventListener('input', el.input)
  },
  beforeUnmount (el: any) {
    el.removeEventListener('input', el.input)
  }
}