export class IdWorker {
  private twepoch: bigint
  private workerIdBits: bigint
  private datacenterIdBits: bigint
  private sequenceBits: bigint
  private maxWorkerId: bigint
  private maxDatacenterId: bigint
  private sequenceMask: bigint
  private workerIdShift: bigint
  private datacenterIdShift: bigint
  private timestampLeftShift: bigint
  private workerId: bigint
  private datacenterId: bigint
  private sequence: bigint
  private lastTimestamp: bigint

  constructor (workerId: bigint, datacenterId: bigint) {
    this.twepoch = 1420041600000n
    this.workerIdBits = 5n
    this.datacenterIdBits = 5n
    this.sequenceBits = 12n
    this.maxWorkerId = (1n << this.workerIdBits) - 1n
    this.maxDatacenterId = (1n << this.datacenterIdBits) - 1n
    this.sequenceMask = (1n << this.sequenceBits) - 1n
    this.workerIdShift = this.sequenceBits
    this.datacenterIdShift = this.sequenceBits + this.workerIdBits
    this.timestampLeftShift = this.sequenceBits + this.workerIdBits + this.datacenterIdBits

    if (workerId > this.maxWorkerId || workerId < 0n) {
      throw new Error(`Worker ID can't be greater than ${this.maxWorkerId} or less than 0`)
    }
    if (datacenterId > this.maxDatacenterId || datacenterId < 0n) {
      throw new Error(`Datacenter ID can't be greater than ${this.maxDatacenterId} or less than 0`)
    }

    this.workerId = workerId
    this.datacenterId = datacenterId
    this.sequence = 0n
    this.lastTimestamp = -1n
  }

  private nextId (): bigint {
    let timestamp = this.timeGen()

    if (timestamp < this.lastTimestamp) {
      throw new Error(`Clock moved backwards. Refusing to generate id for ${this.lastTimestamp - timestamp} milliseconds`)
    }

    if (timestamp === this.lastTimestamp) {
      this.sequence = (this.sequence + 1n) & this.sequenceMask
      if (this.sequence === 0n) {
        timestamp = this.tilNextMillis(this.lastTimestamp)
      }
    } else {
      this.sequence = 0n
    }

    this.lastTimestamp = timestamp

    return ((timestamp - this.twepoch) << this.timestampLeftShift) |
            (this.datacenterId << this.datacenterIdShift) |
            (this.workerId << this.workerIdShift) |
            this.sequence
  }

  private tilNextMillis (lastTimestamp: bigint): bigint {
    let timestamp = this.timeGen()
    while (timestamp <= lastTimestamp) {
      timestamp = this.timeGen()
    }
    return timestamp
  }

  private timeGen (): bigint {
    return BigInt(Date.now())
  }

  public getUniqueId (): bigint {
    return this.nextId()
  }

  public toString (): string {
    return `${this.workerId}${this.datacenterId}`
  }
}

// Example usage:
// const idWorker = new IdWorker(1n, 1n)
// console.log(idWorker.getUniqueId().toString()) // 输出唯一ID
// console.log(idWorker.toString(), 'LLOOO') // 输出workerId和datacenterId的组合