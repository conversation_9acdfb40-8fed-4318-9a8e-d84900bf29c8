import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

/**
 * @name 部门管理模块
 */

// * 新增部门
export const addDepartment = (data: any) => {
  return request.post('/admin/department/add', data, jsonHeaderConfig)
}

// * 编辑部门
export const updateDepartment = (data: any) => {
  return request.post('/admin/department/update', data, jsonHeaderConfig)
}

// * 删除部门
export const deleteDepartment = (id: string ) => {
  return request.post('/admin/department/delete', { id })
}


// * 根据部门ID查询当前部门主管
export const getDepartmentManager = (departmentId: string) => {
  return request.get('/admin/department/manager', { departmentId })
}
