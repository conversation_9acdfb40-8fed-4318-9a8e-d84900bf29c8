<template>
  <div class="channel-management-list">
    <SearchFilter @register="registerSetting" @search="search" />
    <div class="btn-box">
      <RxkButton v-if="getAuth('CREATE')"
                 @click="handleAdd"
                 type="primary"
      >添加</RxkButton
      >
      <el-button
        v-if="!isAllView"
        @click="handleIsViewChannelName(true)"
        type="text"
        :icon="View"
      >显示渠道名</el-button
      >
      <el-button
        v-else
        @click="handleIsViewChannelName(false)"
        type="text"
        :icon="Hide"
      >隐藏渠道名</el-button
      >
    </div>
    <div class="table-box">
      <RxkVTable @register="registerTable" @getRefTableData="getRefTableData" />
    </div>
    <CommonDrawer @refreshTable="reload" ref="commonDrawerRef" />
    <ChannelGroupDialog
      v-if="state.channelGroupDialogVisible"
      v-model="state.channelGroupDialogVisible"
      ref="channelGroupDialogRef"
    />
  </div>
</template>

<script setup lang="tsx">
import { View, Hide } from '@element-plus/icons-vue'
import type { Operation } from './type'
import { ref, unref, reactive, onMounted, nextTick } from 'vue'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import ChannelGroupDialog from './components/channelGroupDialog.vue'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import CommonDrawer from '@/views/channelManagement/list/components/commonDrawer.vue'
import type { ColumnType } from '@/types/table'
import { fetchGetCarChannelList } from '@/apis/carlife'
import { channelTypeOption, doMainOption, statusOption } from '@/enums/carlife'
import { useResource } from '@/hooks/useResource'
import { dataDetailListFormat } from '@/apis/system'

const { getAuth } = useResource()

const searchInfo: { model: Record<string, any> } = reactive({
  model: {}
})
const state = reactive({
  channelGroupDialogVisible: false
})
const commonDrawerRef = ref()

onMounted(() => {
  setSearchInfo(searchInfo)
  reload()
})

const searchFormData = ref<FormSchema[]>([
  {
    key: 'id',
    component: 'Input',
    val: '',
    fieldName: '渠道ID'
  },
  {
    key: 'name',
    component: 'Input',
    val: '',
    fieldName: '渠道名称'
  },
  {
    fieldName: '渠道类型',
    component: 'Select',
    key: 'type',
    val: [],
    options: channelTypeOption,
    componentProps: {
      clearable: true,
      multiple: true
    }
  }
])
const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})

const channelGroupDialogRef = ref()
const handleLink = (row: any) => {
  state.channelGroupDialogVisible = true

  nextTick(() =>
    channelGroupDialogRef.value.init({
      channelId: row.id,
      extractionCode: row.extractionCode,
      settleType: row.settleType
    })
  )
}

const columns: ColumnType[] = [
  { key: 'id', title: '渠道ID', width: 200 },
  {
    key: 'name',
    title: '渠道名称',
    width: 200,
    render: ({ cellRefData }) => {
      return (
        <div class="channel-name-view">
          {cellRefData.isView ? (
            <el-tooltip
              class="item"
              effect="dark"
              content={cellRefData.name}
              placement="top-start"
            >
              <span class="channel-name"> {cellRefData.name}</span>
            </el-tooltip>
          ) : (
            <span> ********</span>
          )}

          <el-button
            class="icon-view-btn"
            type="text"
            icon={cellRefData.isView ? Hide : View}
            onClick={() => {
              cellRefData.isView = !cellRefData.isView
            }}
          ></el-button>
        </div>
      )
    }
  },
  {
    key: 'domainSubject',
    title: '域名主体',
    render: ({ cellData }) => {
      return (
        <span>
          {doMainOption.find(
            (item: any) => item.value === cellData.domainSubject
          )?.label || '-'}
        </span>
      )
    }
  },
  {
    key: 'promoteLink',
    title: '推广链接',
    width: 450
  },
  {
    key: 'type',
    title: '渠道类型',
    width: 120,
    render: ({ cellData }) => {
      return (
        <span>
          {channelTypeOption.find((item: any) => item.value === cellData.type)
          ?.label || '-'}
        </span>
      )
    }
  },
  {
    key: 'h5Type',
    title: 'H5类型',
    width: 120,
    syncData: {
      api: dataDetailListFormat,
      value: 'value',
      label: 'label',
      params: {
        groupCode: 'channel_h5_type'
      }
    }
  },
  {
    key: 'status',
    title: '状态',
    render: ({ cellData }) => {
      return (
        <span>
          {
            statusOption.find((item: any) => item.value === cellData.status)
            ?.label
          }
        </span>
      )
    }
  },
  { key: 'createTime', title: '创建时间', width: 200 },
  { key: 'updateTime', title: '更新时间', width: 200 },
  {
    key: 'operate',
    title: '操作',
    fixed: 'right',
    width: 220,
    render: ({ cellData }) => {
      return (
        <div>
          <el-button type="text" onClick={() => handleDetails(cellData)}>
            详情
          </el-button>
          {getAuth('LINK') ? (
            <el-button type="text" onClick={() => handleLink(cellData)}>
              渠道链接
            </el-button>
          ) : (
            ''
          )}
        </div>
      )
    }
  }
]

const [registerTable, { reload, setSearchInfo }] = useTable({
  api: fetchGetCarChannelList,
  columns: columns,
  searchInfo: searchInfo,
  immediate: false // 是否立刻请求
})

const search = (val: { [k: string]: any }) => {
  Object.assign(searchInfo.model, val)

  searchInfo.model.type = val.type.join(',')
  setSearchInfo(searchInfo)
  reload()
}

const handleAdd = () => {
  const data: Operation = {
    isAdd: true
  }
  commonDrawerRef.value?.init(data)
}

const isAllView = ref(false)
const handleIsViewChannelName = (isView: boolean) => {
  isAllView.value = isView
  // 重新加载数据
  reload()
}

const getRefTableData = ({ tableData }: Recordable) => {
  tableData.value.forEach((item: any) => {
    item.isView = isAllView.value
  })
}

const handleDetails = (cellData: any) => {
  const data: Operation = {
    isAdd: false,
    id: cellData.id
  }
  commonDrawerRef.value?.init(data)
}
</script>

<style lang="scss" scoped>
.channel-management-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  .btn-box {
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 16px;
  }
  :deep(.channel-name-view) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;
    .channel-name {
      display: block;
      max-width: 180px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  :deep(.icon-view-btn) {
    position: absolute;
    right: 20px;
  }
  .table-box {
    flex: 1;
    overflow: auto;
  }
}
</style>
