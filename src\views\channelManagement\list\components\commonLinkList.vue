<template>
  <div class="channel_link_list">
    <el-image class="link-logo" :src="LinkLogo" fit="scale-down" />
    <SearchFilter
      class="link-search-filter"
      @register="registerSetting"
      isRefresh
      @search="search"
    >
      <RxkButton v-if="showExportBtn" @click="handleExport">导出数据</RxkButton>
    </SearchFilter>
    <div class="table-box">
      <RxkVTable @register="registerTable"/>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed, reactive } from 'vue'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { excelExport } from '@/utils/index'
import LinkLogo from '@/assets/images/link-logo.png'
import { RxkButton } from '@/components/common/RxkButton'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import {
  channelMerchantInfoFLowCount,
  channelMerchantCountExport
} from '@/apis/channelManage'

import timeUtils from '@/utils/libs/time'

const props = defineProps({
  tableProConfig: {
    type: Object
  },
  api: {
    type: Function,
    default: channelMerchantInfoFLowCount
  },
  type: {
    type: Number,
    default: 1 // 渠道商链接类型（0：单个渠道，1：渠道组）
  },
  exportType: {
    type: Number,
    default: 1
  },
  settleType: {
    type: String,
    default: '1'
  },
  encodeId: {
    type: String,
    default: ''
  },
  showCityVisitorStatistics: {
    type: Boolean,
    default: false
  },
  showExportBtn: { // 是否显示导出按钮
    type: Boolean,
    default: true
  },
  exportApi: { // 导出api
    type: String,
    default: channelMerchantCountExport
  }
})

const [transStartTime, transEndTime] = timeUtils.transTypeToTime(60, 'yyyy-MM-dd')
const searchInfo: { model: Record<string, any> } = reactive({
  model: {
    startDate: transStartTime,
    endDate: transEndTime,
    channelId: props.encodeId
  }
})

const [registerSetting] = useSearch({
  schemas: props.tableProConfig.searchFormData
})

const columns = computed(() => {
  // 如果是"CPS结算"则字段展示为"结算金额"
  if([2].includes(Number(props.settleType))){
    props.tableProConfig.columns.forEach(item => {
      if(item.key === 'consumeAmount'){
        item.title = '结算金额'
      }
    })
  }

  return props.tableProConfig.columns
})

const [
  registerTable,
  { reload, setSearchInfo, getSearchInfo, getPaginationData }
] = useTable({
  api: props.api,
  columns,
  searchInfo: searchInfo,
  pagination: {
    pageNum: 1,
    pageSize: 10
  }, 
  immediate: true, // 是否立刻请求
  customTableConfig: false
})

const search = (val:{ [k: string]: any }) => {
  Object.assign(searchInfo.model, val)

  searchInfo.model.startDate = val.timeRange?.[0] ?? ''
  searchInfo.model.endDate = val.timeRange?.[1] ?? ''
  delete searchInfo.model.timeRange

  setSearchInfo(searchInfo)
  reload()
}

const handleExport = () => {
  const params = { ...getSearchInfo(), ...getPaginationData() }
  excelExport(props!.exportApi, params, 'POST')
}

</script>

<style lang="scss" scoped>
.channel_link_list {
  box-sizing: border-box;
  padding: 32px 84px 0 84px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .btn-box {
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 16px;
  }
  .table-box {
    flex: 1;
    overflow: auto;
  }
  .link-logo {
    padding-left: 16px;
    width: 140px;
    height: 32px;
  }
  .link-search-filter {
    display: flex;
    padding: 32px 16px 4px;
  }
  :deep(.custom-search-item) {
    width: 100% !important;
  }
  :deep(.custom-form) {
    width: 400px !important;
    flex: none;
  }
  :deep(.btn-box) {
    flex: 1;
    justify-content: space-between;
    .el-space {
      width: 100%;
      position: relative;
    }
    .el-space__item:last-child {
      position: absolute;
      right: 0;
    }
  }
  :deep(.operate-btn){
    padding: 0;
  }
}
</style>
