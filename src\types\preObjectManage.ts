import { fieldTypeConfig } from '@/enums/preObjectManage'
import type { List } from '@/types/common'
/**
 * @author：张胜
 * @desc：对象字段
 * @todo
 * */
export interface BaseDataType {
    id?: number | string;
    lineIndex?: number; // 第几行
    listIndex?: number;// 第几列
    groupInfoId?: number| string;
    positionId?: number | string;
    tableColumnId?: number | string;
    tableInfoId?: number | string;
    columnInfo: {
        id?: number | string;
        tableInfoId?: number | string;
        tableType?: number | string;
        tableField?: string;
        tableAlias?: string;
        tableName?: string;
        fieldName?: string;
        fieldCode?: string;
        fieldProperty:string;
        fieldType?: number | string;
        fieldLead?: string;
        dataType?: string;
        defaultData?: string;
        defaultMark?: number;
        defaultValue?: string;
        precisions?: number;
        proportion?: number;
        tips?: string;
        descriptions?: string;
        setting?: string;
        minValue?: string;
        maxValue?: string;
        minLength?: string;
        maxLength?: string;
        lengthFlag?: boolean;
        importFlag?: boolean;
        exportFlag?: boolean;
        queryFlag?: boolean;
        showFlag?: boolean;
        editFlag?: boolean;
        enableFlag?: boolean;
        writeFlag?: boolean;
        columnFlag?: boolean;
        mappingFlag?: boolean;
        detailEditFlag?: boolean;
        listEditFlag?: boolean;
        repeatFlag?: boolean;
        positionFlag?: boolean;
        branchFlag?: boolean;
        assistFlag?: boolean;
        validFlag?: boolean;
        encryptType?: boolean;
        authorityFlag?: boolean;
        coverFlag?: boolean;
        rangeFlag?: boolean;
        order?: number;
        titleOrder?: number;
    }
}

/**
 * @author：张胜
 * @desc：10 预定义  20预定义
 * */
export type tableColumnType = 10 | 20

/**
 * @author：张胜
 * @desc：模块类型(0-分栏,10-子表,20-容器组)
 * */
export type ModuleType = 0 | 10 | 20

/**
 * @author：张胜
 * @desc：控件库属性
 * */
export interface FieldLibrary {
    value: FieldPropertyType; // 字段类型
    label: string; // 字段名称
    icon: string; // 字段图标
    dataClass: any; // 类
    layoutShow: boolean; // 是否展示
    dropSize: 'large' | 'default' | 'small' | 'all';// 字段拖拽停靠高度
    widthSize: 'halfLine' | 'fullLine'; // 一半、整行
}

/**
 * @author：张胜
 * @desc：预定义字段类的选项
 * */
export interface OptionType {
    tableColumnType?: number;
    isTable?: boolean;
    moduleSerial?: string;
}

export interface ValidNameType {
    fieldBaseData: BaseDataType,
    msg?: string;
    success?: boolean;
}

export interface libraryListType {
    title: string;
    children: FieldLibrary[];
}

export type FieldPropertyType = ValueOf<typeof fieldTypeConfig>

export interface SubTableType {
    name: string;
    id: string;
    list: DragLayoutType[]
}

export interface conditionsType {
    columnId: string | number;
    condition: string;
    groupId: string | number;
    leftColumnId: string | number;
    leftCondition: string | number;
    leftTableCode: string | number;
    leftType: string | number;
    leftValue: string | number;
    rightColumnId: string | number;
    rightColumnSerial: string | number;
    rightCondition: string | number;
    rightTableCode: string | number;
    rightType: string | number;
    rightValue: string | number;
    tableCode: string | number;
}

export interface conditionListType {
    conditions: conditionsType[];
    exteriorCondition: string;
    groupId: string | number;
    interiorCondition: string;
    sceneId: string | number;
    seriesNo: string;
    tableCode: string | number;
    type: number;
}

export interface conditionInfoType {
    columnId?: string | number;
    conditionList?:conditionListType[];
    conditiontableCodes?: string | number;
    seriesNo?: string;
    tableCode?: string | number;
}
export interface defaultValueConfigType {
    columnId?: string | number;
    columnSerial?: string;
    conditionInfo?: conditionInfoType;
    connectColumnId?: string | number;
    connectColumnSerial?: string;
    connectTableCode?: string | number;
    connectType?: number;
    tableCode?: string | number;
}

export interface columnQuoteInfoType {
    columnSerial?: string;
    columnId?: string | number;
    conditionInfo?: conditionInfoType;
    id?: string | number;
    group?: string;
    quoteColumnSerial?: string;
    quoteTableCode?: string;
    tableCode?: string | number;
    unionDialogColumnSerials?: string;
    unionQueryColumnSerials?: string;
    alert?: boolean
}

/**
 * @author：张胜
 * @desc：table容器tab的类型
 * */
export interface tableContainerStorageResultListType {
    id?: string | number;
    tableInfoId?: string | number;
    containerInfoId?: string | number;
    name?: string;
    sort?: number;
    positionList?: DragLayoutType[],
    tId?: string;
}

// @todo
export interface DragLayoutType {
    moduleType: number;
    type?: tableColumnType;
    id?: number | string;
    positionId?: string | number; // 布局id
    tableInfoId?: string | number; // 表单id
    containerStorageId?:string | number;// 容器id
    name?: string; // 字段名称
    desc?: string; // 描述信息
    writeFlag?:boolean;
    addEditFlag?: boolean;
    detailEditFlag?: boolean;
    compileEditFlag?: boolean;
    listEditFlag?: boolean;
    createDataFlag?: boolean;
    deleteDataFlag?: boolean;
    updateDataFlag?: boolean;
    tableContainerStorageResultList?: tableContainerStorageResultListType[];
    columnList?: BaseDataType[]
    tId?: string;
}

// 组件操作结构
export interface componentOperateListType {
    tableCode?: number;
    moduleId?: number;
    componentId?: number;
    moduleType?: number;
    defineType?: number;
    code?: string;
    value?: boolean;
}
export interface extraDataListType{
    tenantId: string;
    tableCode: string;
    columnSerials: string;
    departmentId: string | number;
    personId: string | number;
}
export interface columnConfigType {
    tableCode: string;
    columnSerial: string;
    selectedShow: number;
    containChildren: number;
    acquireCurrent: number;
    acquireCurrentFather: number;
    linkColumnSerial: string;
    extraDataList: extraDataListType[]
}

// 字段结构
export interface FieldType {
    id?: number | string;
    tableCode?: string;
    tableField?: string;
    tableAlias?: string;
    tableName?: string;
    code?: string;
    defineType?: number;
    name?: string;
    prompt?: string;
    desc?: string;
    setting?: string;
    fieldCode?: string;
    fieldType?: number | string;
    fieldIdentity?: string;
    dataType?: string;
    defaultMark?: number;
    defaultValue?: string;
    precisions?: number | string;
    unit?: string;
    minValue?: string;
    maxValue?: string;
    minLength?: string;
    maxLength?: string;
    encryptType?: number;
    columnOperateList?: componentOperateListType[],
    conditionList?: any[],
    defaultValueConfigInfo?: defaultValueConfigType,
    columnQuoteInfo?: columnQuoteInfoType,
    fieldSerial?: string;
    columnDataList?: Recordable[];
    dataMark?: number
    columnConfig?:columnConfigType
}

// 组件结构

export interface ComponentType {
    id?: number;
    tableCode?: number;
    positionId?: number;
    containerId?: number;
    moduleId?: number;
    columnId?: number;
    // code?: string;
    // moduleType?: number;
    // defineType?: number;
    // name?: string;
    // prompt?: string;
    // desc?: string;
    // setting?: string;
    columnSerial?: string;
    layout?: any;
    columnInfo: FieldType,

    // componentOperateList?: componentOperateListType[]
    tId?: string;
}
/**
 * @author：张胜
 * @desc：拖拽之后的字段属性
 * */
export interface dragFieldData {
    fieldBaseData: ComponentType;
    fieldProperty: FieldPropertyType; // 字段类型
    fieldTypeName: string; // 字段名称
    fieldTypeOwnTableType: tableColumnType; // 字段属于哪个类型下 (自定义,预定义), 分栏,子表
    tId: string; // 前端自定义的唯一id
    isTable: boolean;
    limitNum?: number;
}

// 按钮结构
export interface buttonListType {
    containerId?: number;
    desc?: string;
    eventCode?: string;
    eventConfig?: string;
    eventId?: number;
    layout?: string;
    moduleId?: number;
    name?: string;
    positionId?: number;
    tableCode?: number;
}
// 分栏，子表结构
export interface SubType {
    id?: number;
    tableCode?: number;
    positionId?: number;
    containerId?: number;
    title?: string;
    desc?: string;
    moduleType?: number;
    defineType?: number;
    layout?: any;
    group?: string;
    componentList?:dragFieldData[];
    buttonList?: buttonListType[];
    columnTableCode?: string;
    tId?: string;
    operateList?: Recordable[];
}

// 容器结构
export interface PositionsType {
    id?: number;
    positionId?: number;
    parentId?: number;
    title?: string;
    code?: string;
    defineType?: number;
    layout?: Recordable;
    children: PositionsType[],
    modules: SubType[],
    tId?: string;
    tableCode?: number;
}

export interface CustomOperateListTYpe {
    title: string;
    children: List[];
}