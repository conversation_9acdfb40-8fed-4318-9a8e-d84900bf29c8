import { fileURLToPath, URL } from 'node:url'
import { defineAppConfig } from './build'
const fs = require('fs')
if (process.env.NODE_ENV !== 'development') {
  const test = {
    versionTime: new Date().getTime() + '',
    refresh: 'operation' // 不刷新'no'，手动刷新'operation', 强制刷新'forced'
  }
  fs.writeFile('./public/version.json', JSON.stringify(test), () => {
    console.log('新版本号生成成功')
  })
}

/**
 * @desc: 要改的在这个文件，其他的在build中
 */
export default defineAppConfig({
  overrides: {
    base: '/',
    server: {
      proxy: {
        '/api': {
          // target: 'http://192.168.2.235:9101', // 何杰
          // target: 'http://192.168.2.25:9101/', // 李斌
          // target: 'http://192.168.30.64:8090', // 吴日清
          // target: 'http://192.168.30.66:9040', // 何江奎
          // target: 'http://192.168.30.241:9040', // 李学文
          // target: 'http://192.168.2.104:8090', // 周瑜
          // target: 'http://192.168.2.235:8090', // 何杰
          // target: 'https://dev-sentinel.youxinsign.com/gw', // dev环境
          target: 'https://tc.haoxincd.cn', // 测试环境
          // target: 'http://192.168.30.207:9101', // 李龙
          // target: 'http://192.168.30.110:9040', // 洪庆东
          // target: 'http://192.168.2.223:8090', // 谢希杰
          // target: 'http://192.168.2.242:8090', // 李常凯
          changeOrigin: true, // 允许websockets跨域
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    // css: {
    //   preprocessorOptions: {
    //     scss: {
    //       additionalData: '@use "@/assets/styles/element.scss" as *;'
    //     }
    //   }
    // },
    resolve: {
      // 设置别名
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    }
  }
})
