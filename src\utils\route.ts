import { usePermissionStore } from '@/stores/modules/permission'
import { filterCurrentRoute } from '@/utils/util'
// 跳过权限验证的页面（工作台、详情页、链接页）
const noAuthRoutes = ['/', '/dashboard', '/link', '/securityPasswordSet']

// 如果有菜单但是没有访问权限就跳503
export function checkPath (to: any, next: any, replace: boolean = false) {
  const { menuList } = usePermissionStore()
  const route: any = filterCurrentRoute(menuList as any, to.path) || {}
  if (route.path || noAuthRoutes.includes(to.path)) {
    replace ? next({ ...to, replace: true }) : next()
  } else {
    next('/502')
  }
}
