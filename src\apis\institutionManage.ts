import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'

const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

// 获取机构列表表头配置以及筛选条件配置
export const getInstitutionOption = (parmas:any) => {
  return request.get('/admin/search/fieldJson', parmas, jsonHeaderConfig, { ignoreCancelToken: true })
}

export const setInstitutionOption = (data: Recordable) => {
  return request.post('/admin/search/saveFieldJson', data, jsonHeaderConfig)
}
