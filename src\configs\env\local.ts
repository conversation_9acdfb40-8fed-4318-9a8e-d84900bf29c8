// 本地环境
import type { GlobalEnv } from '../type'

export const localEnv: GlobalEnv = {
  baseUrl: '/api', // 接口请求域名
  apiPrefix: '', // 接口请求前缀,一般对应于后端的服务
  uploadApi: 'https://dev-gw.rongxk.com/crm-report/file/upload', // 图片上传地址
  videoUplodApi: 'https://dev-gw.rongxk.com/crm-report/file/upload', // 视频上传地址
  fileDownloadApi: '/crm/file/download', // 文件下载地址
  websocketPath: 'http://**************:52999', // websoket地址
  imgBaseUrl: 'https://dev-file.rongxk.com', // 图片访问路径
  protocolPath: '', // 协议路径
  mapKey: 'de9b8f465f36876c10e3411e75c53062', // 高德地图的key(公司)
  signKey:
    'NmFhYjEzYWFlOTFlN2Q2ZmJiZDAzOGYzODY4ZDdiNzg3OWZkYzYzOTg2M2RjM2RhOGNkYmE1YTRhNzMxYjIwMA==', // 签名的key
  aes: 'enc-', // 加密解密前缀
  secretKey: 'GQDTXGf7EZuCqwNG', // 解密用的key
  offset: '8GUUxywnlW0v9hk6',
  isNeedAes: false, // 是否需要加密
  sip_url: '@**************', // sip:1008@************
  sip_socket_url: 'wss://test-yocwebrtc.youxinsign.com:7443',
  sip_session_timmers_expires: '150', // 会话计时器间隔
  sip_register_expires: '300', // 注册到期时间
  agreementPath: 'https://agreement.youxinsign.com', // 协议的地址,默认都写成线上的看一个地方即可

}
