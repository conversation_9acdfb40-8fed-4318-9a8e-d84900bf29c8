<template>
  <el-drawer
    :model-value="drawerVisible"
    :title="title"
    :direction="direction"
    :size="size"
    :append-to-body="true"
    :destroy-on-close="true"
    @close="handleClose"
    class="rxkDrawer"
    v-bind="$attrs"
  >
    <template #header>
      <slot name="header" />
    </template>
    <template #default>
      <div v-loading="fullLoading" class="rxkDrawer-content" style="height: 100%">
        <slot name="content" />
      </div>
    </template>
    <template v-if="footer" #footer>
      <div class="rxkDrawer-footer">
        <div class="rxkDrawer-footer-left">
          <slot name="footerBefore" />
          <RxkButton
            v-if="showPrimaryBtn"
            type="primary"
            :isLoading="btnLoading"
            @click="handleConfirm"
          >{{ primaryText }}</RxkButton
          >
          <RxkButton plain
                     :isLoading="btnLoading"
                     @click="handleClose"
                     v-if="showCancleBtn">{{
                       cancelText
                     }}</RxkButton>
        </div>
        <slot name="footerAfter" />
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { computed, useAttrs } from 'vue'
import type { Props } from './types'
const emit = defineEmits<Emit>()

interface Emit {
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', fn: Fn): void
  (e: 'close', fn: Fn): void
  (e: 'handleBeforeClose'): void
}

const drawerVisible = computed({
  get: () => props.visible,
  set: (visible: boolean) => emit('update:visible', visible)
})

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  direction: 'rtl',
  fullLoading: false,
  btnLoading: true,
  footer: true,
  showCancleBtn: true,
  primaryText: '提交',
  cancelText: '取消',
  title: '标题',
  size: 480,
  showPrimaryBtn: true
})

/**
 * 点击确认
 */
const handleConfirm = (fn: Fn) => {
  emit('confirm', () => {
    fn?.()
  })
}

/**
 * 点击取消
 */
const handleClose = (fn: Fn) => {
  drawerVisible.value = false
  console.log('close', props.fullLoading)
  emit('close', () => {
    fn?.()
  })
}
</script>

<style lang="scss">
@import url('./index.scss');
</style>
