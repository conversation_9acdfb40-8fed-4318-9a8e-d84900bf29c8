import { ref, unref } from 'vue'
import type { BasicProps, DeComponentTreeVO } from '../type'
export interface PanelActionType{
  init: (props:BasicProps, fn?: Fn) => void;
  getCheckedDep: () => DeComponentTreeVO[];
  updateValue: (data: DeComponentTreeVO[], type?: string) => void;
  getParameter: () => Recordable | undefined;
  reload: (props:BasicProps, fn?: Fn) => void;
  handleCheckedValue: (data: UserVOList[]) => void;
}
export function usePanel ():[(instance: PanelActionType) => void, PanelActionType] {
  const panelRef = ref<Nullable<PanelActionType>>(null)
  function registerPanel (instance: PanelActionType, fn?:Fn){
    if (instance === unref(panelRef)) return
    panelRef.value = instance
    fn && fn()
    // props && instance.init(props, fn)
  }
  function getPanelInstance (): PanelActionType {
    const panel = unref(panelRef.value)
    return panel as PanelActionType
  }
  const methods: PanelActionType = {
    init: (props:BasicProps, fn?: Fn) => {
      getPanelInstance()?.init?.(unref(props), fn)
    },
    getCheckedDep: () => {
      return getPanelInstance().getCheckedDep()
    },
    updateValue: (data: DeComponentTreeVO[], type?: string) => {
      getPanelInstance()?.updateValue(data, type)
    },
    getParameter: () => {
      return getPanelInstance().getParameter()
    },
    reload: (props:BasicProps, fn?: Fn) => {
      getPanelInstance()?.reload?.(unref(props), fn)
    },
    handleCheckedValue: (data: UserVOList[]) => {
      getPanelInstance()?.handleCheckedValue(data)
    }
  }
  return [registerPanel, methods]
}