<template>
  <div class="dynamicParameters">
    <div class="dynamicParameters-item">
      <el-radio v-model="dynamicParameters" :label="1">当前新增/修改数据的用户</el-radio>
    </div>
    <div class="dynamicParameters-item">
      <el-radio v-model="dynamicParameters" :label="2">当前新增/修改数据的用户的直属领导</el-radio>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, unref, watch, type PropType } from 'vue'

const emit = defineEmits(['update:dynamicParameters', 'updateParameter'])
const props = defineProps({
  data: {
    type: Number as PropType<Recordable>,
    default: () => {}
  }
})
const dynamicParameters = computed({
  get: () => unref(props.data),
  set: (newValue) => {
    emit('update:dynamicParameters', newValue )
  }
})

watch(dynamicParameters, (val) => {
  emit('updateParameter', val)
})
</script>

<style lang="scss" scoped>
.dynamicParameters {
  height: 100%;
  padding: 16px 0 0 16px;
  &-item {
    display: block;
    width: 100%;
  }
}
</style>