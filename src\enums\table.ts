export const defaultPagination = {
  pageSize: 20,
  currentPage: 1
}

// table唯一表示枚举
export enum TableAppCodeEnum {
  // 用户管理
  leaveUser = 'leaveUser', // 离职用户
  // 机构列表弹窗
  orgBaseInfo = 'orgBaseInfo', // 机构基本信息
  orgBranch = 'orgBranch', // 分公司
  orgAuthAccount = 'orgAuthAccount', // 授权账号
  orgTradeRecord = 'orgTradeRecord', // 交易记录
  // 短信管理 // 通信中心 暂不使用 等产品确认
  smsVerificationCode = 'smsVerificationCode', // 验证码
  smsNotice = 'smsNotice', // 通知
  smsMarketing = 'smsMarketing', // 营销
  // 发送记录
  statistics = 'statistics', // 统计
  detail = 'detail', // 明细
  // 黑白名单 // 通信中心 暂不使用 等产品确认
  blackList = 'blackList', // 黑名单
  whiteList = 'whiteList', // 白名单
  // 仪表盘
    // 线路分析 下
      lineAnalysisLine = 'lineAnalysisLine', // 线路分析
      lineAnalysisCompare = 'lineAnalysisCompare', // 线路对比分析
      lineAnalysisOrg = 'lineAnalysisOrg', // 机构使用路线分析
  accountAnalysis = 'accountAnalysis', // 账户分析
  seatAnalysis = 'seatAnalysis', // 坐席分析
  // 线路管理
  simCardCount = 'simCardCount', // sim卡数量
  sipCardCount = 'sipCardCount', // sip卡数量
  accountCount = 'accountCount', // 账户数量
  // 订单审批
  approvalPending = 'approvalPending', // 待审批
  approvalApproved = 'approvalApproved', // 已审批
  approvalOverTime = 'approvalOverTime', // 已超时审批
  // 商品管理
  goods = 'goods', // 商品
    specification = 'specification', // 规格与价格
    selectGoods = 'selectGoods', // 选择商品
  rule = 'rule', // 投放规则
  // 产品管理
  approvalRecord = 'approvalRecord', // 审核记录
}
