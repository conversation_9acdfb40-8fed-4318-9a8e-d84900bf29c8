import { defineConfig, mergeConfig, type UserConfig } from 'vite'
import { createPlugins } from '../plugins'
import { commonConfig } from './common'
import { getAppEnvConfig } from '../utils'
const { execSync } = require('child_process')
// 获取git信息
const __GIT_HASH__ = JSON.stringify(execSync('git rev-parse HEAD').toString().trim().slice(0, 8))
interface DefineOptions {
  overrides?: UserConfig
}
/**
 * @desc: 定义vite配置
 * */
export function defineAppConfig (defineOptions: DefineOptions) {
  const { overrides = {} } = defineOptions
  return defineConfig(({ mode }) => {
    const env = getAppEnvConfig(mode)
    console.log(env.VITE_DROP_CONSOLE, 'VITE_DROP_CONSOLE')
    const __BUILD_TIME__ = JSON.stringify(new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }))
    const plugins = createPlugins(env)
    const defaultConfig = {
      plugins,
      build: {
        outDir: 'dist',
        assetsDir: 'assets',
        chunkSizeWarningLimit: 500, // 大小警告的限制
        // 分包
        rollupOptions: {
          output: {
            manualChunks: (filePath: string) => {
              if (filePath.includes('node_modules')) {
                return 'vendor'
              }
            },
            entryFileNames: 'js/[name]-[hash].js',
            chunkFileNames: 'js/[name]-[hash].js',
            assetFileNames: '[ext]/[name]-[hash].[ext]'
          }
        }
      },
      esbuild: {
        drop: env.VITE_DROP_CONSOLE ? ['console', 'debugger'] : []
      },
      define: {
        __GIT_HASH__,
        __BUILD_TIME__
      }
    }
    const config = mergeConfig(defaultConfig, commonConfig(mode))
    return mergeConfig(config, overrides)
  })
}
