/**
 * 去除字符串中的所有空格
 * */
export function clearStrAllSpace (str: string): string {
  return str.replace(/\s*/g, '')
}

/**
 * 去掉字符串头部空格
 * */
export function clearStrFirstSpace (str: string): string {
  return str.replace(/^\s*/g, '')
}

/**
 * 去掉字符串尾部空格
 * */
export function clearStrLastSpace (str: string): string {
  return str.replace(/\s*$/g, '')
}

/**
 * 0~2,保留2位有效小数， 可以输入0  1  2  1.99 2.00
 * */
export function validateInterestRate (str: string): boolean {
  const reg = /(^[0-2]{1}$)|(^[0-1]{1}[\.]{1}[0-9]{1,2}$)/
  // 先将字符串转成数字，输入1. => 1, 2.0 => 2， 2.00 => 2
  return reg.test(Number(str).toString()) // test接收的是字符串
}

export const emailReg = /^\w+@[a-zA-Z0-9]+((\.[a-z0-9A-Z]{1,})+)$/
// 身份证正则
export const idCardReg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/

// 手机号是否脱敏
export const phoneIsDesensitization = str => {
  const value = String(str) || ''
  return value.indexOf('****') > -1
}