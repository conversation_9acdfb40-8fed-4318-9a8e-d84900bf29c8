<template>
  <div class="isTxt" v-if="isTxt">
    {{innerValue || '-'}}
  </div>
  <template v-else>
    <RxkInput
      style="width: 30%;"
      v-model="innerValue1"
      :type="renderConfig.inputType"
      :disabled="renderConfig.disabled"
      placeholder="请输入区号"
      :maxlength="4"
      @input="handleInput1"
      oninput="value=value.replace(/[^\d]/g,'')"
    />
    <RxkInput
      style="width: 70%;padding-left: 8px"
      v-model="innerValue2"
      type="text"
      :disabled="renderConfig.disabled"
      :placeholder="renderConfig.placeholder"
      :maxlength="8"
      @input="handleInput2"
      oninput="value=value.replace(/[^\d]/g,'')"
    />
  </template>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkInput } from '@/components/common/RxkInput'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})
const innerValue1 = ref('')
const innerValue2 = ref('')
function handleInput1 () {
  innerValue.value = `${innerValue1.value}-${innerValue2.value}`
}

function handleInput2 () {
  innerValue.value = `${innerValue1.value}-${innerValue2.value}`
}
onMounted(() => {
  if (innerValue.value) {
    const arr = innerValue.value.split('-')
    innerValue1.value = arr[0] || ''
    innerValue2.value = arr[1] || ''
  }
})
</script>