import { type ColumnProps } from '@/components/common/RxkProTable/interface'
import { type User } from '@/apis/interface/userManage'
import { type SearchConfig } from '@/components/common/RxkSearchBar/type'
import { ref, reactive } from 'vue'

export const stateEnum = [
  { label: '启用', value: 'ENABLED' },
  { label: '禁用', value: 'DISABLED' }
]

export const Config = reactive<SearchConfig[]>([
  {
    code: 'username',
    type: 'input',
    value: '',
    label: '员工姓名',
    placeholder: '请输入'
  },
  {
    code: 'mobile',
    type: 'input',
    value: '',
    label: '联系电话',
    placeholder: '请输入'
  },
])

// 表格配置项
export const columns: ColumnProps<User.ResUserList>[] = [
  { prop: 'id', label: '用户ID', width: 80 },
  { prop: 'username', label: '用户姓名', width: 100 },
  { prop: 'mobile', label: '联系电话' },
  {
    prop: 'departmentVO',
    label: '所属部门',
    render: (scope) => scope.row?.departmentVO?.name
  },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'operation', label: '操作', fixed: 'right', width: 160 }
]
