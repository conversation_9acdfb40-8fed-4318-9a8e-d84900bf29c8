<template>
  <RxkDialog
    v-model:modelValue="show"
    :showFooter="false"
    :showHeader="false"
    title="成员单选"
    width="600px"
    class="customTransfer"
  >
    <SelectPanel 
      @registerPanel="registerPanel"
      @close="close"
      @confirm="confirm"/>
  </RxkDialog>
</template>

<script lang="ts" setup>
import { RxkDialog } from '@/components/common/RxkDialog'
import SelectPanel from '../selectPanel.vue'

import { computed } from 'vue'
import type { BasicProps } from '../type'
import { usePanel } from '../hooks/usePanel'

const props = withDefaults(defineProps<BasicProps>(), {
  custom: false,
  value: '',
  visible: false,
  activeName: 'organizational',
  paramsData: {},
  configuration: false
})

const emit = defineEmits(['update:visible', 'sure', 'close'])
const show = computed({
  get () {
    init(props)
    return props.visible
  },
  set: (visible: boolean) => emit('update:visible', visible)
})
const [registerPanel, { getCheckedUser, getContainChildren, getParameter, init }] = usePanel()

function close (){
  show.value = false
}

function confirm () { 
  if(getCheckedUser().length === 0) {
    ElMessage.error('请选择指定员工')
    return
  }
  emit('sure', {
    userInfoList: getCheckedUser(),
    dynamicParameters: getParameter(),
    containChildren: getContainChildren()
  })
}

</script>

<style lang="scss" scoped>
</style>