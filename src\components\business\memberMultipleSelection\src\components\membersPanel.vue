<template>
  <div class="members">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索人员"
        clearable
        :prefix-icon="Search"/>
      <div class="memberList">
        <el-checkbox-group v-model="checkUserIds">
          <div 
            class="childNodeItem"
            v-for="(item, index) in memberList"
            :key="index">
            <el-checkbox 
              @change="(checked: boolean) => selectChange(checked, item)"
              :label="item.id"
              :disabled="item?.jobStatus?.code === 2"
              v-model="item.checked"
            >{{ item.realName }}<template v-if="item?.jobStatus?.code === 2">（离职）</template></el-checkbox>
          </div>
        </el-checkbox-group>
        
      </div>
    </div>
    <div class="right">
      <div class="top">
        <span>已选择{{ checkUserList.length }}项</span>
        <span class="clear" @click="clearAll">清空</span>
      </div>
      <div class="checkUserList">
        <span class="checkUserList-item"  v-for="(item,index) in checkUserList" :key="index">
          <span>{{ item.realName }}</span>
          <i v-if="!item?.disable" class="iconfont icon-close" @click="delItem(item.id)"/>
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, unref, watch, type PropType } from 'vue'
import { Search } from '@element-plus/icons-vue'
import type { UserVOList } from '../type'
import { cloneDeep } from '@/utils/tools'

const emit = defineEmits(['update:data', 'updateValue'])

const personnelSelectionInput = ref<string>('')
const checkUserList = ref<UserVOList[]>([])
const checkUserIds = ref<string[]>([])
const memberList = ref<UserVOList[]>([])

const props = defineProps({
  data: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  },
  checkedUser: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  }
})

const orginRoleData = computed(() => {
  return unref(props.data)
})

watch(() => props.data, (newValue) => {
  memberList.value = cloneDeep(newValue)
}, {
  immediate: true,
  deep: true
})

watch(() => props.checkedUser, (checkedUser) => {
  console.log(checkedUser, 'checkedUsercheckedUser')
  checkUserList.value = checkedUser || []
  checkUserIds.value = checkedUser.map(item => item.id) || []
}, {
  immediate: true,
  deep: true
})

function selectChange (checked:boolean, item: any) {
  console.log(item, checkUserIds.value)
  item.checked = checked
  const findId = checkUserList.value.findIndex(el => el.id === item.id)
  if(findId !== -1) {
    checkUserList.value.splice(findId, 1)
  } else {
    checkUserList.value.push(item)
  }
  emit('updateValue', checkUserList.value)
}

function delItem (id: string){
  const findId = checkUserList.value.findIndex(el => el.id === id)
  if(findId !== -1) {
    checkUserList.value.splice(findId, 1)
  }
  emit('updateValue', checkUserList.value)
}

function clearAll () {
  checkUserList.value = checkUserList.value.filter(item => item.disable)
  emit('updateValue', checkUserList.value)
}

watch(personnelSelectionInput, (val) => {
  console.log(orginRoleData, 'orginRoleData')
  memberList.value = orginRoleData.value?.filter(item => item.realName && item.realName.includes(val))
})

</script>

<style lang="scss" scoped>
.members {
  @include flex-center(row, normal, normal);
  height: 100%;
  .left {
    flex: 1;
    border-right: 1px solid #EBEEF5;
    padding: 16px 16px 16px 0;
    .memberList {
      overflow-y: auto;
      height: calc(100% - 38px);
      .childNodeItem {
        display: block;
        width: 100%;
      }
    }
    .personnelSelectionInput {
      border-radius: 4px;
      width: 268px;
      :deep(.el-input__wrapper) {
        box-shadow: none;
        background: #F4F4F5;
      }
      margin-bottom: 13px;
    }
  }
  .right {
    flex: 1;
    padding: 16px 0 16px 16px;
    overflow-y: auto;
    font-size: $font-size-mini;
    color: $main-text-color;
    .top {
      @include flex-center(row, space-between, center);
      margin-bottom: 12px;
      .clear {
        color: $primary-color;
        cursor: pointer;
      }
    }
    .checkUserList {
      overflow-y: auto;
      height: calc(100% - 30px);
      &-item {
        display: inline-block;
        padding: 2px 6px 2px 10px;
        border-radius: 2px;
        background: #EEF3FF;
        margin-right: 8px;
        margin-bottom: 8px;
        .icon-close {
          cursor: pointer;
          font-size: $font-size-mini;
          margin-left: 8px;
        }
      }
    }
  }
  
}
</style>