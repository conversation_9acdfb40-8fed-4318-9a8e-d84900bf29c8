<template>
  <div class="parameter-manage">
    <!-- 头部 -->
    <div class="client-top">
      <ul class="select-tab">
        <li
          class="select-li"
          @click="clickTab(item)"
          :class="item.value === state.activeTab ? 'active' : ''"
          v-for="(item, index) of ulData"
          :key="index"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>
    <!-- 主要部分 -->
    <div class="client-main">
      <component :is="state.activeTabComponent" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import type { Component } from 'vue'
import ParameterManage from './parameterManage/index.vue'
import AdminManage from './adminManage/index.vue'
const ulData = [
  { label: 'API配置', value: 'parameterManage', component: ParameterManage },
  { label: 'Admin配置', value: 'adminManage', component: AdminManage }
]
const state = reactive({
  activeTab: 'parameterManage',
  activeTabComponent: ParameterManage
})
function clickTab(item: {
  label: string
  component: Component
  value: string
}) {
  state.activeTab = item.value
  state.activeTabComponent = item.component
}
</script>

<style lang="scss" scoped>
.parameter-manage {
  height: 100%;
  position: relative;
  .client-top {
    height: 54px;
    padding-left: 15px;
    border-bottom: 1px solid #ebeef5;
    .select-tab {
      height: 100%;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      .select-li {
        cursor: pointer;
        margin-right: 24px;
        height: 100%;
        display: flex;
        align-items: center;
        &.active {
          color: #5687ff;
          border-bottom: 2px solid #5687ff;
        }
      }
    }
  }
  .client-main {
    height: calc(100% - 54px);
    overflow: auto;
  }
}
</style>
