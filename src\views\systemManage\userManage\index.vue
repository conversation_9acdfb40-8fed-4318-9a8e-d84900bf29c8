<template>
  <div class="main-user-manage user-container">
    <DepartmentTree @handleNodeClick="handleNodeClick" @changeDepartment="reload" />
    <div class="right-content">
      <SearchFilter @register="registerSetting" @search="search"/>
      <div class="operate-row">
        <RxkButton
          type="primary"
          @click="handleAdd"
          v-auth="'USER_ADD'"
        >
          添加用户
        </RxkButton>
        <RxkButton
          text
          style="margin-left: auto"
          @click="state.quitListVisible = true"
          v-auth="'RESIGN_DETAIL'"
        >
          查看离职用户
        </RxkButton>
      </div>
      <div class="table-row">
        <RxkVTable @register="registerTable">
          <template #leftSlot>
            <div class="user-count">用户数：{{ getTableData().total }}</div>
          </template>
          <template #realName="{ row }">
            <div class="tw-flex tw-items-center">
              <span>{{row.realName}}</span>
              <img style="width: 36px;height: auto;margin-left: 8px" :src="isAdminPng" v-if="row.isAdmin" >
            </div>
          </template>
          <template #stateSlot="{row}">
            <div class="state-box">
              <span class="state-dot" :style="{backgroundColor: row.state?.name === 'ENABLED'?'#5DC144':'#F44D4D'}"/>
              <span>{{row.state?.name === 'ENABLED'? '启用': '禁用'}}</span>
            </div>
          </template>
          <template #loginSlot="{row}">
            <div class="state-box">
              <span class="state-dot" :style="{backgroundColor: row.externalLoginSwitch ?'#5DC144':'#F44D4D'}"/>
              <span>{{row.externalLoginSwitch ? '启用': '禁用'}}</span>
            </div>
          </template>
          <template #operateSlot="{row}">
            <div v-if="!isSuper(row)">
              <RxkButton class="table-action-btn"
                         text
                         @click="handleEdit(row)"
                         v-auth="'USER_UPDATE'">编辑</RxkButton>
              <el-popconfirm :title="`是否确定要${row.state?.name === 'ENABLED'?'禁用':'启用'}?`" @confirm="handleEnable(row)">
                <template #reference>
                  <RxkButton
                    class="table-action-btn"
                    v-auth="'USER_ENABLED'"
                    :loading="enableLoading && enableId === row.id"
                    text
                  >{{ row.state?.name === 'ENABLED'?'禁用':'启用' }}</RxkButton>
                </template>
              </el-popconfirm>
              <RxkButton
                class="table-action-btn"
                v-auth="'USER_RESIGN'"
                text
                @click="handleQuit(row)">离职</RxkButton>
            </div>
          </template>
        </RxkVTable>
      </div>
    </div>
    <EditUserDialog
      v-if="state.showDialog"
      :rowData="rowData"
      @handleClose="state.showDialog = false"
      @handleSubmit="reload"
    />
    <QuitDialog
      v-if="state.quitVisible"
      :user-id="rowData.id"
      @handleClose="state.quitVisible = false"
      @handleSubmit="reload"
    />
    <QuitList
      v-model="state.quitListVisible"
      @handleClose="state.quitListVisible = false"
    />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref, toRaw, unref } from 'vue'
import DepartmentTree from './DepartmentTree/index.vue'
import { SearchFilter } from '@/components/business/searchFilter'
import EditUserDialog from './EditUserDialog/index.vue'
import QuitDialog from './QuitDialog/index.vue'
import QuitList from './QuitList/index.vue'
import { getUserList } from '@/apis/userManage'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import { ColumnType } from '@/types/table'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { FormSchema } from '@/components/business/searchFilter/src/type'
import { stateEnum } from '@/views/systemManage/userManage/config'
import { updateUser } from '@/apis/userManage'
import { ElMessage } from 'element-plus'
import { formatToDateTime } from '@/utils/date'
import isAdminPng from '@/assets/images/icon/adminTag.png'

// 获取 ProTable DOM
const state = reactive({
  showDialog: false,
  quitVisible: false,
  quitListVisible: false,
  selectRowData: {},
  quiteName: '',
  searchParam: {}
})

const searchCache = {}

const searchInfo: {model: Recordable} = {
  model: {
    realName: searchCache?.realName || undefined,
    state: searchCache?.state || undefined,
    departmentId: searchCache?.departmentId || undefined
  }
}

const searchFormData = ref<FormSchema[]>([
  {
    key: 'realName',
    component: 'Input',
    val: searchCache?.realName || '',
    fieldName: '用户姓名'
  },
  {
    key: 'state',
    component: 'Select',
    fieldName: '状态',
    options: stateEnum,
    val: searchCache?.state || ''
  }
])

const columns = ref<ColumnType[]>([
  {
    key: 'id',
    title: '用户ID',
    width: 180
  },
  {
    key: 'realName',
    title: '用户姓名',
    width: 200,
    slot: 'realName'
  },
  {
    key: 'username',
    title: '登录名'
  },
  {
    key: 'mobile',
    title: '联系电话',
    width: 140
  },
  {
    key: 'departmentVO',
    title: '所属部门',
    width: 140,
    render: ({ cellData }) => cellData?.departmentVO?.name
  },
  {
    key: 'supervisor',
    title: '直属上级',
    render: ({ cellData }) => cellData?.supervisorVO?.name || cellData?.supervisorName || '-'
  },
  {
    key: 'roleVOList',
    title: '角色',
    width: 200,
    render: ({ cellData }) => {
      const res = cellData?.roleVOList?.map((el:any) => el.name) || []
      return res.join(',') || '-'
    }
  },
  {
    key: 'state',
    title: '状态',
    slot: 'stateSlot'
  },
  {
    key: 'createTime',
    title: '添加时间',
    render: ({ cellData }) => {
      return cellData.createTime ? formatToDateTime(cellData.createTime) : '-'
    },
    width: 160
  },
  {
    key: 'loginTime',
    title: '最近登录时间',
    render: ({ cellData }) => {
      return cellData.loginTime ? formatToDateTime(cellData.loginTime) : '-'
    },
    width: 160
  },
  {
    key: 'operate',
    title: '操作',
    fixed: 'right',
    width: 140,
    slot: 'operateSlot'
  }
])

const rowData = ref<any>(undefined)

const [registerTable, { reload, setSearchInfo, getTableData }] = useTable({
  api: getUserList,
  columns: getBasicColumns(),
  searchInfo: searchInfo,
  immediate: true // 是否立刻请求
})

function search (data: Recordable) {
  searchInfo.model.realName = data.realName || undefined
  searchInfo.model.state = data.state || undefined
  setSearchInfo(searchInfo)
  reload()
}

const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})

function getBasicColumns (): ColumnType[] {
  return unref(columns)
}

const handleNodeClick = (data:any) => {
  searchInfo.model.departmentId = data.id
  if(data.id === '0'){
    searchInfo.model.departmentId = undefined
  }
  reload()
}

const handleAdd = () => {
  state.showDialog = true
  rowData.value = {
    departmentId: searchInfo.model.departmentId
  }
}

const handleEdit = (scope: any) => {
  state.showDialog = true
  rowData.value = scope
}

const enableLoading = ref(false)
const enableId = ref<any>('')
const handleEnable = async (scope: any) => {
  try {
    enableLoading.value = true
    enableId.value = scope.id
    const res = await updateUser({ id: scope.id, state: scope.state?.name === 'DISABLED' ? 'ENABLED' : 'DISABLED' })
    console.log(res)
    ElMessage({
      message: '操作成功！',
      type: 'success'
    })
    await reload()
  } finally {
    enableId.value = ''
    enableLoading.value = false
  }
}
const handleQuit = (scope: any) => {
  rowData.value = scope
  state.quitVisible = true
}

function isSuper (row:any){
  const roleVOList = row.roleVOList || []
  const superItem = roleVOList.find((el:any) => el?.roleType?.name === 'SUPER')
  return !!superItem
}

</script>

<style scoped lang="scss">
.user-container{
  min-width: 1200px;
}
.main-user-manage {
  font-size: 14px;
  height: 100%;
  overflow: hidden;
  display: flex;
  .right-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 0;
    background: #fff;
    width: 500px;
    overflow: hidden;
    height: 100%;
    .operate-row{
      padding: 0px 16px 16px;
      display: flex;
      justify-items: center;
    }
  }

  .el-form--inline .el-form-item {
    margin-right: 40px;
  }

  .user-count{
    display: flex;
    height: 100%;
    align-items: center;
    color: #666666;
  }

  .state-box{
    display: flex;
    align-items: center;
    .state-dot{
      width: 6px;
      height: 6px;
      border-radius: 6px;
      flex-shrink: 0;
      margin-right: 10px;
    }
  }
}
.table-row{
  width: 100%;
  flex: 1;
  overflow-y: hidden;
}
</style>