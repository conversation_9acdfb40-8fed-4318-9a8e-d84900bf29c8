import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getUserInfo, loginApi, logoutApi } from '@/apis/user'
import { getCurrentUserProductPricePermission } from '@/apis/permissionManage'
import type { LoginParams } from '@/types/user'
import { setToken } from '@/utils/auth'
import { usePermissionStore } from '@/stores/modules/permission'
import { router } from '@/router'
import { clearMemory } from '@/utils/login'
import { createLocalStorage } from '@/utils/cache'
const ls = createLocalStorage()

export const useAccountStore = defineStore('useAccount', () => {
  const usePermission = usePermissionStore()
  const hasLogin = ref(false) // 记录登录状态
  const userInfo = ref<Recordable>({})
  // 当前用户的产品价格权限
  const priceAuth = ref<'NO' | 'VIEW' | 'ALL'>('NO')

  // 修改登录状态
  function setHasLogin (bool: boolean) {
    hasLogin.value = bool
  }

  // 登录
  function login (data: LoginParams) {
    return new Promise((resolve, reject) => {
      loginApi(data)
      .then((res) => {
        console.log(res)
        // 存token
        setToken('bearer ' + res)
        // 获取用户信息
        getUser()
        // getPriceAuth()
        // 获取权限菜单
        usePermission.getAuthMenuList().then(() => {
          resolve(res)
        })
      })
      .catch((err) => {
        reject(err)
      })
    })
  }

  function getUser () {
    getUserInfo().then((res) => {
      userInfo.value = res
    })
  }

  function getPriceAuth () {
    getCurrentUserProductPricePermission().then((res) => {
      priceAuth.value = res?.productPriceType?.name || 'NO'
    })
  }

  // 登出
  async function logout (isLogout: boolean = false) {
    try {
      if (!isLogout) {
        await logoutApi()
      }
    } finally {
      // 清除token 历史标签
      clearMemory()
      // 清除是否活跃状态标志
      ls.remove(`${userInfo.value?.username}isActive`)
      console.log(`/login?redirect=${router?.currentRoute?.value?.fullPath}`)
      await router.push('/login')
    }
  }

  return {
    hasLogin,
    login,
    setHasLogin,
    logout,
    getUser,
    userInfo,
    priceAuth,
    getPriceAuth
  }
})
