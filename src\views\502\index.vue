<template>
  <div class="error-page">
    <img
      class="image"
      src="@/assets/images/common/502.png"
    >
    <div class="error-title">502</div>
    <p class="error-text">网页加载失败或请求超时</p>
  </div>
</template>

<script lang="ts" setup>

</script>

<style scoped lang="scss">
.error-page {
  margin: 0 auto;
  padding-top: 120px;
  text-align: center;
  .image {
    width: 300px;
    margin: 0 auto;
  }
  .error-title{
    color: #333333;
    font-size: 30px;
    font-weight: bold;
  }
  .error-text {
    margin-bottom: 30px;
    color: #333333;
    font-family: "Microsoft YaHei";
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
