import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
import { getEnvMode } from '@/utils/env'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

export const getQuoteValue = (data?: any) => request.post('/crm/component/quoteValue', data, jsonHeaderConfig, { ignoreCancelToken: true })
export const getDetaultValue = (data?: any) => request.post('/crm/component/defaultValue', data, jsonHeaderConfig, { ignoreCancelToken: true })
