import type { EpPropMergeType } from 'element-plus/es/utils/vue/props/types'

export interface Props {
  visible?: boolean
  direction?: EpPropMergeType<StringConstructor, 'rtl' | 'ltr' | 'ttb' | 'btt', unknown> | undefined
  size?: number | string
  fullLoading?: boolean
  btnLoading?: boolean
  footer?: boolean
  primaryText?: string
  cancelText?: string,
  title?: string
  showPrimaryBtn?: boolean
  showCancleBtn?:boolean
}
