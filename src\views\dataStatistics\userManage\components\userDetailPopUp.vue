<template>
  <RxkDrawer
    v-model="state.drawer"
    size="700"
    :footer="false"
    :title="state.title"
    @close="handleClose"
  >
    <template v-slot:content>
      <div class="info-wrapper">
        <div class="detail-block">
          <div class="common-title">基础信息</div>
          <div class="base-Info">
            <div
              class="info-item"
              v-for="item in userDetailBaseInfoEnum"
              :key="item.value"
            >
              <span>{{ item.label }}:</span>
              <span v-if="item.value === 'carMortgageStatus'">
                {{ carMortgageStatusOptions.find(i=>i.value === Number(state.details[item.value]))?.label || '-' }}
              </span>
              <span v-else>{{ state.details[item.value] || '-' }}</span>
            </div>
          </div>
        </div>

        <div class="detail-block">
          <div class="common-title">用户申请</div>
          <div class="base-Info">
            <div
              class="info-item"
              v-for="item in userApplyInfoEnum"
              :key="item.value"
            >
              <span>{{ item.label }}:</span>
              <span>{{ state.details[item.value] || '-' }}</span>
            </div>
          </div>
        </div>

        <div class="detail-block">
          <div class="common-title">来源信息</div>
          <div class="base-Info">
            <div
              class="info-item"
              v-for="item in userResourceInfoEnum"
              :key="item.value"
            >
              <span>{{ item.label }}:</span>
              <span v-if="item.value === 'channelType'">
                {{ channelTypeOption.find(i=>i.value === Number(state.details[item.value]))?.label || '-' }}
              </span>
              <span v-else-if="item.value === 'device'">
                {{ equipmentTypeOption.find(i=>i.value === Number(state.details[item.value]))?.label || '-' }}
              </span>
              <span v-else>{{ state.details[item.value] || '-' }}</span>
            </div>
          </div>
        </div>

        <div class="detail-block user-action">
          <div class="common-title">用户行为</div>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in userActionList"
              :key="index"
              :timestamp="activity.timestamp"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </template>
  </RxkDrawer>
</template>
<script setup lang="ts">
import { RxkDrawer } from '@/components/common/RxkDrawer'
import { onMounted, reactive, ref } from 'vue'
import dayjs from 'dayjs'
import {
  userDetailBaseInfoEnum,
  userApplyInfoEnum,
  userResourceInfoEnum
} from '../data'
import { UseActionsEnum, carMortgageStatusOptions, channelTypeOption, equipmentTypeOption, type ActionEnumType } from '@/enums/carlife'
import { fetchGetUserInfo, fetchGetUserAction } from '@/apis/carlife'

const props = defineProps({
  rowData: Object
})
const emits = defineEmits(['close'])
const handleClose = () => {
  emits('close')
}

onMounted(async () => {
  await getUserInfo()
  await getUserAction()
})

const state = reactive<{ [key: string]: any }>({
  drawer: true,
  title: '用户明细',
  details: {}
})
const userActionList = ref<{timestamp: string, content: string}[]>([])

async function getUserInfo () {
  if(!props.rowData?.id) return

  const res = await fetchGetUserInfo({ userId: props.rowData.id })
  state.details = res || {}
}

async function getUserAction () {
  const res = await fetchGetUserAction({ userId: state.details.userId })
  
  userActionList.value = res?.map((item: Recordable) => {
    return {
      timestamp: dayjs(Number(item.dateTime)).format('YYYY-MM-DD HH:mm:ss'),
      content: item.buriedPoint && UseActionsEnum[item.buriedPoint as ActionEnumType]
    }
  }) || []
}

</script>
<style lang="scss" scoped>
.info-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  .detail-block {
    margin-bottom: 8px;
  }

  .user-action {
    min-height: 320px;
    flex: 1;
    display: flex;
    flex-direction: column;
    :deep(.el-timeline) {
      padding: 4px;
      padding-bottom: 16px;
      min-height: 0;
      flex: 1;
      overflow: auto;
      .el-timeline-item__wrapper {
        display: flex;
        align-items: center;
        .el-timeline-item__timestamp {
          margin-top: unset;
          margin-left: 16px;
        }
      }
    }
  }
}

.common-title {
  font-size: 14px;
  font-weight: 700;
  line-height: 16px;
  padding-left: 12px;
  border-left: 4px solid #5687ff;
  margin-bottom: 24px;
  margin-top: 6px;
  width: 100%;
}
.base-Info {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  & > span {
    font-size: 14px;
    display: inline-block;
  }
  & > span:nth-child(1) {
    color: #666666;
    margin-right: 8px;
  }
  & > span:nth-child(2) {
    color: #333333;
    font-size: 14px;
    width: 200px;
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 溢出隐藏 */
    text-overflow: ellipsis;
  }
}
</style>
