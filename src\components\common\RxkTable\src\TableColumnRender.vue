<script lang="tsx">
import { h, type PropType, toRaw } from 'vue'
import type { renderFn } from '@/types/common'
export default {
  name: 'table-header-render',
  props: {
    data: { // 框架自带的数据scope
      type: Object,
      default: () => ({})
    },
    columnConfig: { // 当前列的配置
      type: Object,
      default: () => ({})
    },
    renderFn: {
      type: Function as PropType<renderFn>,
      default: () => {}
    }
  },
  setup (props: Recordable) {
    return () => {
      return props.renderFn(toRaw(props.data), props.columnConfig)
    }
  }

}
</script>

<style scoped lang="scss">

</style>
