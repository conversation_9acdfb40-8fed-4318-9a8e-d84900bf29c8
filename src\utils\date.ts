import dayjs from 'dayjs'
import { isNullOrUndefOrEmpty } from "@/utils/is";
const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'
const DATE_FORMAT = 'YYYY-MM-DD'

/**
 * @author：张胜
 * @desc：如 2023-04-12 10:08:55
 * */
export function formatToDateTime (date?: dayjs.ConfigType, format = DATE_TIME_FORMAT): string {
  return dayjs(date).format(format)
}

/**
 * @author：张胜
 * @desc：如 2023-04-12
 * */
export function formatToDate (date?: dayjs.ConfigType, format = DATE_FORMAT): string {
  return dayjs(date).format(format)
}

export const date = dayjs

/**
 * @author：张胜
 * @desc：获取当月包含的天数，若传入date，则指定返回该时间对应月份的天数，否则返回当前时间对应月份的天数
 * */
export function getMonthDayNum (date?: dayjs.ConfigType) {
  return dayjs(date).daysInMonth()
}

export const time = {
  format (time: string, format: string) { // 时间转换 将Thu Jun 14 2018 00:00:00 GMT+0800 (中国标准时间)转换成你所需要的格式 如：yyy-MM-dd-HH-mm-ss
    if (isNullOrUndefOrEmpty(time)) {
      return '';
    }
    if (new Date(time) === 'Invalid Date') {
      return time;
    }
    var t = new Date(time);
    var tf = function (i) {
      return (i < 10 ? '0' : '') + i;
    };
    return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
      switch (a) {
        case 'yyyy':
          return tf(t.getFullYear());
        case 'MM':
          return tf(t.getMonth() + 1);
        case 'mm':
          return tf(t.getMinutes());
        case 'dd':
          return tf(t.getDate());
        case 'HH':
          return tf(t.getHours());
        case 'ss':
          return tf(t.getSeconds());
      }
    });
  },
  timeFormat (time: string, format: string) {
    if (isNullOrUndefOrEmpty(time)) {
      return '';
    }
    if (isNullOrUndefOrEmpty(format)) {
      format = '';
    }
    time = time.replace(/:/g, format);
    return time;
  },
  timeStamp (time: string) {
    var date = new Date(time);
    var Y = date.getFullYear() + '-';
    var M =
      (date.getMonth() + 1 < 10
        ? '0' + (date.getMonth() + 1)
        : date.getMonth() + 1) + '-';
    var D = date.getDate() + ' ';
    // var h = date.getHours() + ':';
    // var m = date.getMinutes() + ':';
    // var s = date.getSeconds();
    return Y + M + D;
  },
  dateFormat (fmt: string, date: string) {
    let ret;
    const time = new Date(date);
    const opt = {
      'Y+': time.getFullYear().toString(), // 年
      'm+': (time.getMonth() + 1).toString(), // 月
      'd+': time.getDate().toString(), // 日
      'H+': time.getHours().toString(), // 时
      'M+': time.getMinutes().toString(), // 分
      'S+': time.getSeconds().toString() // 秒
      // 有其他格式化字符需求可以继续添加，必须转化成字符串
    };
    for (const k in opt) {
      ret = new RegExp('(' + k + ')').exec(fmt);
      if (ret) {
        fmt = fmt.replace(ret[1], (ret[1].length === 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, '0')));
      };
    };
    return fmt;
  },
  // 特殊周，返回上周四至这周三
  specialWeek () {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 星期天为0，星期六为6
    let thursdayDiff = 0;
    // 计算当前日期与周四差
    if (dayOfWeek < 4) {
      thursdayDiff = dayOfWeek + 3;
    }
    if (dayOfWeek > 4) {
      thursdayDiff = dayOfWeek - 4;
    }
    const thisThursday = new Date(today.getFullYear(), today.getMonth(), today.getDate() - thursdayDiff, 0, 0, 0); // 获取周四的日期对象
    const thisWednesday = new Date(thisThursday.getFullYear(), thisThursday.getMonth(), thisThursday.getDate() + 6, 23, 59, 59); // 获取周三的日期对象
    return [dayjs(thisThursday).format('YYYY-MM-DD'), dayjs(thisWednesday).format('YYYY-MM-DD')];
  },
  /**
   * @param time 比较的时间值 [startTime, endTime]
   * @param type 差值的度量单位 years、months、weeks、days、hours、minutes 和 seconds 默认的单位是毫秒
   * @param isFloat 是否返回一个回浮点数
   */
  timeDiff (time: string[], type: string, isFloisFloat?: boolean) {
    const diff = dayjs(time[1]).diff(time[0], type, isFloisFloat);
    console.log(diff);
    return diff;
  }
};
/**
 * @param date 时间戳（毫秒）
 * @param fmt 格式
 * @returns {string}
 */
export function parseTimeTwo (time: string, fmt = 'yyyy-MM-dd HH:mm:ss') {
  if (time === '' || !time || time === '-') return '-';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'number') {
      if (('' + time).length === 10) time = parseInt(time) * 1000;
    } else if (typeof time === 'string' && time.indexOf('-') > -1) {
      time = time.replace(/-/g, '/');
    }
    date = new Date(time);
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  const o:{[key: string]: number} = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  };

  // 遍历这个对象
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = o[k] + '';
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str));
    }
  }
  return fmt;

  function padLeftZero (str) {
    return ('00' + str).substr(str.length);
  }
}
// 获取本月第一天
export function getCurrentMonthFirst () {
  const date: any = new Date();
  date.setDate(1);
  return parseTimeTwo(date, 'yyyy-MM-dd');
}

// 获取本月最后一天
export function getCurrentMonthLast () {
  const nowDate = new Date();
  const y = nowDate.getFullYear();
  let m = nowDate.getMonth() + 1;
  let d = new Date(y, m, 0).getDate();
  m = m < 10 ? '0' + m : m; // 月份补 0
  d = d < 10 ? '0' + d : d; // 日数补 0
  return [y, m, d].join('-');
}

/**
 * @author：张胜
 * @desc：如 10:59:59
 * */
export function getTime (date?: dayjs.ConfigType) {
  return dayjs(date).format('HH:mm:ss')
}
