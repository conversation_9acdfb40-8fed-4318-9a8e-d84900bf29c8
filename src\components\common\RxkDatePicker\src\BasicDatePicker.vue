<template>
  <el-date-picker
    v-bind="$attrs"
    style="width: 100%"
    v-model="date"
    :type="type"
    :prefix-icon="Icon"
    :placeholder="placeholder"
    :start-placeholder="startPlaceholder"
    :end-placeholder="endPlaceholder"/>
</template>
<script lang="ts" setup>
import Icon from './icon.vue'
import { computed, type PropType } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Date, Number, String, Array],
    default: ''
  },
  type: {
    type: String as PropType<'year' | 'month' | 'date' | 'datetime' | 'week' | 'datetimerange' | 'daterange'>,
    default: ''
  },
  startPlaceholder: {
    type: String,
    default: '开始日期'
  },
  endPlaceholder: {
    type: String,
    default: '结束日期'
  },
  placeholder: {
    type: String,
    default: '请选择'
  }
})
const emit = defineEmits(['update:modelValue'])
const date = computed({
  get () {
    return props.modelValue
  },
  set (val) {
    emit('update:modelValue', val)
  }
})
</script>
