export namespace PlatformManage {
  export interface ReqSavePlatform {
    id?: string
    fullName: string
    simpleName: string
    merchantInfoSyncType: number
    merchantInfoSyncUrl: string
    productInfoSyncUrl: string
    productStatusSyncUrl: string
    productCustomizeInfo: ProductCustomizeInfoItem[]
    code?: string
  }
  export interface ProductCustomizeInfoItem {
    id?: string
    fieldDesc: string
    fieldName: string
    fieldType: number
    labelList?: LabelListItem[]
  }
  export interface LabelListItem {
    fieldId?: string
    label: string
    value?: string
  }
  export interface RyhProductAuditReq {
    id: string
    auditStatus: number
    auditRemark: string
  }
  export interface RyhProductUpdateReq {
    id: string
    enableStatus: number
    bidPrice: number
    productTypeSpecial: number
    channelIds: string[]
    receiveMappingOrder: number
    capitalCondition: number
  }
  export interface WechatLinkAuditReq {
    id: string
    wechatAuditStatus: number
  }
}
