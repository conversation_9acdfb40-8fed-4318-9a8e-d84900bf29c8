/* 应用商城 - 商品管理*/
// 商品类型
export const goodTypes = [
  { value: 'VALUE_ADD_APP', label: '增值应用' },
  { value: 'SOFT_SERVICE', label: '软件服务' },
  { value: 'HARDWARE_DEVICES', label: '硬件设备' },
  { value: 'SERVICE_SUPPORT', label: '服务支持' },
  { value: 'VALUE_ADD_PACKAGE', label: '增值套餐' }
];
// 收费类型
export const goodChargeWays = [
  { value: 'PERIOD', label: '周期性计费' },
  { value: 'QUANTITY', label: '按数量计费' },
  { value: 'QUANTITY_PERIOD', label: '按数量周期性计费' },
  { value: 'PERMANENT', label: '永久生效' }
];
// 商品状态
export const goodStatus = [
  { value: 'DISABLED', label: '禁用' },
  { value: 'ENABLED', label: '启用' }
];
/* 应用商城 - 优惠券管理*/
// 优惠券类型
export const couponTypes = [
  { value: 0, label: '代金券' },
  { value: 1, label: '折扣券' }
];
// 优惠券状态
export const couponStatusList = [
  { value: 0, label: '禁用' },
  { value: 1, label: '启用' }
];
// 投放规则-可见客户端
export const platformList = [
  { value: '0', label: 'A端购买商品(代下单)' },
  { value: '1', label: 'B端应用商城' }
];
// 租户可见类型：0 所有租户 1 指定租户
export const tenantAvailableTypes = [
  { value: 0, label: '全部机构' },
  { value: 1, label: '指定机构' }
];
// 商品规格-类型
export const goodSkuTimeCycleUnit = [
  {
    value: 1,
    label: '年'
  },
  {
    value: 2,
    label: '季度'
  },
  {
    value: 3,
    label: '月'
  }
];

// 订单名称
export const orderNameList = [
  { value: 'SYSTEM_OPEN_FEE', label: '系统开通费' },
];

// 子订单是否可见
export const subOrderVisibleList = [
  { value: false, label: '否' },
  { value: true, label: '是' }
];