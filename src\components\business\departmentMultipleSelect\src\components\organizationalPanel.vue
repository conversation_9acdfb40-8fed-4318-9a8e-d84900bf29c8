<template>
  <div class="organizational">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索部门"
        clearable
        :prefix-icon="Search"/>
      <div class="treeList">
        <el-tree 
          node-key="id"
          ref="treeRef"
          :data="treeData"
          show-checkbox
          :filter-node-method="filterNode"
          :check-strictly="isIncludeChildValue"
          :defaultProps="defaultProps"
          :check-on-click-node="false"
          :expand-on-click-node="true"
          @node-expand="nodeToggle"
          @node-collapse="nodeToggle"
          @check="handleNodeClick">
          <template #default="{ node, data }">
            <div class="custom-tree-node" @click.stop>
              <div class="custom-tree-node-item" :class="data.id === treeData?.[0].id ? 'treetop' : ''" >
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="`${data.name}`"
                  placement="top-start"
                >
                  <span>{{data.name}}</span>
                </el-tooltip>
                <!-- 只在第一层展示 -->
                <span class="treeoperate" v-if="data.id === treeData?.[0].id" @click.stop="handleToggle">
                  {{treeStatue ? '收起' : '展开'}}子部门
                </span>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    <div class="right">
      <div class="top">
        <span>已选择{{ checkDepList.length }}项</span>
        <span class="clear" @click="clearAll">清空</span>
      </div>
      <div class="checkUserList">
        <span class="checkUserList-item" v-for="(item, index) in checkDepList" :key="index">
          <span>{{ item.name }}</span>
          <i v-if="!item?.disable" class="iconfont icon-close" @click="deleteCheckeDep(item)"/>
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { ref, watch, type PropType, computed, unref, nextTick } from 'vue'
import type { ElTree } from 'element-plus'
import type { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type'
import { cloneDeep, recursion } from '@/utils/tools'
import type { DeComponentTreeVO } from '../type'

const emit = defineEmits(['update:data', 'updateValue'])
const treeRef = ref<InstanceType<typeof ElTree>>()
const treeStatue = ref<boolean>(false) // 组织架构展开收起状态，默认收起
const checkDepList = ref<Recordable[]>([])
const currentNodeData = ref<Recordable>({}) // 当前选中节点
const defaultProps = {
  children: 'children',
  label: 'name'
}

const personnelSelectionInput = ref('')
const isIncludeChildValue = ref<boolean>(false)
const cloneCheckDepList = ref<Recordable[]>([])

const props = defineProps({
  data: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  },
  checkedDep: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  },
  isIncludeChild: {
    type: Boolean,
    default: false
  }
})

const treeData = computed(() => {
  return unref(props.data)
})
watch(() => props.isIncludeChild, (newValue) => {
  isIncludeChildValue.value = cloneDeep(newValue)
}, {
  immediate: true,
  deep: true
})
watch(() => props.checkedDep, async (checkedDep) => {
  console.log(checkedDep, 'checkedDep', treeData.value)
  nextTick(async () => {
    treeRef.value?.setCheckedKeys(checkedDep.map(item => item.id))
    cloneCheckDepList.value = cloneDeep(checkedDep.filter(item => item.disable))
    await handleDepCheck()
  })
}, {
  immediate: true,
  deep: true
})
// 节点点击
async function handleNodeClick (currentNode: Recordable) {
  console.log(currentNode)
  currentNodeData.value = currentNode
  await handleDepCheck()
  emit('updateValue', checkDepList.value)
}

async function handleDepCheck () {
  const data = cloneDeep(treeData.value)
  const nodesMap = treeRef.value?.store.nodesMap
  const checkedNodeMap: any[] = []
  for (let i in nodesMap) {
    if(nodesMap[i].checked ) {
      checkedNodeMap.push(nodesMap[i])
    }
  }
  console.log(nodesMap, 'handleNodeClick', checkedNodeMap, isIncludeChildValue.value)
  let checkedNodes: TreeNodeData[] = []
  console.log(treeRef.value?.store, 'treeRef.value?.store')
  if(!isIncludeChildValue.value) {
    const checkedNodesData = checkedNodeMap.map(item => item.data)
    // 包含子员工
    recursion(data, (item) => {
      const selectItem = checkedNodesData.find(select => select.id === item.id)
      if(!item.parentId) { 
        if(selectItem) {
          checkedNodes.push(item)
          return true
        }
        
      } else {
        if(!item.checked && selectItem) {
          checkedNodes.push(item)
          item.checked = true
          if (item.children && item.children.length) {
            recursion(item.children, (child) => {
              child.checked = true
            })
          }
        }
      }
    })
  } else {
    console.log(treeRef.value?.store, 'treeRef.value?.storeisIncludeChildValue')
    Object.values(nodesMap as Recordable).forEach(node => {
      if (node.checked) {
        checkedNodes.push(node.data)
      }
    })
  }
  console.log(checkedNodes, 'checkedNodes', cloneCheckDepList.value)
  const deepData = cloneDeep(cloneCheckDepList.value)
  console.log(deepData, 'deepData')
  // 如果checkedNodes为空，说明一个都没选
  if(checkedNodes.length === 0) {
    checkDepList.value = deepData.filter((item:Recordable) => item.disable)
  } else {
    checkedNodes.forEach(item => {
      const findIndex = deepData.findIndex((itemB:Recordable) => itemB.id === item.id)
      if(findIndex === -1) {
        deepData.push(item)
      } else {
        deepData.splice(findIndex, 0)
      }
    })
    checkDepList.value = deepData
  }
 
}

const filterNode = (value: string, data: Recordable) => {
  if (!value) return true
  return data.name.includes(value)
}
watch(personnelSelectionInput, (val) => {
  treeRef.value!.filter(val)
})

/**
 * 展开收起全部
 */
function handleToggle () {
  treeStatue.value = !treeStatue.value
  console.log(treeRef.value?.store)
  let nodesData = treeRef.value?.store.nodesMap
  for (let i in nodesData) {
    if(nodesData[i].childNodes && nodesData[i].childNodes.length > 0) {
      nodesData[i].expanded = treeStatue.value
    }
  }
}

function nodeToggle (data, currentNode) {
  console.log(data)
  console.log(currentNode)
  setTimeout(() => {
    const nodesData = treeRef.value?.store.nodesMap
    let expandedVal = false
    for (let i in nodesData) {
      if(nodesData[i].expanded) {
        expandedVal = true
      }
    }
    treeStatue.value = expandedVal
  }, 0)
}

function deleteCheckeDep (itemData:Recordable) {
  const findIndex = checkDepList.value.findIndex(select => select.id === itemData.id)
  console.log(treeRef.value!.getCheckedKeys(false))
  const allCheckedKey = treeRef.value!.getCheckedKeys(false).filter(item => item !== itemData.id)
  console.log(allCheckedKey, 'allCheckedKey')
  treeRef.value!.setCheckedKeys(allCheckedKey, true)
  if(findIndex !== -1) {
    checkDepList.value?.splice(findIndex, 1)
  }
  console.log(checkDepList.value, 'checkDepList.value')
  emit('updateValue', checkDepList.value)
}

/**
 * 清空
 */
function clearAll () {
  // 只能清空diable为false
  checkDepList.value = checkDepList.value.filter(item => item.disable)
  treeRef.value!.setCheckedKeys(checkDepList.value.map(item => item.id), false)
  emit('updateValue', checkDepList.value)
}

// 父部门不包含子部门
async function handleIncludeChild (val:boolean){
  console.log('父部门不包含子部门', val)
  isIncludeChildValue.value = val
  await handleDepCheck()
  emit('updateValue', checkDepList.value)
}

/**
 * 选中可见选项
 */
async function handleAllCheck (val: boolean){
  if(!val) {
    // 清空
    clearAll()
  }
  const nodesMap = treeRef.value?.store.nodesMap
  if(val && isIncludeChildValue.value) {
    // 选中可见且不包含子部门
    const topNodes = Object.values(nodesMap as Recordable).filter(i => i.level === 1)
    console.log(topNodes, 'topNodes')
    const getExpandNode = (topNode: Recordable[]): Recordable[] => {
      if (!topNode.length) return []
      let nodes:Recordable[] = []
      nodes = topNode.reduce((pre: Recordable[], nex: Recordable) => {
        let expandNodes: Recordable[] = []
        if (nex.expanded) {
          expandNodes = getExpandNode(nex.childNodes)
        }
        return [...pre, nex, ...expandNodes]
      }, [])
      return nodes
    }
    const nodes = getExpandNode(topNodes)
    nodes.forEach(item => {
      item.checked = true
    })
    await handleDepCheck()
    emit('updateValue', checkDepList.value)
  }
  if(val && !isIncludeChildValue.value) {
    // 选中可见且包含子部门
    const nodes = Object.values(nodesMap as Recordable).filter(i => i.level === 1)
    console.log(nodes, 'nodes')
    recursion(nodes, (item) => {
      item.checked = true
    }, {
      children: 'childNodes'
    })
    await handleDepCheck()
    emit('updateValue', checkDepList.value)
  }
}

defineExpose({
  handleIncludeChild,
  handleAllCheck
})

</script>

<style lang="scss" scoped>
.organizational {
  @include flex-center(row, normal, normal);
  height: 100%;
  .left {
    width: 284px;
    border-right: 1px solid #EBEEF5;
    padding: 16px 16px 16px 0;
    .custom-tree-node {
      line-height: 12px;
      // display: inline-block;
      // min-width: 100%;
      &-item {
        .treelabel {
          width: 100%;
          :deep(.custom-tooltip-content) {
            text-align: left !important;
          }
        }
        .treeoperate {
          font-size: $font-size-mini;
          color: $primary-color;
          margin-right: 16px;
        }
        &-block {
          @include flex-center(row, space-between, center);
        }
      }
      .treetop {
        @include flex-center(row, space-between, center);
        width: 220px;
      }
    }
    .treeList {
      overflow-y: auto;
      height: calc(100% - 32px);
      :deep(.el-tree-node.is-expanded>.el-tree-node__children) {
        display: inline-block;
        min-width: 100%;
      }
    }
  }
  .center {
    width: 200px;
    border-right: 1px solid #EBEEF5;
    padding: 20px 16px;
    overflow-y: auto;
  }
  .right {
    flex: 1;
    padding: 16px 0 16px 16px;
    overflow-y: auto;
    font-size: $font-size-mini;
    color: $main-text-color;
    .top {
      @include flex-center(row, space-between, center);
      margin-bottom: 12px;
      .clear {
        color: $primary-color;
        cursor: pointer;
      }
    }
    .checkUserList {
      overflow-y: auto;
      height: calc(100% - 30px);
      &-item {
        display: inline-block;
        padding: 2px 6px 2px 10px;
        border-radius: 2px;
        background: #EEF3FF;
        margin-right: 8px;
        margin-bottom: 8px;
        .icon-close {
          cursor: pointer;
          font-size: $font-size-mini;
          margin-left: 8px;
        }
      }
    }
  }
  .personnelSelectionInput {
    border-radius: 4px;
    :deep(.el-input__wrapper) {
      box-shadow: none;
      background: #F4F4F5;
    }
    margin-bottom: 13px;
  }
}
</style>