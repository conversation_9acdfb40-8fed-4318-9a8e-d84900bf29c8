<template>
  <div class="role">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索角色"
        clearable
        :prefix-icon="Search"/>
      <div class="roleList">
        <div
          class="tw-flex tw-items-center"
          v-for="(item, index) in roleList"
          :key="index">
          <el-checkbox
            v-show="item.show"
            @change="(val: boolean) => selectChange(val,item)"
            v-model="item.checked"
            :indeterminate="item.indeterminate"
            :label="item.name" />
        </div>
      </div>
    </div>
  </div>
</template>
  
<script lang="ts" setup>
import { ref, watch, unref, type PropType } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { cloneDeep } from '@/utils/tools'

const personnelSelectionInput = ref<string>('')
const roleList = ref<Recordable[]>([])
const emit = defineEmits(['update:data', 'updateValue'])

const props = defineProps({
  data: {
    type: Array as PropType<Recordable[]>,
    default: () => {[]}
  },
  selectedData: {
    type: Array as PropType<Recordable[]>,
    default: () => {[]}
  }
})

watch(() => props.data, (newValue) => {
  roleList.value = cloneDeep(newValue).map((item:Recordable) => {
    return {
      ...item,
      show: true
    }
  })
}, {
  immediate: true,
  deep: true
})

function setDefaultData (list:any[] = []){
  // 对list做兼容
  const ids = list?.map(el => {
    if(el instanceof Object){
      return el?.value
    }
    if(typeof el === 'string'){
      return el
    }
    return undefined
  })
  unref(roleList).forEach(item => {
    if(ids.includes(item.id)) item.checked = true
  })
  emit('updateValue', getCheckList())
}

// 角色复选框点击
async function selectChange (checked:boolean, itemData: Recordable) {
  console.log(checked, itemData)
  emit('updateValue', getCheckList())
}

// 获取已选中的项
function getCheckList (){
  let list:Recordable[] = []
  unref(roleList).forEach(item => {
    item.checked && list.push({ value: item.id, label: item.name })
  })
  return list
}

watch(personnelSelectionInput, (val) => {
  roleList.value.forEach(item => {
    if(val){
      item.show = item.name && item.name.includes(val)
    } else {
      item.show = true
    }
  })
})

function unChecked (id:string){
  unref(roleList).forEach(item => {
    if(item.id === id) item.checked = false
  })
  emit('updateValue', getCheckList())
}

defineExpose({
  unChecked,
  setDefaultData
})

</script>
  
  <style lang="scss" scoped>
  .role {
    @include flex-center(row, normal, normal);
    height: 100%;
    .left {
      flex: 1;
      border-right: 1px solid #EBEEF5;
      padding: 16px 16px 16px 0;
      .roleList {
        overflow-y: auto;
        height: 284px;
        .el-checkbox {
          margin-right: 10px;
        }
        span {
          cursor: pointer;
        }
      }
      .personnelSelectionInput {
        border-radius: 4px;
        width: 268px;
        :deep(.el-input__wrapper) {
          box-shadow: none;
          background: #F4F4F5;
        }
        margin-bottom: 13px;
      }
    }
  }
  </style>