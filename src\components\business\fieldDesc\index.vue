<template>
  <RxkDialog
    v-model="show"
    title="字段释义"
    :width="1100"
    height="80%"
    cancelBtn="关闭"
    :showConfirmBtn="false"
  >
    <div class="field-desc-box">
      <el-table 
        :data="tableData"
        :border="true"
        height="100%"
        :header-cell-style="{textAlign: 'center', backgroundColor: '#f6f8f9'}"
        :cellStyle="{textAlign: 'center'}"
        cell-class-name="cell-field"
        style="width: 100%">
        <el-table-column prop="title" label="字段名称"/>
        <el-table-column prop="desc" label="释义" />
      </el-table>
    </div>
  </RxkDialog>
</template>

<script setup lang="ts">
import { RxkDialog } from '@/components/common/RxkDialog'
import { defineProps, withDefaults, computed, defineEmits } from 'vue'
const props = withDefaults(defineProps<{
  visible: boolean,
  data: Array<Recordable>
}>(), {
  visible: false,
  data: () => []
})

const emit = defineEmits(['update:visible'])
const show = computed({
  get () {
    return props.visible
  },
  set (val: boolean) {
    emit('update:visible', val)
  }
})
const tableData = computed(() => {
  return props.data.filter(item => !!item.desc)
})
</script>

<style lang="scss" scoped>
.field-desc-box {
  padding: 16px;
}

</style>
