<template>
  <div class="isTxt" v-if="isTxt">
    {{name || '-'}}
  </div>
  <RxkSelect
    v-else
    :list="list"
    v-model="innerValue"
    @focus="remoteMethod"
    @change="handleChange"
  />
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, unref, watch } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkSelect } from '@/components/common/RxkSelect'
import { getPageComponentData, getPageComponentDataEchoAcquire } from '@/apis/customerManage'
import { isNullOrUndefOrEmpty } from '@/utils/is'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue', 'refreshDataList'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    console.log(newVal, 'hghhhh')
    emit('update:modelValue', newVal)
  }
})
const flag = ref(true)
const list = ref([])
const name = ref('')

onMounted(() => {
  console.log(props.data, 'props.data222')
  if (props.data.dataMark !== 2) {
    list.value = props.renderConfig.dataList || []
    getName(list.value)
  }
  if (props.data.dataMark === 2 && props.isTxt && innerValue.value) {
    const postData = {
      columnId: props.data.id,
      echoData: innerValue.value,
      tenantId: props.data.tenantId || 0
    }
    getPageComponentDataEchoAcquire({ ...postData }).then(res => {
      console.log(res)
      getName(res)
    })
  }
})
watch(() => props.modelValue, () => {
  if (props.data.dataMark !== 2 && props.isTxt) {
    getName(list.value)
  }
})

function remoteMethod () {
  console.log(props.data)
  if (unref(flag) && props.data.dataMark === 2) {

    const postData = {
      tableCode: props.data.tableCode,
      columnId: props.data.id,
      columnUseScene: props.data.columnUseScene,
      tenantId: props.data.tenantId || 0
    }
    getPageComponentData(postData).then(res => {
      list.value = res || []
      flag.value = false
      emit('refreshDataList', res)
    })
  }
}

function getName (list: Recordable[]) {
  console.log(innerValue, list, 'lolll')
  if (isNullOrUndefOrEmpty(list) || isNullOrUndefOrEmpty(innerValue.value)) {
    name.value = '-'
    return
  }
  const current = list.find(item => String(item.value) === String(innerValue.value))
  console.log(current, 'current222')
  name.value = current ? current.label || '-' : '-'
}

function handleChange () {

}

</script>