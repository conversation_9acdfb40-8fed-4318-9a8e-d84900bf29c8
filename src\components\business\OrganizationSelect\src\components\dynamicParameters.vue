<template>
  <div class="dynamicParameters">
    <slot name="dynamic" >
      <div style=" padding: 16px 0 0 16px;">
        <div class="dynamicParameters-item" v-if="dynamicParameters?.acquireCurrent !== undefined">
          <el-checkbox v-model="dynamicParameters.acquireCurrent"
                       :true-label="1"
                       :false-label="0"
                       @change="selectChange">当前新增/修改数据的用户{{onlyDep?'所属的部门':''}}</el-checkbox>
        </div>
        <div class="dynamicParameters-item" v-if="dynamicParameters?.acquireCurrentFather !== undefined">
          <el-checkbox v-model="dynamicParameters.acquireCurrentFather"
                       :true-label="1"
                       :false-label="0"
                       @change="selectChange">当前新增/修改数据的用户的直属领导</el-checkbox>
        </div>
      </div>
    </slot>
  </div>
</template>

<script lang="ts" setup>
import { computed, unref, type PropType } from 'vue'
import type { DynamicParameters } from '../type'

const emit = defineEmits(['update:dynamicParameters', 'updateParameter'])
const props = defineProps({
  onlyDep: Boolean,
  data: {
    type: Object as PropType<DynamicParameters>,
    default: () => ({
      acquireCurrent: 0,
      acquireCurrentFather: 0
    })
  }
})
const dynamicParameters = computed({
  get: () => unref(props.data),
  set: (newValue) => {
    emit('update:dynamicParameters', newValue )
  }
})

function selectChange (){
  emit('updateParameter', dynamicParameters.value)
}
</script>

<style lang="scss" scoped>
.dynamicParameters {
  height: 100%;
  &-item {
    display: block;
    width: 100%;
  }
}
</style>