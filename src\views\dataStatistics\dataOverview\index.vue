<template>
  <div class="data-overview-page">
    <SearchFilter @register="registerSetting" @search="search"/>

    <div class="btn-box">
      <RxkButton @click="handleDown">导出</RxkButton>
    </div>
    <div class="table-box">
      <RxkVTable 
        class="custorm-hasTable-footer"
        ref="dataTableRef"
        :defalutLoading="true"
        show-footer
        showHeaderSummary
        :footer-method="footerMethod"
        @register="registerTable"  
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { unref, ref, onMounted } from 'vue'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { pageConfig } from './data'
import { excelExport } from '@/utils/index'
import { fetchGetDataOverviewList, fetchChannelStatisticsListExport, fetchGetDataStatisticsTotal } from '@/apis/carlife'
import type { ColumnType } from '@/types/table'
import timeUtils from '@/utils/libs/time'

// 总计
const sumTableData = ref<Recordable>({})

const dataTableRef = ref()
// 搜索表单数据
const [registerSetting] = useSearch({
  schemas: unref(pageConfig.searchFormData)
})

const [transStartTime, transEndTime] = timeUtils.transTypeToTime(20, 'yyyy-MM-dd')
const searchInfo:Record<string, any> = {
  model: {
    dayStart: transStartTime,
    dayEnd: transEndTime,
    isChannelGroup: false,
    isDayGroup: true
  }
}

const [registerTable, { reload, setSearchInfo, getSearchInfo, getPaginationData }] = useTable({
  api: fetchGetDataOverviewList,
  columns: unref(pageConfig.columns),
  searchInfo: searchInfo,
  immediate: true // 是否立刻请求
})

const search = (val:{ [k: string]: any }) => {
  Object.assign(searchInfo.model, val)

  searchInfo.model.dayStart = val.timeRange?.[0] ?? ''
  searchInfo.model.dayEnd = val.timeRange?.[1] ?? ''
  delete searchInfo.model.timeRange

  setSearchInfo(searchInfo)
  getSummaryData()
  reload()
}

const handleDown = () => {
  const params = { ...getSearchInfo(), ...getPaginationData() }
  excelExport(fetchChannelStatisticsListExport, params, 'POST')
}

function getSummaryData () {
  fetchGetDataStatisticsTotal(searchInfo).then((res) => {
    sumTableData.value = res
    unref(unref(dataTableRef).tableRef)?.updateFooter?.()
  })
}

//  总计
function footerMethod ({ columns }: { columns: ColumnType[] }) {
  return [
    columns.map((column, columnIndex) => {
      if (columnIndex === 0) {
        return '总计'
      }
      const arr = column.field?.split(',') || []
      return arr.map((item) => {
        return unref(sumTableData)[item] ?? ''
      })?.filter(i => i !== '').join(' | ')
    })
  ]
}

onMounted(() => {
  getSummaryData()
})

</script>

<style lang="scss" scoped>
.data-overview-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .el-form--inline .el-form-item {
    margin-right: 40px;
  }

  .btn-box {
    padding: 0 16px 4px;
    display: flex;
    justify-content: flex-end;
  }

  .table-box {
    width: 100%;
    flex:1;
    overflow-y: hidden;
  }
}
</style>
