import { ref, watch, type ComputedRef, unref, onMounted, toRaw } from 'vue'
import type { BasicTableProps, PaginationProps } from '../type'
import { isFunc } from '@/utils/is'
import { usePageCache } from '@/hooks/usePageCache'
import { getColumnWidthApi } from '@/apis/common'
import { useResource } from '@/hooks/useResource'

interface ActionType {
  getPaginationInfo: ComputedRef<PaginationProps>
  emit: EmitType
  defalutLoading?: boolean
  tableRef: ComputedRef<Recordable>
}
export function useTableData (
  props: ComputedRef<BasicTableProps>,
  { getPaginationInfo, emit, tableRef, defalutLoading }: ActionType
) {
  const { updateCache, getCache } = usePageCache()
  const loading = ref(defalutLoading ?? false)
  // 表格数据
  const tableData = ref<Recordable[]>([])
  // 数据总数
  const total = ref(0)
  const extraMap = ref()
  // 如果是传递的表格数据
  watch(
    () => unref(props).data,
    () => {
      const { data, api } = unref(props)
      !api && data && (tableData.value = data)
    },
    {
      immediate: true
    }
  )

  async function reload () {
    isInit = false
    return await fetch()
  }
  const paginationRes = ref<null | Recordable<string>>(null)
  const KEY = 'vPaginationKey'
  let isInit = false
  const { getMenuAppCode } = useResource()
  async function getPagination () {
    const APPCODE = getMenuAppCode()
    const tableCode = props.value?.tableCode || props.value?.moduleId || APPCODE
    try {
      if (!isInit && unref(props).customTableConfig) {
        const res = await getColumnWidthApi({ businessKey: tableCode + KEY })
        paginationRes.value = res?.[0]
        isInit = true
      } else {
        paginationRes.value = null
      }
    } catch (error) {
      console.log(error)
    }
  }
  onMounted(() => {
    getPagination()
    unref(props).immediate && fetch(unref(props).immediate).then()
  })
  async function fetch (immediate = false) {
    const { api, searchInfo, showPagination, pagination } = unref(props)

    if (!api || !isFunc(api)) return
    try {
      const { pageSize, pageNum } = unref(getPaginationInfo) as PaginationProps
      // 请求参数
      const params: Recordable = {
        ...searchInfo
      }

      if (!showPagination) {
        delete params.pageSize
        delete params.pageNum
      }
      loading.value = true
      if (showPagination) {
        await getPagination()
        const remoteSearch = paginationRes.value
          ? Object.assign({}, unref(getPaginationInfo), {
            pageSize: +paginationRes.value?.value
          })
          : getCache()?.table?.searchInfo
        params.pageSize =
          remoteSearch?.pageSize || ref(pagination).value?.pageSize || pageSize
        params.pageNum = pageNum
      }
      updateCache({ table: { searchInfo: params } })
      const res = await api(params)
      const { records } = res
      loading.value = false
      if (showPagination) {
        tableData.value = records || []
      } else {
        tableData.value = res || []
      }

      total.value = res.total
      extraMap.value = res.extraMap
      if (immediate && unref(props).useCache) {
        const table = getCache()?.table
        // TODO: 这里有问题 需要等待表格渲染完毕后才能生效但无法监听表格渲染完成
        setTimeout(() => {
          tableRef.value?.scrollTo(table?.scrollLeft, table?.scrollTop)
        }, 1000)
      }
      emit('getRefTableData', { tableData, total })
      emit('getTableData', {
        tableData: toRaw(unref(tableData)),
        total: unref(total),
        extraMap: toRaw(unref(extraMap))
      })
    } catch (err) {
      console.log(err)
      loading.value = false
    }
  }

  // function getTableData () {
  //
  // }
  return {
    tableData,
    fetch,
    reload,
    total,
    loading,
    extraMap,
    paginationRes
  }
}
