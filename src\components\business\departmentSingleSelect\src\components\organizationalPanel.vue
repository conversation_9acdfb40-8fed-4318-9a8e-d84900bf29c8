<template>
  <div class="organizational">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索部门"
        clearable
        :prefix-icon="Search"/>
      <div class="treeList">
        <el-tree 
          node-key="id"
          ref="treeRef"
          :data="treeData"
          :filter-node-method="filterNode"
          :check-strictly="true"
          :expand-on-click-node="false"
          @node-expand="nodeToggle"
          @node-collapse="nodeToggle"
          @node-click="nodeClick">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="custom-tree-node-item" :class="data.id === treeData?.[0].id ? 'treetop' : ''">
                <div class="custom-tree-node-item-block">
                  <span @click.stop>
                    <el-radio-group v-model="selectDataValue" @change="selectChange(data)">
                      <el-radio
                        style="margin-right: 8px;"
                        :label="data.id"
                        v-model="data.checked"
                      >
                        <el-tooltip
                          class="box-item"
                          effect="dark"
                          :content="`${data.name}`"
                          placement="top-start"
                        >
                          <span>{{data.name}}</span>
                        </el-tooltip>
                      </el-radio>
                    </el-radio-group>
                  </span>
                </div>
                <!-- 只在第一层展示 -->
                <span class="treeoperate" v-if="data.id === treeData?.[0].id" @click.prevent="handleToggle">
                  {{treeStatue ? '收起' : '展开'}}子部门
                </span>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { computed, ref, unref, watch, type PropType } from 'vue'
import type { ElTree } from 'element-plus'
import type { DeComponentTreeVO } from '../type'

const emit = defineEmits(['update:data', 'updateValue'])

const treeRef = ref<InstanceType<typeof ElTree>>()
const treeStatue = ref<boolean>(false) // 组织架构展开收起状态，默认收起
const selectDataValue = ref('')

const personnelSelectionInput = ref('')

const props = defineProps({
  data: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  },
  checkedDep: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  }
})

const treeData = computed(() => {
  return unref(props.data)
})

watch(() => props.checkedDep, async (checkedUser) => {
  console.log(checkedUser, 'checkedDep', treeData.value)
  selectDataValue.value = checkedUser?.[0]?.id || ''
}, {
  immediate: true,
  deep: true
})

// 节点点击
function nodeClick (currentNode, node) {
  console.log(currentNode)
  console.log(node)
  // selectDataValue.value = currentNode.id
  // emit('updateValue', [currentNode])
}
const filterNode = (value: string, data: Recordable) => {
  if (!value) return true
  return data?.name.includes(value)
}
watch(personnelSelectionInput, (val) => {
  treeRef.value!.filter(val)
})

/**
 * 展开收起全部
 */
function handleToggle () {
  treeStatue.value = !treeStatue.value
  console.log(treeRef.value?.store)
  let nodesData = treeRef.value?.store.nodesMap
  for (let i in nodesData) {
    if(nodesData[i].childNodes && nodesData[i].childNodes.length > 0) {
      nodesData[i].expanded = treeStatue.value
    }
  }
}

function nodeToggle (data, currentNode) {
  console.log(data)
  console.log(currentNode)
  setTimeout(() => {
    const nodesData = treeRef.value?.store.nodesMap
    let expandedVal = false
    for (let i in nodesData) {
      if(nodesData[i].expanded) {
        expandedVal = true
      }
    }
    treeStatue.value = expandedVal
  }, 0)
}

function selectChange (val: DeComponentTreeVO) {
  emit('updateValue', [val])
}

</script>

<style lang="scss" scoped>
.organizational {
  @include flex-center(row, normal, normal);
  height: 100%;
  .left {
    width: 284px;
    padding: 16px 16px 16px 0;
    .custom-tree-node {
      width: calc(100% - 24px);
      line-height: 12px;
      // @include flex-center(row, space-between, center);
      &-item {
        .treelabel {
          width: 100%;
          :deep(.custom-tooltip-content) {
            text-align: left !important;
          }
        }
        .treeoperate {
          font-size: $font-size-mini;
          color: $primary-color;
          margin-right: 16px;
        }
        &-block {
          @include flex-center(row, normal, center);
        }
      }
      .treetop {
        @include flex-center(row, space-between, center);
      }
    }
    .treeList {
      overflow-y: auto;
      height: calc(100% - 32px);
      :deep(.el-tree-node.is-expanded>.el-tree-node__children) {
        display: block;
        min-width: 100%;
      }
    }
  }
  .right {
    flex: 1;
    padding: 16px 0 16px 16px;
    overflow-y: auto;
    .childNodeItem {
      display: block;
      width: 100%;
    }
  }
  .personnelSelectionInput {
    border-radius: 4px;
    :deep(.el-input__wrapper) {
      box-shadow: none;
      background: #F4F4F5;
    }
    margin-bottom: 13px;
  }
}
</style>