<template>
  <div class="isTxt" v-if="isTxt">
    {{name || '-'}}
  </div>
  <el-cascader
    style="width: 100%"
    v-else
    v-model="innerValue"
    :options="list"
    :props="propsOpt"
    :disabled="renderConfig.disabled"
    @visible-change="remoteMethod"
    @change="handleChange"
    clearable
  />
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, unref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { getCascadeLabel } from '@/utils/util'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue', 'refreshDataList'])
import { arrayToTree } from '@/utils/util'
import { getPageComponentData, getPageComponentDataEchoAcquire } from '@/apis/customerManage'
import { isNullOrUndefOrEmpty } from '@/utils/is'

const innerValue = computed({
  get () {
    console.log(props.modelValue, 'props.modelValue12333')
    return props.modelValue
  },
  set (newVal) {
    console.log(newVal, 'opop')
    emit('update:modelValue', newVal)
  }
})
const propsOpt = ref({ children: 'childList', label: 'label', value: 'value' })
const name = ref('')
function listTurnToTree (list: Recordable[]) {
  let res = []
  res = arrayToTree(list, '') as Recordable[]
  return res
}
const flag = ref(true)
const list = ref<Recordable[]>([])
onMounted(() => {
  console.log(props.data, 'props.data123', innerValue)
  if (props.data.dataMark !== 2) {
    const dataList = JSON.parse(JSON.stringify(props.renderConfig.dataList))
    list.value = listTurnToTree(dataList) as Recordable[] || []
    getName(dataList)
  }
  if(props.data.dataMark === 2 && innerValue.value.length) {
    console.log(innerValue.value, 'innerValue.value22')
    const postData = {
      columnId: props.data.id,
      echoData: innerValue.value.join(','),
      tenantId: props.data.tenantId || 0
    }
    console.log(postData, 'postData77778')
    getPageComponentDataEchoAcquire({ ...postData }).then(res => {
      // 根据level处理成级联数据
      console.log(res)
      const arr: Recordable[] = []
      const item1 = res.find(item => item.level === '0')
      const item2 = res.find(item => item.level === '1')
      const item3 = res.find(item => item.level === '2')
      if (item1) {
        arr[0] = { ...item1, childStage: [], stageName: item1.label, stageValue: item1.value }
      }
      if (item2) {
        arr[0].childStage[0] = { ...item2, childStage: [], stageName: item2.label, stageValue: item2.value }
      }
      if (item3) {
        arr[0].childStage[0][0] = { ...item3, childStage: [], stageName: item3.label, stageValue: item3.value }
      }
      propsOpt.value = { children: 'childStage', label: 'stageName', value: 'stageValue' }
      list.value = arr || []
      console.log(list, 'list885')
      getName(res)
    })
  }
})

function remoteMethod (bool: boolean) {
  if (unref(flag) && props.data.dataMark === 2) {
    const postData = {
      columnId: props.data.id,
      columnUseScene: props.data.columnUseScene,
      tableCode: props.data.tableCode,
      tenantId: props.data.tenantId || 0
    }
    getPageComponentData(postData).then(res => {
      flag.value = false
      // 客户阶段是这种格式，我也不知道还会有什么格式
      propsOpt.value = { children: 'childStage', label: 'stageName', value: 'stageValue' }
      list.value = res.map(item => {
        if (item.childStage) {
          item.childStage = item.childStage.map(sub => {
            return {
              ...sub,
              stageValue: String(sub.stageValue)
            }
          })
        }
        return { ...item, stageValue: String(item.stageValue) }
      }) || []
      emit('refreshDataList', res)
    })
  }
}
function handleChange (val: any) {
  const nameArr = []
  // 写点垃圾代码，哈哈
  const current = list.value.find(item => item.stageValue === val[0])
  if(current) {
    nameArr.push(current.stageName)
    const current1 = current.childStage?.find(item => item.stageValue === val[1])
    if (current1) {
      nameArr.push(current1.stageName)
      const current2 = current1.childStage?.find(item => item.stageValue === val[3])
      if (current2) {
        nameArr.push(current2.stageName)
      }
    }
  }
  name.value = nameArr.join('/')
}

function getName (list: Recordable[]) {
  console.log(list, '111s')
  if (isNullOrUndefOrEmpty(list) || isNullOrUndefOrEmpty(innerValue.value)) {
    name.value = '-'
    return
  }
  name.value = getCascadeLabel(innerValue.value, list)
}

</script>