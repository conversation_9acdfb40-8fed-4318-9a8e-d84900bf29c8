import type { RouteRecordRaw } from 'vue-router'
import { router } from '@/router/index'
const modules = import.meta.glob('../views/**/*.vue')

// import {} from 'vue-router'

/**
* @author：何宇
* @desc：获取基本静态路由
*/
export function getWhiteRouterList (rootRoute: RouteRecordRaw[]) {
  // 基本静态路由白名单
  const result: string[] = []
  const getRouteNames = (array: any[]) =>
    array.forEach((item) => {
      result.push(item.name)
      getRouteNames(item.children || [])
    })
  getRouteNames(rootRoute)
  return result
}

/**
* @author：何宇
* @desc：平铺路由还原成树
*/
export function ruoterListToTree (arr:RouteRecordRaw[]){
  const currentArr = arr.map(item => {
    const parent = item.path.split('/')
    parent.pop()
    return {
      ...item,
      parent: item.path === '/' ? '' : (parent.join('/') || '/')
    }
  })
  const buildTree = (list:any[], parentPath:string) => {
    const tree:RouteRecordRaw[] = []
    const id = parentPath || ''
    for(let i = 0;i < list.length;i++){
      if(list[i].parent === id){
        tree.push({
          ...list[i],
          children: buildTree(list, list[i].path)
        })
      }
    }
    return tree
  }
  return buildTree(currentArr, '')
}

// 本地路由渲染为菜单
export function formatLocalMenu (routes:any[], path: string = ''){
  return routes.map((el: any) => {
    el.path = path + '/' + el.path
    if (el.children) {
      el.children = formatLocalMenu(el.children, el.path)
    } else {
      el.children = []
    }
    return {
      icon: el?.meta?.icon,
      name: el?.meta?.title,
      path: el.path,
      children: el.children
    }
  })
}

// 动态路由渲染为菜单
export function formatOriginMenu (routes:any[], path: string = ''){
  return routes.map((el: any) => {
    // 兼容绝对路径和相对路径
    if(el?.path?.indexOf('/') === -1){
      el.path = path + '/' + el?.path
    } else {
      el.path = '/' + el?.path
    }
    // el.path = path + '/' + el.path
    if (el.children) {
      el.children = formatOriginMenu(el.children, el.path)
    } else {
      el.children = []
    }
    return {
      id: el.id,
      icon: el.icon,
      name: el.name,
      path: el.path,
      code: el.code,
      children: el.children,
      resourceList: el.resourceVOList || [],
      showed: el.showed
    }
  })
}

// 获取component文件路径
const loadView = (viewPath:string) => {
  const path = modules[`../views${viewPath}/index.vue`]
  if(!path) {
    throw Error(`未找到对应的文件路径：../views${viewPath}/index.vue  请检查文件目录及文件名与路由path是否一致！`)
    return ''
  } else {
    return modules[`../views${viewPath}/index.vue`]
  }
}

// 动态路由渲染为路由
export function formatOriginRoute (routes:any[], path: string = ''){
  return routes.map((el: any) => {
    el.path = path + '/' + el.path
    if (el.children) {
      el.children = formatOriginRoute(el.children, el.path)
    } else {
      el.children = []
    }
    return {
      name: el.code,
      path: el.path,
      meta: {
        icon: el.icon,
        title: el.name
      },
      children: el.children,
      component: loadView(el.path)
    }
  })
}
// 动态添加路由
export function renderSyncRoutes (routes:RouteRecordRaw[]){
  console.log(modules)
  routes.map(route => {
    router.addRoute('Layout', route)
  })
  console.log(router.getRoutes())
}
