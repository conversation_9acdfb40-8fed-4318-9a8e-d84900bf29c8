import { ref } from 'vue'
import type { ColumnType } from '@/types/table'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import ChannelSelect from './components/channelSelect.vue'
import { ElDatePicker } from 'element-plus'
import { CityCascader } from '@/components/business/cityCascader/index'
import { useDateRangePicker } from '@/hooks/useDateRangePicker'
import { channelTypeOption, equipmentTypeOption } from '@/enums/carlife'
import { fetchGetCarChannelList } from '@/apis/carlife'
import SelectAllV2 from '@/components/business/selectAllV2/index.vue'

const { calendarChange, disabledDate, visibleChange } = useDateRangePicker()

const channelNameList = ref<Recordable[]>([])
fetchGetCarChannelList({
  model: {},
  pageSize: 9999,
  pageNum: 1
}).then((res:any) => {
  channelNameList.value = res.records?.map((item:any) => {
    return {
      label: item.name,
      value: item.id
    }
  }) || []
})

export const userManage = {
  searchFormData: ref<FormSchema[]>([
    {
      fieldName: '用户姓名',
      component: 'Input',
      key: 'userName',
      val: ''
    },
    {
      fieldName: '用户手机号',
      component: 'Input',
      key: 'phone',
      val: ''
    },
    {
      fieldName: '设备',
      component: 'Select',
      key: 'device',
      val: '',
      options: equipmentTypeOption
    }, 
    {
      fieldName: '渠道来源',
      key: 'channelIds',
      val: [],
      options: channelTypeOption,
      componentProps: {
        clearable: true,
        multiple: true
      },
      component: 'Render',
      render: (modelValue:string) => {
        return <SelectAllV2 v-model={modelValue} list={channelNameList.value}></SelectAllV2>
      }
    },
    {
      key: 'visitTime',
      fieldName: '用户最近活跃时间',
      component: 'DateRange',
      val: []
    }
  ]),
  columns: ref<ColumnType[]>([
    { key: 'id', title: '用户ID', width: 180 },
    { key: 'realName', title: '姓名', width: 100 },
    { key: 'phone', title: '手机号', width: 150 },
    { key: 'loginTime', title: '登录时间', width: 160 },
    { key: 'lastActiveTime', title: '用户最近活跃时间', width: 160 },
    { key: 'loginCount', title: '登录次数' },
    { key: 'city', title: '贷款城市' },
    { key: 'device', title: '设备',
      render ({ cellData }){
        return <>
          <span>{equipmentTypeOption.find(i => i.value === cellData.device)?.label || '--'}</span>
        </>
      }
    },
    { key: 'channelName', title: '渠道来源' },
    { key: 'channelType', title: '渠道类型',
      render ({ cellData }){
        return <>
          <span>{channelTypeOption.find(i => i.value === cellData.channelType)?.label || '--'}</span>
        </>
      }
    },
    { key: 'productName', title: '产品名称' },
    { key: 'applyCount', title: '申请次数',
      render ({ cellData }){
        return <>
          <span>{ cellData.applyCount || 0 }</span>
        </>
      } 
    },
    { key: 'loanAmount', title: '放款金额',
      render ({ cellData }){
        return <>
          <span>{ cellData.loanAmount || '-' }</span>
        </>
      } },
    { key: 'orderCount', title: '订单数量',
      render ({ cellData }){
        return <>
          <span>{ cellData.orderCount || 0 }</span>
        </>
      } 
    },
    {
      key: 'operate',
      title: '操作',
      fixed: 'right',
      width: 80,
      slot: 'operateSlot'
    }
  ])
}

export const userDetailBaseInfoEnum = [
  { value: 'userId', label: '用户ID' },
  { value: 'realName', label: '用户姓名' },
  { value: 'age', label: '年龄' },
  { value: 'phone', label: '手机号' },
  { value: 'createTime', label: '注册日期' },
  { value: 'idCard', label: '身份证号' },
  { value: 'city', label: '所在城市' },
  { value: 'carNo', label: '车牌号码' },
  { value: 'vin', label: '车辆识别代号' },
  { value: 'carMortgageStatus', label: '车辆状态' }
]

export const userApplyInfoEnum = [
  { value: 'productName', label: '产品名称' },
  { value: 'productProviderName', label: '资方名称' },
  { value: 'sfLoanAmount', label: '放款金额' },
  { value: 'sfTerm', label: '贷款期限' },
  { value: 'sfProductRate', label: '贷款利率（%）' },
  { value: 'applyTime', label: '申请时间' }
]

export const userResourceInfoEnum = [
  { value: 'channelSource', label: '渠道来源' },
  { value: 'channelType', label: '渠道类型' },
  { value: 'visitTime', label: '访问时间' },
  { value: 'device', label: '设备' }
]

export const rotateFlagEnum = [
  { label: '否', value: 0 },
  { label: '是', value: 1 }
]
/** 车辆情况枚举 */
export const carStatusEnum = [
  { label: '无车产', value: 0 },
  { label: '有车产，不抵押', value: 10 },
  { label: '有车产，可抵押', value: 20 }
]

/** 公积金枚举 */
export const providentFundEnum = [
  { label: '无公积金', value: 0 },
  { label: '缴纳未满6个月', value: 10 },
  { label: '缴纳6个月以上', value: 20 }
]
/** 留资来源枚举 */
export const capitalSourceEnum = [
  { label: 'H5', value: 1 },
  { label: 'API', value: 2 }
]

/** 房产情况枚举 */
export const houseStatusEnum = [
  { label: '无房产', value: 0 },
  { label: '有房产，不抵押', value: 10 },
  { label: '有房产，可抵押', value: 20 }
]

/** 借款用途枚举 */
export const loanPurposeEnum = [
  { label: '消费贷', value: 10 },
  { label: '结婚贷', value: 20 },
  { label: '购房贷', value: 30 },
  { label: '装修贷', value: 40 },
  { label: '购车贷', value: 50 },
  { label: '教育贷', value: 60 },
  { label: '旅游贷', value: 70 },
  { label: '医疗贷', value: 80 },
  { label: '其他贷', value: 90 }
]

/** 工资发放形式枚举 */
export const salaryPaymentMethodEnum = [
  { label: '银行卡', value: 10 },
  { label: '现金', value: 20 }
]

/** 职业枚举 */
export const occupationEnum = [
  { label: '上班族', value: 200 },
  { label: '自由职业', value: 300 },
  { label: '企业主', value: 400 },
  { label: '个体户', value: 500 },
  { label: '公务员', value: 800 },
  { label: '其他', value: 900 }
]

/** 社保枚举 */
export const socialSecurityEnum = [
  { label: '无社保', value: 0 },
  { label: '缴纳未满6个月', value: 10 },
  { label: '缴纳6个月以上', value: 20 }
]

/** 芝麻分枚举 */
export const sesameScoreEnum = [
  { label: '无芝麻分', value: 0 },
  { label: '600分以下', value: 10 },
  { label: '600~650分', value: 20 },
  { label: '650~700分', value: 30 },
  { label: '700分以上', value: 40 }
]

/** 当前单位工龄枚举 */
export const workDurationEnum = [
  { label: '0~6个月', value: 10 },
  { label: '6~12个月', value: 20 },
  { label: '12个月以上', value: 30 }
]

export const getEnumLabel = (enumArray, value) => {
  const enumItem = enumArray.find(item => item.value === value)
  return enumItem ? enumItem.label : '-'
} 

export const leaveInformationPageManage = {
  searchFormData: ref<FormSchema[]>([

    {
      fieldName: '用户ID',
      component: 'Input',
      key: 'userId',
      val: ''
    },
    {
      fieldName: '用户手机号',
      component: 'Input',
      key: 'userPhone',
      val: ''
    },
    {
      fieldName: '贷款城市',
      key: 'city',
      component: 'Render',
      val: '',
      render: (modalValue:any) => {
        return <CityCascader v-model={modalValue} cascaderProps={{ multiple: false, emitPath: false }} show-all-levels={false} style="width: 100%" />
      }
    },
    {
      fieldName: '来源渠道',
      component: 'Render',
      key: 'channelId',
      val: '',
      render: (modelValue:string) => {
        return <ChannelSelect v-model={modelValue} style={{ width: '100%' }}></ChannelSelect>
      }
    },
    {
      key: 'time',
      component: 'Render',
      val: '',
      fieldName: '留资提交时间',
      render: (modalValue:any) => {
        return <ElDatePicker
          style="width: 100%"
          v-model={modalValue}
          type="daterange"
          range-separator="-"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          clearable={false}
          disabledDate={(e) => disabledDate(e, 30)}
          onCalendarChange={calendarChange}
          onVisibleChange = {visibleChange}
        />
      }
    },
    {
      fieldName: '留资变更次数≥',
      component: 'Select',
      key: 'updateCount',
      val: '',
      options: [
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '5', value: 5 }
      ],
      componentProps: {
        placeholder: '请选择',
        clearable: true
      }
    },
    {
      fieldName: '用户MD5',
      component: 'Input',
      key: 'userMd5',
      val: ''
    }
  ]),
  columns: ref<ColumnType[]>([
    { key: 'userId', title: '用户ID', width: 180 },
    { key: 'realName', title: '姓名', width: 80 },
    { key: 'phone', title: '手机号', width: 110 },
    { key: 'userMd5', title: '用户MD5', width: 260 },
    { key: 'channelName', title: '渠道来源' },
    { key: 'source', title: '留资来源', render: ({ cellData }) => {
      return <>
        <span>{getEnumLabel(capitalSourceEnum, cellData.source)}</span>
      </>
    } },
    { key: 'city', title: '贷款城市' },
    { key: 'gjjScop', title: '公积金情况', width: 120, render: ({ cellData }) => {
      return <>
        <span>{getEnumLabel(providentFundEnum, cellData.gjjScop)}</span>
      </>
    } },
    { key: 'sbScop', title: '社保情况', render: ({ cellData }) => {
      return <>
        <span>{getEnumLabel(socialSecurityEnum, cellData.sbScop)}</span>
      </>
    } },
    { key: 'house', title: '房产情况', render: ({ cellData }) => {
      return <>
        <span>{getEnumLabel(houseStatusEnum, cellData.house)}</span>
      </>
    } },
    { key: 'car', title: '车产情况', width: 120, render: ({ cellData }) => {
      return <>
        <span>{getEnumLabel(carStatusEnum, cellData.car)}</span>
      </>
    } },
    { key: 'sesameScore', title: '芝麻分情况', width: 100, render: ({ cellData }) => {
      return <>
        <span>{getEnumLabel(sesameScoreEnum, cellData.sesameScore)}</span>
      </>
    } },
    { key: 'incomeMonth', title: '月收入' },
    { key: 'completeDataTime', title: '留资提交时间', width: 160 },
    { key: 'updateCount', title: '留资变更次数', width: 110 },
    {
      key: 'operate',
      title: '操作',
      fixed: 'right',
      width: 80,
      slot: 'operateSlot'
    }
  ])
}

export const leaveInformationFieldEnum = {
  baseInfo: [
    {
      value: 'userId',
      label: '用户ID'
    },
    {
      value: 'realName',
      label: '用户姓名'
    },
    {
      value: 'age',
      label: '年龄'
    },
    {
      value: 'phone',
      label: '手机号'
    },
    {
      value: 'updateCount',
      label: '留资变更次数'
    },
    {
      value: 'ifAuth',
      label: '要素是否通过',
      render: (value:number) => {
        return getEnumLabel(rotateFlagEnum, value)
      }
    }
  ],
  leaveInformationInfo: [
    {
      value: 'createTime',
      label: '提交时间'
    },
    {
      value: 'channelName',
      label: '渠道来源'
    },
    {
      value: 'channelTypeName',
      label: '渠道类型'
    },
    {
      value: 'city',
      label: '贷款城市'
    },
    {
      value: 'gjjScop',
      label: '公积金',
      render: (value:number) => {
        return getEnumLabel(providentFundEnum, value)
      }
    },
    {
      value: 'sbScop',
      label: '社保',
      render: (value:number) => {
        return getEnumLabel(socialSecurityEnum, value)
      }
    },
 
    {
      value: 'house',
      label: '房产情况',
      render: (value:number) => {
        return getEnumLabel(houseStatusEnum, value)
      }
    },
    {
      value: 'car',
      label: '车产情况',
      render: (value:number) => {
        return getEnumLabel(carStatusEnum, value)
      }
    },
    {
      value: 'sesameScore',
      label: '芝麻分',
      render: (value:number) => {
        return getEnumLabel(sesameScoreEnum, value)
      }
    },
    {
      value: 'incomeMonth',
      label: '月收入'
    },
    {
      value: 'payoffType',
      label: '工资发放形式',
      render: (value:number) => {
        return getEnumLabel(salaryPaymentMethodEnum, value)
      }
    },
    {
      value: 'workingYears',
      label: '当前单位工龄',
      render: (value:number) => {
        return getEnumLabel(workDurationEnum, value)
      }
    },
  
    {
      value: 'profession',
      label: '职业',
      render: (value:number) => {
        return getEnumLabel(occupationEnum, value)
      }
    }, {
      value: 'loanUse',
      label: '借款用途',
      render: (value:number) => {
        return getEnumLabel(loanPurposeEnum, value)
      }
    }, {
      value: 'loanMoney',
      label: '贷款金额',
      render: (value:number) => {
        return value ? `${value}万` : '-'
      }
    }
  ]
}