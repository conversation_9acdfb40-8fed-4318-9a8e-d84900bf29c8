<template>
  <RxkDrawer
    v-model="drawer"
    :title="title"
    :btnLoading="btnLoading"
    @close="resetForm(ruleFormRef)"
    @confirm="(fn) => {submitForm(ruleFormRef, fn)}"
  >
    <template #content>
      <div class="tw-p-[24px]">
        <el-form
          ref="ruleFormRef"
          :model="formState"
          :rules="rules"
          label-width='90'
          class="reset-form-label"
          label-position="left"
        >
          <el-form-item label="部门名称" prop="name">
            <el-input v-model="formState.name" maxlength="10" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="上级部门" prop="parentId">
            <el-tree-select
              style="width: 100%"
              v-model="formState.parentId"
              :data="treeData"
              :props="{ label: 'name', value: 'id', key: 'id' }"
              check-strictly
              :render-after-expand="false"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item label="部门主管" prop="managerId">
            <el-select
              style="width: 100%"
              filterable
              clearable
              :loading="selectLoading"
              :filter-method="getManageList"
              placeholder="请选择"
              v-model="formState.managerId">
              <el-option v-for="item in managerOptions"
                         :value="item.id"
                         :label="item.realName"
                         :key="item.id" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </template>
  </RxkDrawer>
</template>
<script setup lang="ts">
import { reactive, ref, defineProps, type PropType, computed, onMounted } from 'vue'
import { getDepartmentManager, addDepartment, updateDepartment } from '@/apis/departmentManage'
import { getUserList } from '@/apis/userManage'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { RxkDrawer } from '@/components/common/RxkDrawer'

interface FormState {
  name: string,
  parentId: number | undefined,
  managerId: string | undefined
}

const props = defineProps({
  handleClose: {},
  parentId: Number,
  departmentInfo: {
    type: Object as PropType<{name: string; id: string; parentId:number}>,
    default: () => {}
  },
  treeData: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['handleClose', 'handleSubmit'])

const title = computed(() => {
  return props.parentId ? '添加子部门' : props.departmentInfo?.id ? '编辑部门' : '添加部门'
})

let drawer = ref(true)

const btnLoading = ref(true)

// Form
const ruleFormRef = ref<FormInstance>()
const formState = reactive<FormState>({
  name: '',
  parentId: undefined,
  managerId: undefined
})
const rules = reactive<FormRules<FormState>>({
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' }
  ],
  parentId: [
    { required: true, message: '请选择上级部门', trigger: 'blur' }
  ]
})
const managerOptions = ref<any[]>([])

onMounted(async () => {
  await getManageList()
  if(props?.departmentInfo?.id){
    await insetDepartment()
  }
  if(props.parentId){
    formState.parentId = props.parentId
    await departmentChange(props.parentId)
  }
})

async function insetDepartment (){
  formState.name = props.departmentInfo.name
  formState.parentId = props.departmentInfo.parentId
  await departmentChange(props.departmentInfo?.id)
}

// 根据部门id查询对于主管; 根据部门id查询人员
async function departmentChange (data:any){
  formState.managerId = undefined
  try {
    const res = await getDepartmentManager(data)
    formState.managerId = res.supervisorId
  } catch(e){
    console.log(e)
  }
}

const selectLoading = ref(false)
async function getManageList (username = undefined as any){
  try {
    selectLoading.value = true
    const res = await getUserList({
      pageSize: 50,
      pageNum: 1,
      model: {
        username
      }
    })
    managerOptions.value = res.records
  } finally {
    selectLoading.value = false
  }
}

const submitForm = async (formEl: FormInstance | undefined, fn:Fn) => {
  if (!formEl) {
    fn && fn()
    return
  }
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        let params:any = { ...formState, id: props?.departmentInfo?.id }
        let api = props?.departmentInfo?.id ? updateDepartment : addDepartment
        await api(params)
        ElMessage({
          message: props?.departmentInfo?.id ? '编辑成功' : '新增成功',
          type: 'success'
        })
        emits('handleSubmit')
        resetForm(formEl)
      } finally {
        fn && fn()
      }
    } else {
      fn && fn()
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  emits('handleClose')
}

</script>
<style lang="scss" scoped></style>
