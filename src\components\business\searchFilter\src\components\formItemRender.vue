<script lang="tsx">
import { type PropType } from 'vue'
import type { renderFn } from '@/types/common'
export default {
  name: 'form-item-render',
  props: {
    renderFn: {
      type: Function as PropType<renderFn>,
      default: () => {}
    },
    modalValue: {}
  },
  setup (props: Recordable) {
    return () => {
      return props.renderFn(props.modalValue)
    }
  }
}
</script>

<style scoped lang="scss"></style>
