export interface ShowValueData {
  id?: Recordable;
  realName?: string;
  departmentVO?: {
    id?: string
  };
  [x: string]: any;
}
export interface DynamicParameters {
  acquireCurrent: number; // 读取当前操作者
  acquireCurrentFather: number; // 读取当前操作者上级
}
export interface UserVOList {
  id: string;
  realName?: string;
  username?: string;
  departmentVO: DeComponentTreeVO[];
  jobStatus?: Recordable;
  checked?: boolean;
  [key: string]: any;
}

export interface DeComponentTreeVO {
  id: string;
  name?: string; // 部门名称
  parentId?: string;
  children?: Recordable[];
  userVOList?: UserVOList[];
  indeterminate?: boolean
}

export interface RoleVOList {
  userVOList?: UserVOList[];
  id: string;
  name: string;
  checked?: boolean;
  indeterminate?: boolean
}

export interface ComponentData {
  deComponentTreeVO?: DeComponentTreeVO[]; // 组织架构
  userVOList?: UserVOList[]; // 人员集合
  userResignVOList?: UserVOList[]; // 离职人员集合
  roleVOList?: RoleVOList[]; // 角色集合
}

export interface defaultValueDataItem {
  value: string;
  type: DataType
}

export interface BasicProps {
  isIncludeChild?: boolean; // 父部门不包含子部门员工
  custom?: boolean; // 是否自定义触发内容
  defaultValueData?: defaultValueDataItem[]; // 回显的值
  visible?: boolean;
  paramsData?: any; // 接口需要的参数
  activeName?: string; // panelName
  configuration?: boolean; // 是否需要参数管理配置
  dynamicParameters?: DynamicParameters; // 动态参数
  selectedShow?: boolean; // 选中所有可见选项 
  data?: ComponentData;// 数据
  api?: (...arg: any) => Promise<any>;// 获取数据接口
  placeholder?: string;
  visibleBeforeFn?: () => Promise<any>; // 显示组件之前
  placement?: string;
  collapseTags?: boolean;
  disabled?: boolean;
  onlyDep?: boolean; // 只选择部门
  validateFn?: () => Promise<any>; // 对数据做校验的方法
  [key: string]: any;
  showSure?: boolean
}

export enum DataType {
  DEP='DEP', // 部门
  USER='USER', // 人员
  ROLE='ROLE', // 角色
}