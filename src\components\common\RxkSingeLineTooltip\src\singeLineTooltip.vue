<template>
  <!-- 单行省略样式、鼠标移入显示全部信息 -->
  <div>
    <el-tooltip
      effect="light"
      :content="content"
      placement="top"
      :disabled="isShowTooltip"
    >
      <span class="custom-tooltip-content" @mouseover="onMouseOver">
        {{ tooltipContent }}
      </span>
    </el-tooltip>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
const props = defineProps({
  tooltipContent: {
    type: [String, Number],
    default: '',
  },
});
const isShowTooltip = ref(true);
const content = computed(() => {
  return '' + props.tooltipContent;
});
// 表格鼠标移入事件
const onMouseOver = (event: { target: Element }) => {
  isShowTooltip.value = !(
    event?.target.scrollWidth > event?.target.clientWidth
  );
};
</script>

<style lang="scss" scoped>
.custom-tooltip-content {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  text-align: center;
  line-height: inherit;
  width: 100%;
  display: inline-block;
}
</style>
