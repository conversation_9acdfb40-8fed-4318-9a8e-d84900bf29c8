<template>
  <el-input
    v-bind="getBindValue"
    v-model="input"
    @input="inputFn"
    @blur="blurFn"
    @focus="focusFn"
    @change="changeFn"
    clearable
  >
    <template #suffix>
      <slot name="suffix"/>
    </template>
    <template #prefix>
      <slot name="prefix"/>
    </template>
    <template v-if="append" #append>
      <slot name="append"/>
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { computed, type PropType } from 'vue'
import type { Component } from 'vue'
defineOptions({
  name: 'RxkInput'
})
const props = defineProps({
  clearable: {
    type: Boolean,
    default: false
  },
  modalValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入'
  },
  // 最大输入长度
  maxlength: {
    type: [String, Number],
    default: ''
  },
  minlength: {
    type: [String, Number],
    default: ''
  },
  'prefix-icon': {
    type: Object as PropType<string | Component>,
    default: () => {}
  },
  'suffix-icon': {
    type: Object as PropType<string | Component>,
    default: () => {}
  },
  'show-word-limit': {
    type: Boolean,
    default: false
  },
  autosize: {
    type: Object,
    default: () => ({ minRows: 2, maxRows: 4 })
  },
  type: {
    type: String as PropType<'text' | 'number' | 'password' | 'textarea'>,
    default: 'text'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  max: {
    type: Number,
    default: 0
  },
  min: {
    type: Number,
    default: 0
  },
  append: {
    type: Boolean,
    default: false
  }
})
const newProps: Obj = { ...props }
if (Object.keys(newProps).includes('modalValue')) {
  delete newProps.modalValue
}
const getBindValue = computed(() => (props))

const emit = defineEmits(['update:modalValue', 'input', 'blur', 'focus', 'change'])
const input = computed({
  get () {
    return props.modalValue
  },
  set (value) {
    emit('update:modalValue', value)
  }
})

function inputFn (val: string) {
  emit('input', val)
}

function blurFn () {
  emit('blur')
}

function focusFn () {
  emit('focus')
}

function changeFn () {
  emit('change')
}
</script>
