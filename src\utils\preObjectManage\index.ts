import {
  SingleLineText,
  MultilineText,
  IdCard,
  Email,
  Phone,
  CreditCode,
  Money,
  Percent,
  Numbers,
  Landline,
  Serial
} from './modules/input'
import {
  Select,
  MultipleChoice,
  MultipleCheckbox,
  Address,
  SingleRadio,
  MultipleDepartment,
  Person,
  Department,
  PersonMultiple,
  Cascader
} from './modules/select'
import {
  Dates1,
  Dates2,
  Dates3
} from './modules/date'

import { Enclosure, Picture } from './modules/upload'

import {
  SubList,
  SubTable,
  PartingLine,
  AssociationQuery,
  AssociationForm,
  Button,
  Sign,
  SubField
} from './modules/advance'

import { Tags, ReferralPersonSource, ReferralPerson } from './modules/business'

export default {
  SingleLineText,
  MultilineText,
  Address,
  Person,
  PersonMultiple,
  Department,
  Enclosure,
  IdCard,
  Email,
  Picture,
  Phone,
  Select,
  MultipleChoice,
  Dates1,
  Dates2,
  Dates3,
  Numbers,
  Money,
  Percent,
  Cascader,
  MultipleCheckbox,
  CreditCode,
  MultipleDepartment,
  SubList,
  SubTable,
  PartingLine,
  AssociationQuery,
  AssociationForm,
  Button,
  Sign,
  SingleRadio,
  SubField,
  Landline,
  Serial,
  Tags,
  ReferralPersonSource,
  ReferralPerson
}

export type SingleLineTextType= SingleLineText