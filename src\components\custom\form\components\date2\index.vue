<template>
  <div class="isTxt" v-if="isTxt">
    {{innerValue || '-'}}
  </div>
  <el-date-picker
    v-else
    style="width: 100%"
    v-model="innerValue"
    :type="renderConfig.type"
    :format="renderConfig.format"
    :value-format="renderConfig.format"
    :placeholder="renderConfig.placeholder"
    :start-placeholder="renderConfig.placeholder"
    :end-placeholder="renderConfig.placeholder"/>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})

</script>