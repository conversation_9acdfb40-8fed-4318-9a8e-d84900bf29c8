import { RxkAxios } from './axios'
import globalEnv from '@/configs'
import { ContentTypeEnum, ResponseCodeEnum, ResponseStatusEnum } from '@/enums/axios'
import { aes, createRequestSign, decryptResponse } from '@/utils/aes'
import qs from 'qs'
import type { CreateOptions } from '@/utils/axios/axiosModel'
import { deepMerge } from '@/utils/tools'
import { getToken } from '@/utils/auth'
import { isBlob } from '@/utils/is'
import { getFileName } from '@/utils/util'
import { ElMessage }from 'element-plus'
import { useAccountStore } from '@/stores/modules/account'
import { useResource } from '@/hooks/useResource'

import { useSimpleNotification } from '@/hooks/useSimpleNotification'

const { open } = useSimpleNotification()

const loading: any = null
const customOptions: CreateOptions = {
  baseURL: globalEnv.baseUrl || '',
  headers: { 'Content-Type': ContentTypeEnum.JSON },
  // 函数必须返回一个字符串，或 ArrayBuffer，或 Stream
  transformRequest: function (data, headers) {
    // 如果是json格式的请求头
    if (ContentTypeEnum.JSON.includes(<string>headers['Content-Type'])) {
      if (globalEnv.isNeedAes) {
        return aes.encrypt(JSON.stringify(data || {}))
      } else {
        // 格式：{"uid":"cs11","pwd":"000000als","username":"cs11","password":"000000als"}
        return JSON.stringify(data || {})
      }
    }
    if (ContentTypeEnum.FORM_URLENCODED.includes(<string>headers['Content-Type'])) {
      // 格式：uid=cs11&pwd=000000als&username=cs11&password=000000als
      return qs.stringify(data || {}, { indices: false })
    }
    return data
  },
  transform: {
    /**
     * 自定义请求拦截处理
     * */
    beforeRequestHook (config, requestOptions) {
      const { isNeedSign, isJoinTimestamp, withToken, loading } = requestOptions
      // const { apiUrl = '', urlPrefix } = requestOptions
      // console.log(getEnvMode(), 'getEnvMode')
      const { getMenuId } = useResource()
      const headers = config.headers || { 'Content-type': ContentTypeEnum.FORM_URLENCODED }
      const data = config.data || config.params || {}
      const headInfo = {
        timestamps: new Date().getTime()
      }
      if (isJoinTimestamp) {
        headers.timestamps = headInfo.timestamps
      }
      if (withToken) {
        const token = getToken()
        if (token) {
          headers.Authorization = token
        }
      }
      // 后端数据权限需要菜单id去判断
      if (!headers.MenuId && getMenuId()) {
        headers.MenuId = getMenuId()
      }
      const isJson = headers['Content-type'] === ContentTypeEnum.JSON
      if (isNeedSign) {
        const tempData = isJson ? data : Object.assign(headInfo, data)
        headers.sign = createRequestSign(tempData, isJson, headInfo.timestamps).toUpperCase()
        // headers.CipherText = true
      }
      // headers.UserId = '3'
      // headers.TenantId = '0'
      config.headers = headers
      if (loading) {
        // @ts-ignore
        ElLoading.service({
          lock: true,
          text: 'Loading'
        })
      }

      return config
    },
    /**
     * 自定义响应拦截处理
     * */
    afterResponseHook (response, requestOptions) {
      if (requestOptions.loading) {
        loading.close()
      }
      const { isReturnNativeResponse } = requestOptions
      // 是否要返回原生响应头
      if (isReturnNativeResponse) {
        return response
      }
      if (isBlob(response?.data)) {
        const filename = getFileName(response.headers['content-disposition'])
        if (filename) {
          return { data: response.data, filename }
        } else { // 错误情况,返回的是json格式
          // 解密
          const fileReader = new FileReader()
          fileReader.onload = function (e) {
            const result = e.target?.result
            const responseData = decryptResponse(result)
            try {
              // this.result  = 字符串类型的 response
              //  如果接口返回的数据，非二进制的话。可以根据response做出响应的判断。
              // const fileResult = JSON.parse(result)
              const msg = responseData.msg || ''
              ElMessage.error(msg)
              //  ps：JSON.parse()是Javascript中一个常用的 JSON 转换方法，JSON.parse()可以把JSON规则的字符串转换为JSONObject 反之就会报错。
              //  如果接口内容为二进制数据。那么JSON.parse解析失败, 抛出异常来到catch进行执行下载操作
              // }
            } catch (err:any) {
              throw new Error(err)
              // 当前构造函数内的this是指向它的实例对象的。所以在外层声明了一个this用于指向全局
            }
          }
          fileReader.readAsText(response.data as unknown as Blob)
          return Promise.reject('下载错误')
        }
      }
      // 进项接口解密处理
      const responseData = decryptResponse(response?.data)
      // 接口错误时报错
      if (!responseData) {
        return Promise.reject('请求出错,请稍后再试')
      }
      const { code, data, msg } = responseData || {}
      let errorMsg = msg
      if (code === ResponseCodeEnum.SUCCESS) {
        return data
      } else {
        if (code === 407) {
          const retryCount = Object.keys(data)[0]
          const retryDelayInMinutes = Object.values(data)[0]
          errorMsg = `您已连续输错${retryCount}次，请您${retryDelayInMinutes}分钟后再试`
        }
        // @ts-ignore
        open({
          title: '系统异常',
          message: errorMsg || '服务端请求错误',
          showClose: false,
          duration: 1500
        })
        
        return Promise.reject(errorMsg || '服务端请求错误')
      }
    },
    /**
     * axios请求拦截器
     * */
    requestInterceptors (config) {
      return config
    },
    /**
     * axios响应拦截器
     * */
    responseInterceptors: (res) => {
      return res
    },
    /**
     * axios响应错误处理
     * */
    responseInterceptorsCatch: (error) => {
      const { response } = error
      const accountStore = useAccountStore()
      if (response) {
        // 请求已发出，但服务器响应的状态码不在2xx范围内
        switch (response.status) {
          case ResponseStatusEnum.SERVER_ERROR: // 500
            open({
              title: '系统异常',
              message: '服务不可用，请联系管理员',
              showClose: false,
              duration: 1500
            })
            break
          case ResponseStatusEnum.UNAUTHORIZED: // 401
            open({
              title: '授权失败',
              message: '登录凭证已过期，请重新登录',
              showClose: false,
              duration: 1500
            })
            accountStore.logout(true)
            break
          case ResponseStatusEnum.FORBIDDEN: // 403
            open({
              title: '操作未授权',
              message: '暂未授权此操作，请联系管理员授权！',
              showClose: false,
              duration: 1500
            })
            break
          default:
            // 统一处理其他状态码
            open({
              title: '系统错误',
              message: '请求过程中发生错误，请稍后再试或联系管理员',
              showClose: false,
              duration: 1500
            })
            break
        }
      } else {
        // 请求未发出或网络错误
        open({
          title: '网络错误',
          message: '请求过程中发生错误，请稍后再试或联系管理员',
          showClose: false,
          duration: 1500
        })
      }
      return error
    }
  },
  requestOptions: {
    apiUrl: globalEnv.baseUrl || '', // 接口地址, 由不同环境提供,相当于axios的baseUrl
    urlPrefix: globalEnv.apiPrefix, // 请求地址前缀, 由不同环境提供,
    isJoinTimestamp: true, // 是否加时间戳
    isNeedSign: true, // 是否需要签名
    // 忽略重复请求
    ignoreCancelToken: false,
    isReturnNativeResponse: false, // 是否返回原生响应头
    withToken: true // 是否加入token
  }
}

function createAxios (opt?: Partial<CreateOptions>) {
  return new RxkAxios(deepMerge(customOptions, opt))
}

const request = createAxios()

export default request
