<template>
  <el-checkbox-group
    @change="change"
    v-model="checkList"
    class="rxk-checkbox-group"
    :size="size"
    :class="{'vertical': vertical}">
    <el-checkbox
      v-for="item in list"
      :key="item.value"
      :label="item.value"
      :disabled="item.disabled"
    >
      {{item.label}}
    </el-checkbox>

  </el-checkbox-group>
</template>
<script lang="ts" setup>

import type { PropType } from 'vue'
import type { List } from '@/types/common'
import { computed } from 'vue'
defineOptions({
  name: 'RxkCheckboxGroup'
})

const props = defineProps({
  list: {
    type: Array as PropType<List[]>,
    required: true
  },
  modelValue: {
    type: Array as PropType<number[] | string[]>,
    default: () => []
  },
  vertical: {
    type: Boolean,
    default: false
  },
  size: {
    type: String as PropType<'large' | 'default' | 'small'>,
    default: 'default'
  }
})
const emit = defineEmits(['update:modelValue', 'change'])
const checkList = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})
function change (val: number[] | string[]) {
  emit('change', val)
}
</script>
<style lang="scss">
@import "./index.scss";
</style>