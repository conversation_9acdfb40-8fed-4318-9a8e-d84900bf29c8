<template>
  <div class="custom-menu-item" v-for="(item, index) in list" :key="index">
    <el-sub-menu class="subMenu-item" :index="item.path || String(index)" v-if="!isEmpty(item.children) && childrenShowed(item.children)">
      <template #title>
        <span style="margin-right: 8px;" v-if="item.icon" :class="['iconfont icon', `icon-${item.icon}`]"/>
        <div :style="{marginRight: level< 2?'8px':'0', opacity: level < 3?'1':'0' }" v-else class="dot"/>
        <span class="menu-name"  v-if="!collapse">{{item.name}}</span>
      </template>
      <MenuItem :list="item.children" :level="level + 1"/>
    </el-sub-menu>
    <el-menu-item class="menu-item"
                  v-else
                  :index="item.path || String(index)"
                  @click="routerPage(item)">
      <template #title>
        <span style="margin-right: 8px;" v-if="item.icon" :class="['iconfont icon', `icon-${item.icon}`]"/>
        <div v-else  class="dot"  :style="{opacity: level < 3?'1':'0'}" />
        <span class="menu-name" v-if="!collapse">{{item.name}}</span>
      </template>
    </el-menu-item>
  </div>

</template>

<script setup lang="ts">
import MenuItem from './MenuItem.vue'
import type { PropType } from 'vue'
import type { RoleMenuType } from '@/types/menu'
import { isEmpty } from '@/utils/is'
import { useRouter } from 'vue-router'
import { useConfig } from '@/stores/modules/config'
import { closeShade } from '@/utils/pageShade'
defineProps({
  list: {
    type: Array as PropType<RoleMenuType[]>,
    required: true
  },
  collapse: Boolean,
  level: {
    type: Number,
    default: 1
  }
})

const config = useConfig()
const router = useRouter()
// 子菜单是否展示
function childrenShowed (children: RoleMenuType[]) {
  let flag = true
  if (children?.length) {
    flag = children.some(i => i.showed)
  }
  return flag
}
function routerPage (data: RoleMenuType) {
  router.push({
    path: data.path
  })
  // 移动端点击关闭menu
  if(config.layout.shrink) {
    config.toggleMenuExpand()
    closeShade()
  }
}

</script>
<style lang="css">
:root {
  --el-menu-level-padding: 5px;
}
</style>
<style lang="scss" scoped>
.custom-menu-item {
  .subMenu-item {
    :deep(.el-sub-menu__title) {
      line-height: 40px;
      height: 40px;
      opacity: .9;
      .icon {
        font-size: 18px;
      }
    }
  }
  .menu-item {
    height: 40px;
    line-height: 40px;
    opacity: .9;
    &:hover {
      background-color: #5687ff;
    }
    &.is-active {
      background-color: #5687ff;
    }
  }
}
.menu-name{
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
}
.dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  position: relative;
  box-sizing: content-box;
  flex-shrink: 0;
  &::after{
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #fff;
  }
}
</style>
