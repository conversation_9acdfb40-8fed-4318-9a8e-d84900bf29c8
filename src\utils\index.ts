import { unref } from 'vue'
import { saveAs } from 'file-saver'
import request from '@/utils/axios/index'
import { ContentTypeEnum } from '@/enums/axios'
import { ElMessage } from 'element-plus'
import { getFileName } from '@/utils/axios/utils'
import { environment } from '@/configs/constant'
import { isString, isNullOrUndefOrEmpty } from '@/utils/is'
/**
 * @desc：默认分页
 * */
export const defaultPagination = {
  pageSize: 20,
  currentPage: 1,
  pageSizeOptions: [20, 30, 40, 50, 100, 200],
  layout: 'total, sizes, prev, pager, next, jumper'
}

export function getDynamicProps<T extends Record<string, unknown>, U> (props: T): Partial<U> {
  const ret: Recordable = {}

  Object.keys(props).map((key) => {
    ret[key] = unref((props as Recordable)[key])
  })

  return ret as Partial<U>
}

/**
 * 导出excel
 * @param {*string} url 请求路径
 * @param {*Object} data 请求参数
 * @param {*POST | GET} method 请求方法 默认GET
 */
export function excelExport (url: string, data: any, method = 'POST', headers = { 'Content-Type': ContentTypeEnum.JSON }) {
  const upperMethod = method.toUpperCase()
  if (upperMethod === 'GET') {
    return new Promise((resolve: Fn, reject) => {
      request
      .get(url, data, {
        responseType: 'blob'
      }, { isReturnNativeResponse: true })
      .then(res => {
        const arr = res.headers['content-disposition'].split(';')
        let filename = ''
        arr.forEach((e: string) => {
          if (e.indexOf('filename') !== -1) {
            const codestr = e.substring(9, e.length - 5)
            filename = decodeURIComponent(codestr)
          }
        })
        const blob = new Blob([res.data])
        const name = filename ? filename + '.xlsx' : new Date().getTime() + '_' + parseInt(Math.random() * 10000) + '.xlsx'
        saveAs(blob, name)
        ElMessage.success('导出成功')
        resolve()
      }).catch(rej => {
        ElMessage.error('导出失败')
        reject()
      })
    })
  }
  if (upperMethod === 'POST') {
    return new Promise((resolve: Fn, reject) => {
      request
      .post(url, data, {
        responseType: 'blob', headers: {
          'Content-Type': ContentTypeEnum.JSON
        }
      }, { isReturnNativeResponse: true })
      .then(res => {
        const arr = res.headers['content-disposition'].split(';')
        let filename = ''
        arr.forEach((e: string) => {
          if (e.indexOf('filename') !== -1) {
            const codestr = e.substring(9, e.length - 5)
            filename = decodeURIComponent(codestr)
          }
        })
        const blob = new Blob([res.data])
        const name = filename ? filename + '.xlsx' : new Date().getTime() + '_' + parseInt(Math.random() * 10000) + '.xlsx'
        saveAs(blob, name)
        ElMessage.success('导出成功')
        resolve()
      }).catch(rej => {
          
        ElMessage.error('导出失败')
        reject()
      })
    })
  }
}
// 数据转变
export const upper = (number:any) => {
  const units = '个十百千万@#%亿^&~'
  const chars = '零一二三四五六七八九'
  const a = (number + '').split('')
  const s = []
  if (a.length > 12) {
    throw new Error('too big')
  } else {
    for (let i = 0, j = a.length - 1; i <= j; i++) {
      if (j === 1 || j === 5 || j === 9) {
        // 两位数 处理特殊的 1*
        if (i === 0) {
          if (a[i] !== '1') s.push(chars.charAt(a[i]))
        } else {
          s.push(chars.charAt(a[i]))
        }
      } else {
        s.push(chars.charAt(a[i]))
      }
      if (i != j) {
        s.push(units.charAt(j - i))
      }
    }
  }
  return s.join('').replace(/零([十百千万亿@#%^&~])/g, function (m, d, b) {
    // 优先处理 零百 零千 等
    b = units.indexOf(d)
    if (b !== -1) {
      if (d === '亿') return d
      if (d === '万') return d
      if (a[j - b] === '0') return '零'
    }
    return ''
  }).replace(/零+/g, '零').replace(/零([万亿])/g, function (m, b) {
    // 零百 零千处理后 可能出现 零零相连的 再处理结尾为零的
    return b
  }).replace(/亿[万千百]/g, '亿').replace(/[零]$/, '').replace(/[@#%^&~]/g, function (m) {
    return { '@': '十', '#': '百', '%': '千', '^': '十', '&': '百', '~': '千' }[m]
  }).replace(/([亿万])([一-九])/g, function (m, d, b, c) {
    c = units.indexOf(d)
    if (c != -1) {
      if (a[j - c] == '0') return d + '零' + b
    }
    return m
  })

}
// 老系统导出
export function oldExcelExport (url, data, method = 'GET') {
  const upperMethod = method.toUpperCase()
  if (upperMethod === 'GET') {
    request
    .get(url, { params: data, responseType: 'blob' })
    .then(res => {
      const blob = new Blob([res.data])
      const name = res.filename ? res.filename : new Date().getTime() + '_' + parseInt(Math.random() * 10000) + '.xlsx'
      saveAs(blob, name)
    })
  }
  if (upperMethod === 'POST') {
    request
    .post(url, data, { responseType: 'blob' })
    .then(res => {
      console.log(res, 'gggg')
      const blob = new Blob([res.data])
      const name = res.filename ? res.filename : new Date().getTime() + '_' + parseInt(Math.random() * 10000) + '.xlsx'
      saveAs(blob, name)
    })
  }
}

/**
 * @desc:图片替换
 * @author:djsong
 * @param:picturePath 图片地址
 * @return:completePicturePath
 */
export const picturePath = (picturePath:string) => {
  if(isNullOrUndefOrEmpty(picturePath))return ''
  
  if (isString(picturePath) && (picturePath as string).indexOf('http') !== -1) {
    return picturePath
  } else {
    return environment.imgBaseUrl + picturePath
  }
}
export const formatDecimals = (num: number | string) => {
  if (isNaN(Number(num))) return '-'
  // 首先保留两位小数
  const numStr = Number(num).toFixed(2)
  // 然后添加千分位分隔符
  return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export const print = function (...params: any[]) {
  Array.from(params).forEach(item => {
    if (Object.prototype.toString.call(item).slice(8, -1) === 'Object') {
      const e = [
        `%c ${item.title} %c ${item.content} `,
        'padding: 1px; border-radius: 3px 0 0 3px; color: #fff; background: #606060;',
        `padding: 1px; border-radius: 0 3px 3px 0; color: #fff; background: ${
          item.backgroundColor ? item.backgroundColor : '#1475b2'
        };`
      ]
      const fun = function (...args: any[]) {
        return (
          window.console &&
          'function' === typeof window.console.log &&
          (item = console).log.apply(e, args)
        )
      }
      // eslint-disable-next-line prefer-spread
      fun.apply(null, e)
    }
  })
}

export const base64ToBlob = (code: string) => {
  const parts = code.split(';base64,')
  const contentType = parts[0].split(':')[1]
  const raw = window.atob(parts[1])
  const rawLength = raw.length
  const uInt8Array = new Uint8Array(rawLength)
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i)
  }
  return new window.Blob([uInt8Array], { type: contentType })
}

// 类型已知，不包含“;base64,”字符
export const base64ToBlob_NotBase64Str = (base64Str: string, type = 'video/mp4') => {
  const raw = window.atob(base64Str)
  const rawLength = raw.length
  const uInt8Array = new Uint8Array(rawLength)
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i)
  }
  return new window.Blob([uInt8Array], { type })
}