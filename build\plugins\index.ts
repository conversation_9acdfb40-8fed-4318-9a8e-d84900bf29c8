import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import type { PluginOption } from 'vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { createHtmlPlugin } from 'vite-plugin-html'

/**
 * @desc: 使用插件
 * */
export function createPlugins(env: ViteEnv) {
  const {VITE_GLOB_APP_TITLE,VITE_SAAS_ICON} = env
  const vitePlugins: PluginOption = [vue(), vueJsx()]
  vitePlugins.push(
    AutoImport({
      resolvers: [ElementPlusResolver()]
    })
  )
  vitePlugins.push(
    Components({
      resolvers: [ElementPlusResolver()]
    })
  )
  vitePlugins.push(
    createHtmlPlugin({
      inject: {
        data: {
          title: VITE_GLOB_APP_TITLE
        },
        tags:[
          {
            injectTo: 'head-prepend',
            tag: 'link',
            attrs: {
              rel: 'icon',
              href: VITE_SAAS_ICON
            },
          },
        ],


      }
    })
  )
  return vitePlugins
}
