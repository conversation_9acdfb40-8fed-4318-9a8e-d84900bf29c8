import { defineStore } from 'pinia'
import type { historyType } from './type'

const noHistoryNames = ['404', '502']
export const useNavHistory = defineStore('navHistory', {
  state: ():{
    history:historyType[],
    activeKey:string
} => {
    return { 
      history: [{
        name: '工作台',
        path: '/',
        closeAble: true
      }],
      activeKey: '/'
    }
  },
  persist: true,
  actions: {
    addHistory (value:historyType) {
      if(noHistoryNames.includes(value.name)) {
        this.setActive(value.path)
        return
      }
      if(this.history.every(item => item.path !== value.path)){
        // 由于path 是全路径包含路由参数 
        // 故判断当前路由是否存证
        // 存在就替换，不存在就push
        const pathIndex = this.history.findIndex(item => item.path.split('?')[0] === value.path.split('?')[0])
        if(pathIndex > -1){
          this.history.splice(pathIndex, 1, value)
        }else{
          this.history.push(value)
        }
        
      }
      this.setActive(value.path)
    },
    setActive (path:string){
      this.activeKey = path
    },
    closeTag (path:string){
      const index = this.history.findIndex(item => item.path === path)
      if(path === this.activeKey){
        if(index !== 0){
          this.setActive(this.history[index - 1].path)
        } else if (this.history.length > 1) {
          this.setActive(this.history[index + 1].path)
        }
      }
      this.history.splice(index, 1)
    },
    closeElse (){
      this.history = this.history.filter(item => item.closeAble || item.path === this.activeKey)
    },
    closeAll (){
      if(this.history.length){
        const firstTab = this.history[0]
        const filterTabs = this.history.filter(item => item.closeAble)
        console.log(filterTabs)
        // 没有工作台就保留第一个标签
        if(filterTabs.length) {
          this.history = filterTabs
        } else {
          this.history = [firstTab]
        }
        const active = this.history[0]
        this.activeKey = active?.path || '/'
      }
    },
    clearHistory (){
      // TODO 做不做持久化
      this.history = []
    }
  }
})
