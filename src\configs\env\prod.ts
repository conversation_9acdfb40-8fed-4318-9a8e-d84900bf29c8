// 线上环境
import type { GlobalEnv } from '../type'

export const prodEnv:GlobalEnv = {
  baseUrl: 'https://c.haoxincd.cn', // 接口请求域名
  apiPrefix: '/rxk-saas/web/youka', // 接口请求前缀,一般对应于后端的服务
  uploadApi: 'https://c.haoxincd.cn/crm-report/file/upload', // 图片上传地址
  videoUplodApi: 'https://c.haoxincd.cn/crm-report/file/upload', // 视频上传地址
  fileDownloadApi: 'https://gw.youxinsign.com/crm/file/download', // 文件下载地址
  websocketPath: 'https://wss.youxinsign.com', // websoket地址
  imgBaseUrl: 'https://file.rongxk.com', // 图片访问路径
  protocolPath: '', // 协议路径
  mapKey: 'de9b8f465f36876c10e3411e75c53062', // 高德地图的key(公司)
  signKey: 'x8eIC5sON7xKRbJeNttFibW5kGulL68Nz0ZXY4XurRBo4AQZB11Z0jIjFcLsIuitLQc29QeKyooorL32wZu5LZm8', // 签名的key
  aes: 'enc-', // 加密解密前缀
  secretKey: '8AEK6247qxYBdBtb', // 解密用的key
  offset: 'QONgVjIhDzDJbN5R',
  isNeedAes: false, // 是否需要加密
  sip_url: '@*************', // sip:1008@************
  sip_socket_url: 'wss://yocwebrtc.youxinsign.com:7443',
  sip_session_timmers_expires: '150', // 会话计时器间隔
  sip_register_expires: '300', // 注册到期时间
  agreementPath: 'https://agreement.youxinsign.com' // 协议的地址,默认都写成线上的看一个地方即可

}
