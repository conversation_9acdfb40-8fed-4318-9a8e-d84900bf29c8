.base-table-page {
  width: 100%;
  height: 100%;
  padding: 0 16px;
  &.reHeight {
    height: calc(100% - 65px);
  }
  .custom-table {
    //border-left: 1px solid #EBEEF5;
    //border-right: 1px solid #EBEEF5;
  }
  .el-table__body {
    //border-left: 1px solid #EBEEF5;
    //border-right: 1px solid #EBEEF5;
    .el-table__cell {
      padding: 0;
      height: 40px;
      line-height: 40px;
      border-right-color: transparent;
      .cell {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #333333;
      }
    }
  }
  .custom-header {
    padding: 0;
    height: 40px;
    line-height: 40px;
    background: #F4F4F5!important;
    //border-left: 1px solid #EBEEF5;
    //&:first-child{
    //  border-left: none;
    //}
    .cell {
      line-height: 40px;
      font-size: 14px;
      color: #666666;
      font-weight: 700;
    }
  }
}



.el-loading-mask {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}


.pagination-box {
  display: flex;
  justify-content: space-between;

  padding: 0 24px;
  .custom-pagination {
    height: 64px;
  }
}
// TODO: 与button按钮loading样式冲突
.is-loading {
  //color: #5687ff;
}
.operate {
  //border-left: 1px solid #EBEEF5;
}