import { AppRouteRecordRaw } from '@/router/types'

export const appManage: AppRouteRecordRaw[] = [
  {
    path: 'appManage',
    name: 'AppManage',
    meta: {
      title: 'APP发版管理',
      icon: 'appguanli'
    },
    children: [
      {
        path: 'versions',
        name: 'versions',
        component: () => import('@/views/appManage/versions/index.vue'),
        meta: {
          title: '版本管理'
        }
      }
    ]
  }
]
