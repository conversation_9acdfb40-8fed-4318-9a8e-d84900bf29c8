import { createApp } from 'vue'
import 'normalize.css/normalize.css'
import 'element-plus/es/components/message/style/css'
import 'element-plus/es/components/message-box/style/css'
import 'element-plus/es/components/image-viewer/style/css'
import App from './App.vue'
import registerGlobalComp from '@/components/registerGlobalComp'
import setupGlobDirectives from '@/directives'
import { setupStore } from '@/stores'
import { setupRouter } from '@/router'
import { setupGlobVxeTable } from '@/plugins'
import './permission'
import '@/assets/styles/index.scss'
import '@/assets/styles/tailwind.css'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
import '@/assets/styles/iconfonts/iconfont'
import 'highlight.js/styles/stackoverflow-light.css'
import 'highlight.js/lib/common'
import hljsVuePlugin from '@highlightjs/vue-plugin'
import { printANSI } from '@/utils/screenLog'
function bootstrap () {
  const app = createApp(App)
  VXETable.config({
    tooltip: {
      zIndex: 9999
    }
  })
  printANSI()
  // 注册全局组件
  app.use(registerGlobalComp)
  setupGlobVxeTable(app)
  // 注册store
  setupStore(app)
  // 注册路由
  setupRouter(app)
  // 注册全局指令
  app.use(setupGlobDirectives)
  app.use(VXETable)
  app.use(hljsVuePlugin)
  app.mount('#app')
  app.config.globalProperties.$app = app
  return app
}
export const app = bootstrap()
