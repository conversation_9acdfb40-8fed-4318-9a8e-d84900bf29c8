import { ref, unref, onUnmounted } from 'vue'
/**
 * 表格批量筛选
 */

export interface Props {
  tableTotal: number;
}
export interface BatchData {
  startLimit: number | string;
  endLimit: number | string;
}

export interface BatchSelectPageInfo {
  tableBatchStartPage: number;
  tableBatchStartIndex: number;
  tableBatchEndPage: number;
  tableBatchEndIndex: number;
}

export interface TableBatchSelectActionType{
  initBatchData: (data: BatchData) => void;
  getBatchSelectIds: (data: Recordable) => void;
  getBatchSelectPageInfo: (data: BatchData, pageSize: number) => BatchSelectPageInfo;
  getBatchSelectIdsList: () => any[];
}

export function useTableBatchSelect (data: BatchData):[(instance: TableBatchSelectActionType) => void, TableBatchSelectActionType] {
  const tableRef = ref<Nullable<TableBatchSelectActionType>>(null)
  function registerTableBatchSelect (instance: TableBatchSelectActionType){
    onUnmounted(() => {
      tableRef.value = null
    })
    if (instance === unref(tableRef)) return
    tableRef.value = instance
    data && instance.initBatchData(data)
  }
  function getTableInstance (): TableBatchSelectActionType {
    const tableBatchSelect = unref(tableRef)
    return tableBatchSelect as TableBatchSelectActionType
  }
  const methods: TableBatchSelectActionType = {
    initBatchData: (data: BatchData) => {
      getTableInstance().initBatchData(data)
    },
    getBatchSelectIds: async (data: Recordable) => {
      return await getTableInstance().getBatchSelectIds(data)
    },
    getBatchSelectPageInfo: (data: BatchData, pageSize: number) => {
      return getTableInstance().getBatchSelectPageInfo(data, pageSize)
    },
    getBatchSelectIdsList: () => {
      return getTableInstance().getBatchSelectIdsList()
    }
  }

  return [registerTableBatchSelect, methods]
  
}