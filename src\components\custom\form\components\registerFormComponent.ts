import Input from './input/index.vue'
import SingleSelect from './singleSelect/index.vue'
import MultipleSelect from './multipleSelect/index.vue'
import Date1 from './date1/index.vue'
import Date2 from './date2/index.vue'
import Date3 from './date3/index.vue'
import Phone from './phone/index.vue'
import InputGai from './inputGai/index.vue'
import Radio from './radio/index.vue'
import Cascade from './cascade/index.vue'
import Checkbox from './checkbox/index.vue'
import UploadImg from './uploadImg/index.vue'
import UploadFile from './uploadFile/index.vue'
import MemberMultipleSelection from './memberMultipleSelection/index.vue'
import MemberSingleSelection from './memberSingleSelect/index.vue'
import MultipleDepartment from './multipleDepartment/index.vue'
import SingleDepartment from './singleDepartment/index.vue'
import Quote from './quote/index.vue'
import Mapping from './mapping/index.vue'
import Tags from './tags/index.vue'
import AssociatedData from './associatedData/index.vue'
import Address from './address/index.vue'
import Money from './money/index.vue'
import Landline from './landline/index.vue'
import type { App } from 'vue'
const components = {
  'w-input': Input,
  'w-singleSelect': SingleSelect,
  'w-multipleSelect': MultipleSelect,
  'w-date1': Date1,
  'w-date2': Date2,
  'w-date3': Date3,
  'w-phone': Phone,
  'w-inputGai': InputGai,
  'w-radio': Radio,
  'w-cascade': Cascade,
  'w-checkbox': Checkbox,
  'w-uploadImg': UploadImg,
  'w-uploadFile': UploadFile,
  'w-personMultiple': MemberMultipleSelection,
  'w-person': MemberSingleSelection,
  'w-multipleDepartment': MultipleDepartment,
  'w-department': SingleDepartment,
  'w-quote': Quote,
  'w-mapping': Mapping,
  'w-tags': Tags,
  'w-associatedData': AssociatedData,
  'w-address': Address,
  'w-money': Money,
  'w-landline': Landline
}

export function registerGlobalFormComp (Vue: App) {
  Object.entries(components).forEach(([name, comp]) => {
    Vue.component(name, comp)
  })
}
// 组件名称的枚举
const obj: Recordable = {}
export const customCompNameEnums = Object.keys(components).reduce((prev, cur) => {
  prev[cur.slice(2)] = cur
  return prev
}, obj)
