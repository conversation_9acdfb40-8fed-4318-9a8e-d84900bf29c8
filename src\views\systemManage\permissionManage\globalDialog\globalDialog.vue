<template>
  <div class="global-dialog">
    <div class="tip">设置后，该角色在所有模块中的数据权限以此处的选择为准</div>
    <ul class="checkbox">
      <el-skeleton v-if="!permissionList.length" :rows="5" animated/>
      <li v-for="item in permissionList" :key="item.code">
        <template v-if="item.name===EDataScope.SELF">
          <el-checkbox :model-value="true"
                       :label="item.value"
                       :disabled="item.name === EDataScope.SELF"
                       @change="handleChangeCheck($event, item)"/>
        </template>
        <template v-else>
          <el-checkbox v-model="item.isChecked"
                       :label="item.value"
                       :disabled="item.name === EDataScope.SELF"
                       @change="handleChangeCheck($event, item)"/>
        </template>

        <template v-if="item.name === EDataScope.DEPARTMENT_CUSTOM">
          <el-cascader
            class="tw-ml-[24px] tw-flex-1"
            v-model="departmentOptions"
            :disabled="!item.isChecked"
            :show-all-levels="false"
            :options="departmentList"
            collapse-tags-tooltip
            collapse-tags
            max-collapse-tags="1"
            :props="{  multiple: true, value: 'id', label: 'name', checkStrictly: true, emitPath: false }"
          />
        </template>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, type PropType, ref, watch } from 'vue'
import { getPermissionListApi } from '@/apis/permissionManage'
import { getDepartmentTree } from '@/apis/user'
import { EDataScope, type Permission } from '@/apis/interface/permissionManage'
import type { User } from '@/apis/interface/userManage'

const props = defineProps({
  data: {
    type: Array as PropType<Permission.ReqRoleMenuReturn['dataScopeList']>,
    default: () => []
  },
  departmentIdList: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  isGlobal: Boolean
})

const permissionList = ref<Permission.ReqPermissionListReturn[]>([])
const departmentList = ref<User.ResDepartment[]>([])
const departmentOptions = ref(props.departmentIdList)

const getDepartment = async () => {
  departmentList.value = await getDepartmentTree()
}

const handleGetPermission = async () => {
  const data = await getPermissionListApi()
  permissionList.value = data.map(item => {
    return {
      ...item,
      isChecked: props.isGlobal ? false : props.data?.map(item => item.code).includes(item.code)
    }
  })
}

const handleChangeCheck = (event: boolean, data: Permission.ReqPermissionListReturn) => {
  if (data.name === EDataScope.ALL) {
    permissionList.value.forEach(item => {
      if(item.name !== EDataScope.DEPARTMENT_CUSTOM){
        item.isChecked = event
      }
      if (item.name === EDataScope.SELF) item.isChecked = true
    })
  }
  if(data.name === EDataScope.DEPARTMENT_CUSTOM && !event){
    departmentOptions.value = []
  }
}

const getData = () => {
  return {
    department: [...new Set(departmentOptions.value.map(item => item).flat(Infinity))],
    dataScopeList: [EDataScope.SELF, ...permissionList.value.filter(item => item.isChecked && item.name !== 'SELF').map(item => item.name)]
  }
}

watch(() => props.departmentIdList, (val) => {
  departmentOptions.value = val
})

onBeforeMount(() => {
  handleGetPermission()
  getDepartment()
})

defineExpose({
  getData
})
</script>

<style scoped lang="scss">
.global-dialog {
  .tip {
    font-size: 12px;
    padding-left: 24px;
    color: #999999;
    background-color: #F4F4F5;
    height: 32px;
    line-height: 32px;
  }

  .checkbox {
    padding: 16px 24px;
    display: flex;
    flex-direction: column;

    li {
      display: flex;
      align-items: center;
    }
  }
}
</style>