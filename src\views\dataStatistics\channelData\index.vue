<template>
  <div class="configure-page">
    <PermissionsTab 
      v-model="activeTab"
      :isPermission="false"
      :list="list"
    />
    <div class="configure-content">
      <component :is="tabs[activeTab]" :key="activeTab" @showDesc="showDesc" />
    </div>
    <FieldDesc v-model:visible="fieldDescVisible" :data="tableColumns" />
  </div>
</template>

<script setup lang="ts">
import { ref, type Component, type Ref } from 'vue'
import { PermissionsTab } from '@/components/business/permissionsTab'
import ChannelStatistics from './channelStatistics/index.vue'
import DateStatistics from './dateStatistics/index.vue'
import FieldDesc from '@/components/business/fieldDesc/index.vue'
import type { ColumnType } from '@/types/table'
const list = [
  { label: '按渠道统计', name: 'ChannelStatistics', permissionCode: 'MarketingChannelDataTab' },
  { label: '按日期统计', name: 'DateStatistics', permissionCode: 'MarketingChannelDataDateTab' }
]
const activeTab = ref('ChannelStatistics')
const tabs:{[key:string]: Component} = {
  ChannelStatistics,
  DateStatistics
}
const tableColumns: Ref<ColumnType[]> = ref([])
const fieldDescVisible = ref(false)

function showDesc (columns: ColumnType[]) {
  tableColumns.value = columns
  fieldDescVisible.value = true
}

</script>

<style lang="scss" scoped>
.configure-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-tabs {
    padding: 0 16px;
    border-bottom: 1px solid #ebeef5;
  }
  :deep() {
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__item {
      color: #666;
    }
    .el-tabs__item.is-active {
      font-weight: bold;
      color: var(--el-color-primary);
    }
    .el-tabs__nav-wrap::after {
      height: 0px;
    }
  }
  .configure-content {
    min-height: 0;
    flex: 1;
    overflow-y: auto;
  }
}
:deep(.channelRoi-img){
  display: flex;
  align-items: center;
  height: 100%;
  .ROIEarlyWarning {
    width: 16px;
    height: 16px;
    .el-image__inner {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
