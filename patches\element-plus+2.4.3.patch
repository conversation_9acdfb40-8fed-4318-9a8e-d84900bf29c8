diff --git a/node_modules/element-plus/es/components/cascader/src/cascader2.mjs b/node_modules/element-plus/es/components/cascader/src/cascader2.mjs
index c05fe02..4150c67 100644
--- a/node_modules/element-plus/es/components/cascader/src/cascader2.mjs
+++ b/node_modules/element-plus/es/components/cascader/src/cascader2.mjs
@@ -325,6 +325,7 @@ const _sfc_main = /* @__PURE__ */ defineComponent({
         !checked && ((_b = cascaderPanelRef.value) == null ? void 0 : _b.handleCheckChange(node, true, false));
         togglePopperVisible(false);
       }
+      searchInputValue.value = '';
     };
     const handleSuggestionKeyDown = (e) => {
       const target = e.target;
diff --git a/node_modules/element-plus/es/components/select-v2/src/defaults.mjs b/node_modules/element-plus/es/components/select-v2/src/defaults.mjs
index b38015b..8a82404 100644
--- a/node_modules/element-plus/es/components/select-v2/src/defaults.mjs
+++ b/node_modules/element-plus/es/components/select-v2/src/defaults.mjs
@@ -68,7 +68,7 @@ const SelectProps = buildProps({
   remoteMethod: Function,
   reserveKeyword: {
     type: Boolean,
-    default: true
+    default: false
   },
   options: {
     type: definePropType(Array),
diff --git a/node_modules/element-plus/es/components/select-v2/src/select-dropdown.mjs b/node_modules/element-plus/es/components/select-v2/src/select-dropdown.mjs
index 0c40ae4..5869d52 100644
--- a/node_modules/element-plus/es/components/select-v2/src/select-dropdown.mjs
+++ b/node_modules/element-plus/es/components/select-v2/src/select-dropdown.mjs
@@ -217,18 +217,23 @@ var ElSelectMenu = defineComponent({
         multiple,
         scrollbarAlwaysOn
       } = select.props;
+
       if (data.length === 0) {
         return createVNode("div", {
           "class": ns.b("dropdown"),
           "style": {
             width: `${width}px`
           }
-        }, [(_a = slots.empty) == null ? void 0 : _a.call(slots)]);
+        }, [(_a = slots.header) == null ? void 0 : _a.call(slots),(_a = slots.empty) == null ? void 0 : _a.call(slots)]);
       }
       const List = unref(isSized) ? FixedSizeList : DynamicSizeList;
       return createVNode("div", {
         "class": [ns.b("dropdown"), ns.is("multiple", multiple)]
-      }, [createVNode(List, mergeProps({
+      }, [
+        createVNode("div", {
+          "class": ns.b("dropdown-check-warp"),
+        }, [(_a = slots.header) == null ? void 0 : _a.call(slots)]),
+        createVNode(List, mergeProps({
         "ref": listRef
       }, unref(listProps), {
         "className": ns.be("dropdown", "list"),
diff --git a/node_modules/element-plus/es/components/select-v2/src/select.mjs b/node_modules/element-plus/es/components/select-v2/src/select.mjs
index f80e5d3..4c211c1 100644
--- a/node_modules/element-plus/es/components/select-v2/src/select.mjs
+++ b/node_modules/element-plus/es/components/select-v2/src/select.mjs
@@ -396,6 +396,9 @@ function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
           default: withCtx((scope) => [
             renderSlot(_ctx.$slots, "default", normalizeProps(guardReactiveProps(scope)))
           ]),
+          header: withCtx(() => [
+            renderSlot(_ctx.$slots, "header", {})
+          ]),
           empty: withCtx(() => [
             renderSlot(_ctx.$slots, "empty", {}, () => [
               createElementVNode("p", {
diff --git a/node_modules/element-plus/es/components/select-v2/src/useSelect.mjs b/node_modules/element-plus/es/components/select-v2/src/useSelect.mjs
index 464141c..7ce0309 100644
--- a/node_modules/element-plus/es/components/select-v2/src/useSelect.mjs
+++ b/node_modules/element-plus/es/components/select-v2/src/useSelect.mjs
@@ -143,7 +143,7 @@ const useSelect = (props, emit) => {
     const size = collapseTagSize.value || "default";
     const paddingLeft = select ? Number.parseInt(getComputedStyle(select).paddingLeft) : 0;
     const paddingRight = select ? Number.parseInt(getComputedStyle(select).paddingRight) : 0;
-    return states.selectWidth - paddingRight - paddingLeft - TAG_BASE_WIDTH[size];
+    return states.selectWidth - paddingRight - paddingLeft - TAG_BASE_WIDTH[size] - 32;
   });
   const calculatePopperSize = () => {
     var _a;
diff --git a/node_modules/element-plus/es/components/select/src/select.mjs b/node_modules/element-plus/es/components/select/src/select.mjs
index 4c8f8b3..9c42edb 100644
--- a/node_modules/element-plus/es/components/select/src/select.mjs
+++ b/node_modules/element-plus/es/components/select/src/select.mjs
@@ -92,7 +92,7 @@ const _sfc_main = defineComponent({
     defaultFirstOption: Boolean,
     reserveKeyword: {
       type: Boolean,
-      default: true
+      default: false
     },
     valueKey: {
       type: String,
@@ -264,12 +264,10 @@ const _sfc_main = defineComponent({
     ]);
     const tagTextStyle = computed(() => {
       const maxWidth = unref(inputWidth) > 123 && unref(selected).length > props.maxCollapseTags ? unref(inputWidth) - 123 : unref(inputWidth) - 75;
-      return { maxWidth: `${maxWidth}px` };
+      return { maxWidth: `${maxWidth - 34}px` };
     });
     const inputStyle = computed(() => ({
-      marginLeft: `${unref(prefixWidth)}px`,
-      flexGrow: 1,
-      width: `${unref(inputLength) / (unref(inputWidth) - 32)}%`,
+      width: `${states.query.length * 14 || 14}px`,
       maxWidth: `${unref(inputWidth) - 42}px`
     }));
     provide(selectKey, reactive({
diff --git a/node_modules/element-plus/es/components/select/src/useSelect.mjs b/node_modules/element-plus/es/components/select/src/useSelect.mjs
index 69fb4e5..5589556 100644
--- a/node_modules/element-plus/es/components/select/src/useSelect.mjs
+++ b/node_modules/element-plus/es/components/select/src/useSelect.mjs
@@ -623,6 +623,7 @@ const useSelect = (props, states, ctx) => {
     }
     states.visible && handleClose();
     states.focused = false;
+    states.query && resetInputHeight()
     ctx.emit("blur", event);
   };
   const handleClearClick = (event) => {
