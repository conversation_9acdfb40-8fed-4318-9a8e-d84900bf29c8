<template>
  <RxkDialog v-model="dialogVisible"
             title="用户离职"
             width="480"
             @close="resetForm(ruleFormRef)">
    <div class="tw-p-[24px]">
      <div class="quit-lable">
        离职移交
        <span>离职用户的数据将移交给指定员工</span>
      </div>
      <el-form
        ref="ruleFormRef"
        :model="formState"
        :rules="rules"
        style="width: 100%"
        label-position="top"
      >
        <el-form-item prop="followId">
          <el-select style="width: 100%"
                     filterable
                     :loading="selectLoading"
                     :filter-method="queryUserList"
                     v-model="formState.followId">
            <el-option v-for="item in userList"
                       :value="item.id"
                       :label="item.realName"
                       :key="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="tw-py-[16px] tw-px-[24px]">
        <el-button @click="resetForm(ruleFormRef)">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submitForm(ruleFormRef)">确定</el-button>
      </div>
    </template>
  </RxkDialog>
</template>
<script setup lang="ts">
import { ref, defineProps, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { getUserList, userResign } from '@/apis/userManage'
import { RxkDialog } from '@/components/common/RxkDialog'

const props = defineProps({
  userId: Number,
  handleClose: {
    type: Function,
    default: null
  }
})

const emits = defineEmits(['handleClose', 'handleSubmit'])

const dialogVisible = ref(true)

const loading = ref(false)
const selectLoading = ref(false)

const ruleFormRef = ref<FormInstance>()
const formState = reactive<{followId: string}>({
  followId: ''
})

const rules = reactive<FormRules<{followId: string}>>({
  followId: [
    { required: true, message: '请选择要移交的员工', trigger: 'blur' }
  ]
})

const userList = ref<any[]>([])

onMounted(() => {
  queryUserList()
})

const queryUserList = async (keyword = undefined as any) => {
  try {
    selectLoading.value = true
    const res = await getUserList({
      pageNum: 1,
      pageSize: 50,
      model: { realName: keyword }
    })
    userList.value = res.records.filter((el:any) => el.id !== props.userId)
  } finally {
    selectLoading.value = false
  }
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        loading.value = true
        let params:any = { ...formState, id: props.userId }
        await userResign(params)
        ElMessage({
          message: '操作成功',
          type: 'success'
        })
        emits('handleSubmit')
        resetForm(ruleFormRef.value)
      } finally {
        loading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  emits('handleClose')
}

</script>
<style lang="scss" scoped>
.quit-lable {
  display: flex;
  padding: 5px 0;
  line-height: 32px;
  align-items: center;
  > span {
    margin-left: auto;
    color: #999999;
    text-align: right;
    font-family: 'Microsoft YaHei';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
}
</style>
