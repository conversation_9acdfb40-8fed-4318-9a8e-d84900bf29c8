<template>
  <component
    :is="compName"
    :renderConfig="renderConfig"
    v-model="modalValue"
    v-bind="$attrs"
    :data="data"
    :positions="positions"
    :quoteMappingConfig="quoteMappingConfig"
    :formData="formData"
    :isSubTable="isSubTable"
    :index="index"
  />
</template>
<script setup lang="ts">
import { computed, PropType } from 'vue'

const props = defineProps({
  compName: {
    type: String,
    default: () => ''
  },
  value: {
    type: [String, Array, Date, Number] as PropType<any>,
    default: ''
  },
  renderConfig: {
    type: Object as PropType<Recordable>,
    default: () => {}
  },
  data: {
    type: Object as PropType<Recordable>,
    default: () => {}
  },
  positions: {
    type: Array as PropType<Recordable[]>,
    default: () => []
  },
  quoteMappingConfig: {
    type: Object as PropType<Recordable>,
    default: () => {}
  },
  isSubTable: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object as PropType<Recordable>,
    default: () => { }
  },
  index: {
    type: Number,
    default: 0
  }

})
const emit = defineEmits(['changeValue'])
const modalValue = computed({
  get () {
    return props.value
  },
  set (newVal) {
    console.log(newVal, 'newVal113')
    emit('changeValue', newVal)
  }
})
</script>