<template>
  <ul class="tabs-ul">
    <li v-for="item in list"
        :key="item.value"
        @click="handleTab(item.value)"
        :class="{'active': current === item.value}">{{item.label}}</li>
  </ul>
</template>
<script lang="ts" setup>
import { computed, type PropType } from 'vue'
import type { List } from '@/types/common'
const props = defineProps({
  list: {
    type: Array as PropType<List[]>,
    default: () => ([])
  },
  tab: {
    type: Number,
    default: 0
  }
})
const current = computed(() => {
  console.log(props.tab, 'props.tab111')
  return props.tab
})
const emit = defineEmits(['change'])
function handleTab (data: number | string) {
  emit('change', data)
}

</script>
<style lang="scss" scoped>
.tabs-ul {
  display: flex;
  background: #F4F4F5;
  padding: 4px 0;
  border-radius: 4px;
  li {
    font-size: 14px;
    color: #666666;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    flex: 1;
    &:first-child {
      margin-left: 4px;
    }
    &:last-child {
      margin-right: 4px;
    }
    &.active {
      color: #5687FF;
      background: #ffffff;
      border-radius: 2px;
    }
  }
}
</style>