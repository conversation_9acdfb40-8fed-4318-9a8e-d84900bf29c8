import type { AppRouteRecordRaw } from '@/router/types'

export const systemManage: AppRouteRecordRaw[] = [
  {
    path: 'systemManage',
    name: 'SystemManage',
    meta: {
      title: '系统管理',
      icon: 'jiton<PERSON><PERSON><PERSON>'
    },
    children: [
      {
        path: 'sysUserManage',
        name: 'SysUserManage',
        component: () => import('@/views/systemManage/userManage/index.vue'),
        meta: {
          title: '用户管理'
        }
      },
      {
        path: 'roleManage',
        name: 'RoleManage',
        component: () => import('@/views/systemManage/roleManage/index.vue'),
        meta: {
          title: '角色管理'
        }
      },
      {
        path: 'permissionManage',
        name: 'PermissionManage',
        component: () => import('@/views/systemManage/permissionManage/index.vue'),
        meta: {
          title: '权限管理'
        }
      },
      {
        path: 'dictionaries',
        name: 'dictionaries',
        meta: {
          title: '字典管理'
        },
        component: () => import('@/views/systemManage/dictionaries/index.vue')
      },
      {
        path: 'systemParam',
        name: 'systemParam',
        meta: {
          title: '系统参数'
        },
        component: () => import('@/views/systemManage/systemParam/index.vue')
      },
      {
        path: 'systemBasicConfig',
        name: 'SystemBasicConfig',
        component: () => import('@/views/systemManage/systemBasicConfig/index.vue'),
        meta: {
          title: '系统基本配置'
        }
      },
      {
        path: 'paramsManage',
        name: 'ParamsManage',
        meta: {
          title: '参数管理',
          icon: 'jigou'
        },
        component: () => import('@/views/systemManage/paramsManage/index.vue'),
        children: [
          {
            path: 'paramsConfig',
            name: 'ParamsConfig',
            component: () => import('@/views/systemManage/paramsManage/paramsConfig/index.vue'),
            meta: {
              title: '系统参数管理'
            }
          },
          {
            path: 'groupsManage',
            name: 'GroupsManage',
            component: () => import('@/views/systemManage/paramsManage/groupsManage/index.vue'),
            meta: {
              title: '系统分组管理'
            }
          }
        ]
      },
      {
        path: 'operationLog',
        name: 'OperationLog',
        component: () => import('@/views/systemManage/operationLog/index.vue'),
        meta: {
          title: '操作日志'
        }
      }
    ]
  }
]
