diff --git a/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js b/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js
index a9cd6bb..2b9c5ee 100644
--- a/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js
+++ b/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js
@@ -2509,7 +2509,7 @@ const KeepAliveImpl = {
     const cache = /* @__PURE__ */ new Map();
     const keys = /* @__PURE__ */ new Set();
     let current = null;
-    if (!!(process.env.NODE_ENV !== "production") || __VUE_PROD_DEVTOOLS__) {
+    if (!!(process.env.NODE_ENV !== "production2") || __VUE_PROD_DEVTOOLS__) {
       instance.__v_cache = cache;
     }
     const parentSuspense = instance.suspense;
