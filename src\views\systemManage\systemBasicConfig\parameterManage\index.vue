<template>
  <div class="apiConfig">
    <SearchFilter @register="registerSetting" @search="search" />
    <div class="btn-box">
      <RxkButton type="primary" @click="openAdd">添加渠道</RxkButton>
    </div>
    <div class="table-box">
      <RxkVTable @register="registerTable" @getTableData="handleGetTableData">
        <template #classNameSlot="{ row }">
          <RxkButton text @click="openApiConfig(row)">编辑</RxkButton>
        </template>
        <template #sourceCodeSlot="{ row }">
          <RxkButton text @click="openMappingModel(row)">编辑</RxkButton>
        </template>
      </RxkVTable>
    </div>

    <!-- 枚举映射弹窗 -->
    <Mapping
      v-if="state.enumMappingModel"
      v-model:visible="state.enumMappingModel"
      :detailData="state.activeRow"
      @close="state.enumMappingModel = false"
      @success="handleEnumMappingSuccess"
    />
    <!-- 新增弹窗 -->
    <AddChannel v-model:visible="addVisible" @refresh="reload" />
    <!-- 配置示例 -->
    <ApiConfig v-model:visible="configVisible" :data="state.activeRow" @refresh="reload"/>
  </div>
</template>
<script lang="tsx" setup>
import { reactive, unref, ref } from 'vue'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { SearchFilter } from '@/components/business/searchFilter'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import {
  getTemplateConfigList
} from '@/apis/systemBasicConfig'
import Mapping from './components/mapping.vue'
import AddChannel from './components/addChannel.vue'
import ApiConfig from './components/apiConfig.vue'
import type { ColumnType } from '@/types/table'
import { dockingTypeEnum, filterTypeEnum } from './data'
import SelectAllV2 from '@/components/business/selectAllV2/index.vue'
const state = reactive({
  apiKeyValue: '',
  enumMappingModel: false,
  activeRow: {}
})
const getEnumLabel = (enumArray: Array<{ label: string, value: number }>, value: number) => {
  const enumItem = enumArray.find(item => item.value === value)
  return enumItem ? enumItem.label : '-'
}
const columns = ref<ColumnType[]>([
  { key: 'apiName', title: '渠道名称' },
  { key: 'type', title: '渠道标识' },
  { key: 'defaultConfig', title: 'API配置示例', slot: 'classNameSlot' },
  { key: 'keyValue', title: 'API字段映射', slot: 'sourceCodeSlot' },
  { key: 'dockingType', title: '对接类型', render: ({ cellData }) => {
    return <>
      <span>{getEnumLabel(dockingTypeEnum, cellData.dockingType)}</span>
    </>
  } },
  { key: 'filterType', title: '加密方式', render: ({ cellData }) => {
    return <>
      <span>{getEnumLabel(filterTypeEnum, cellData.filterType)}</span>
    </>
  } }
])
const searchInfo:Record<string, any> = {
  channelTypes: []
}

const getBasicColumns = () => unref(columns)
const [registerTable, { reload, setSearchInfo }] = useTable({
  api: getTemplateConfigList,
  columns: getBasicColumns(),
  showRowIndex: false,
  useCache: false,
  showPagination: false,
  searchInfo: searchInfo,
  immediate: true // 是否立刻请求
})

const searchFormData = ref([
  {
    fieldName: '渠道名称',
    component: 'Render',
    key: 'channelTypes',
    val: [],
    render: (modelValue:string) => {
      return <SelectAllV2 v-model={modelValue} list={channelListOptions.value} propsObj={ { value: 'type', label: 'apiName' } }></SelectAllV2>
    }
  }
])

function search (val:{ [k: string]: any }) {
  Object.assign(searchInfo, val)
  setSearchInfo(searchInfo)
  reload()
}

// 搜索表单数据
const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})
const channelListOptions = ref([])
function handleGetTableData (value: Recordable) {
  if (value.tableData.length >= channelListOptions.value.length) {
    channelListOptions.value = value.tableData
  }
  state.apiKeyValue = value.tableData[0]?.keyValue ?? ''
}

// 枚举映射
function handleEnumMappingSuccess () {
  state.enumMappingModel = false
  reload()
}
function openMappingModel (row: Recordable) {
  state.activeRow = row
  state.enumMappingModel = true
}

const addVisible = ref(false)
function openAdd () {
  addVisible.value = true
}
const configVisible = ref(false)

// API配置示例
function openApiConfig (row: Recordable) {
  state.activeRow = row
  configVisible.value = true
}
</script>
<style lang="scss" scoped>
.apiConfig {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 16px;
  .table-box {
    flex: 1;
    // height: calc(100% - 74px);
    width: 100%;
    padding: 0 16px;
    overflow-y: hidden;
  }
}
.btn-box {
  display: flex;
  padding: 0 16px;
  margin-bottom: 12px;
}
</style>
