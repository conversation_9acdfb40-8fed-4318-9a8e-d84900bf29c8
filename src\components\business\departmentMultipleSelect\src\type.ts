export interface ShowValueData {
  id?: Recordable;
  name?: string;
}
export interface UserVOList {
  id: string;
  realName?: string;
  username?: string;
  departmentVO: DeComponentTreeVO[];
  jobStatus?: Recordable;
  checked?: boolean;
  [key: string]: any;
}

export interface DeComponentTreeVO {
  id: string;
  name?: string; // 部门名称
  parentId?: string;
  children?: Recordable[];
  userVOList?: UserVOList[];
  indeterminate?: boolean;
  [key: string]: any;
}

export interface RoleVOList {
  userVOList?: UserVOList[];
  id: string;
  name: string;
  checked?: boolean;
  indeterminate?: boolean
}

export interface ComponentData {
  deComponentTreeVO?: DeComponentTreeVO[]; // 组织架构
  userVOList?: UserVOList[]; // 人员集合
  userResignVOList?: UserVOList[]; // 离职人员集合
  roleVOList?: RoleVOList[]; // 角色集合
  showDeptList?: DeComponentTreeVO[]; // 会显的部门集合
}

export interface BasicProps {
  isIncludeChild?: boolean; // 父部门不包含子部门员工
  custom?: boolean; // 是否自定义触发内容
  showValueData?: DeComponentTreeVO[]; // 回显的值
  visible?: boolean;
  paramsData?: any; // 接口需要的参数
  activeName?: string; // panelName
  configuration?: boolean; // 是否需要参数管理配置
  dynamicParameters?: Recordable; // 动态参数
  selectedShow?: boolean; // 选中所有可见选项 
  data?: ComponentData;// 数据
  api?: (...arg: any) => Promise<any>;// 获取数据接口
  placeholder?: string;
  visibleBeforeFn?: () => Promise<any>; // 显示组件之前
  placement?: string;
  collapseTags?: boolean;
  showSet?: boolean; // 是否展示下方复选框操作
  disabled?: boolean;
  menuId?: string; // 菜单权限
  [key: string]: any
}
