<template>
  <SubList v-if="isSubTable" v-bind="$attrs"/>
  <SubField v-else v-bind="$attrs" :formData="formData"/>
</template>
<script lang="ts" setup>
import SubField from './subField.vue'
import SubList from './subList.vue'
import type { PropType } from 'vue'
defineOptions({
  inheritAttrs: false
})
defineProps({
  isSubTable: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object as PropType<Recordable>,
    default: () => { }
  }
})
defineEmits(['refreshDataList'])
</script>