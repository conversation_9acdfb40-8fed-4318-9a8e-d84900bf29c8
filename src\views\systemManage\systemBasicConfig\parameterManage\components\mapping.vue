<template>
  <RxkDrawer 
    v-model:modelValue="modelVisible"
    title="枚举映射"
    size="700"
    :close-on-click-modal="false"
    :footer="false"
    @close="handleClose"
    
  >
    <template #content>
      <div class="main">
        <el-form
          class="form"
          label-position="left"
          :label-width="config.layout.shrink ? 120: 200"
        >
          <div class="header-box">
            <el-form-item label="系统字段">
              <div class="label-item">渠道映射字段
              </div>
            </el-form-item>
          </div>
          <div
            class="filed-box"
            v-for="(item,index) in state.baseCapitalField"
            :key="index"
          >
            <el-form-item  :label="item.fieldName + ':'">
              <div class="lists">
                <el-input
                  placeholder="请输入渠道映射字段"
                  v-model="item.fieldValues"
                  :class="{'item-fields': !config.layout.shrink}"
                  @change="reflectChange($event,item)"
                />
                <el-icon class="icon-item"
                         v-if="item.fieldType == 1"
                         @click="addReflect(item)"
                         color='#5687FF'><CirclePlusFilled /></el-icon>
              </div>
            </el-form-item>
            <div
              v-if="item.fieldType == 1"
              style="margin-bottom: 18px"
            >
              <div
                v-for="(i,idx) in item.values"
                :key="idx"
              >
                <template v-if="i.children && i.children.length > 0">
                  <div
                    v-for="(j,jdx) in i.children"
                    :key="jdx"
                    class="lists-item"
                  >
                    <div class="lists-item-block">
                      <div>{{i.valueDesc}}</div>
                      <div class="lists-item-item">=</div>
                      <div class="lists-item-item">{{j.value}}</div>
                    </div>
                    <div class="lists-item-icon">
                      <el-icon class="icon-item" @click="editReflec(item,j)"><Edit /></el-icon>
                      <el-icon class="icon-item" @click="deleteFieldReflect(j?.id)"><Delete /></el-icon>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </template>
  </RxkDrawer>
  <RxkDialog
    v-model:modelValue="state.visible"
    :title="state.title"
    width="480px"
    :close-on-click-modal="false"
    :isLoading="true"
    @sure="handleReflectSure"
  >
    <el-form
      class="config-form"
      :model="state.addForm"
      ref="addForm"
      :rules="state.addRules"
      label-position="left"
    >
      <el-form-item
        label="系统字段枚举值"
        prop="bcfId"
      >
        <el-select
          v-model="state.addForm.bcfId"
          placeholder="请选择系统字段枚举值"
          :disabled="state.isEdit"
          style="width: 100%;"
        >
          <el-option
            v-for="(item,index) in state.sourceList"
            :key="index"
            :label="item.valueDesc"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="渠道字段枚举值"
        prop="value"
      >
        <el-input
          placeholder="请输入渠道字段枚举值"
          v-model="state.addForm.value"
          maxlength="20"
        />
      </el-form-item>
    </el-form>
  </RxkDialog>

</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { getBaseConfigFieldApi, getFieldReflectByConfigId, updateFieldApi, deleteFieldApi } from '@/apis/systemBasicConfig'
import { RxkDrawer } from '@/components/common/RxkDrawer'
import { RxkDialog } from '@/components/common/RxkDialog/index'
import { Edit, Delete, CirclePlusFilled } from '@element-plus/icons-vue'
import { useConfig } from '@/stores/modules/config'
const props = defineProps({
  visible: Boolean,
  detailData: {
    type: Object,
    default: () => {}
  }
})
const config = useConfig()
const emit = defineEmits(['update:modelValue', 'close'])
const modelVisible = computed({
  get (){
    return props.visible
  },
  set (val){
    emit('update:modelValue', val)
  }
})
const state = reactive<Recordable>({
  visible: false,
  encryptTypeList: [],
  baseCapitalField: [],
  isEdit: false,
  addForm: {},
  sourceList: [],
  addRules: {
    value: [{
      required: true, message: '请输入渠道枚举值', trigger: 'blur'
    }],
    bcfId: [{
      required: true, message: '请选择标识符', trigger: 'blur'
    }]
  },
  channelReflectConfig: [],
  reflects: [],
  baseData: [],
  title: '添加枚举映射',
  error: false,
  baseCapitalFieldData: [],
  useTemplateState: false
})

const updateData = async () => {
  await getBaseCapitalField()
}

const getChannelReflectConfig = async (arr) => {
  const res = await getFieldReflectByConfigId(props.detailData.id)
  if (res) {
    state.channelReflectConfig = res
    arr.forEach((i) => {
      res.forEach((j) => {
        i.values.forEach((item) => {
          if (j.bcfId === String(item.id)) {
            j.bcfId = Number(j.bcfId)
            item.children.push(j)
            i.fieldValues = j.field
          }
        })
      })
    })
  }
  state.baseCapitalField = arr
  state.baseData = JSON.parse(JSON.stringify(arr))
}

const getBaseCapitalField = async () => {
  const res = await getBaseConfigFieldApi({})
  res.map((i) => {
    i.fieldValues = ''
    i.values.map((j) => {
      j.children = []
    })
  })
  state.baseCapitalFieldData = JSON.parse(JSON.stringify(res))
  getChannelReflectConfig(res)
}

const addReflect = (item) => {
  state.sourceList = []
  state.sourceList = item.values
  state.addForm = {}
  state.addForm.apiConfigId = props.detailData.id
  state.addForm.field = item.fieldValues ? item.fieldValues : item.values[0].field
  state.addForm.fieldValues = item.fieldValues ? item.fieldValues : ''
  state.title = '添加枚举映射'
  state.isEdit = false
  state.visible = true
}

const editReflec = (sourceList, item) => {
  state.sourceList = []
  state.sourceList = sourceList.values
  state.addForm = item
  state.addForm.apiConfigId = props.detailData.id
  state.title = '编辑枚举映射'
  state.isEdit = true
  state.visible = true
}

const deleteFieldReflect = (id:string) => {
  ElMessageBox.confirm('此操作将永久删除, 是否继续?', '提示', {
    type: 'warning'
  }).then(() => {
    visibleSure(id)
  })
}
const reflectChange = (value:string, item:any) => {
  if (value === '') {
    ElMessage.error('请输入渠道映射字段')
    state.baseCapitalField = JSON.parse(JSON.stringify(state.baseData))
    return false
  }
  if (isReapt(value)) {
    ElMessage.error('有重复字段名')
    state.baseCapitalField = JSON.parse(JSON.stringify(state.baseData))
    return false
  }
  state.reflects = []
  state.addForm = {}
  if (item.fieldType === 0) {
    state.reflects.push({
      apiConfigId: props.detailData.id,
      field: value,
      bcfId: item.values[0].children.length > 0 ? item.values[0].children[0].bcfId : item.values[0].id,
      value: '',
      id: item.values[0].children.length > 0 ? item.values[0].children[0].id : ''
    })
  } else {
    item.values.forEach(i => {
      if (i.children.length > 0) {
        i.children.forEach(item => {
          state.reflects.push({
            apiConfigId: props.detailData.id,
            field: value,
            bcfId: item.bcfId,
            value: item.value,
            id: item.id
          })
        })
      }
    })
    if (state.reflects.length === 0) {
      ElMessage.error(item.fieldName + '没有添加枚举映射值')
      return false
    }
  }

  insertOrUpdateReflect()
}

const visibleSure = async (id) => {
  await deleteFieldApi(id)
  ElMessage.success('操作成功')
  updateData()
}

const isReapt = (value:string) => {
  // 判断是否有重复
  let error = false
  state.baseData.forEach(i => {
    if (i.fieldValues === value) {
      error = true
      return false
    }
  })
  return error
}

const addForm = ref()
const handleReflectSure = (fn:Fn) => {
  addForm.value.validate((valid:boolean) => {
    if (valid) {
      state.reflects = []
      state.reflects[0] = state.addForm
      insertOrUpdateReflect()
      fn()
    }else {
      fn()
    }
  })
}

const insertOrUpdateReflect = () => {
  updateFieldApi({ templateList: state.reflects })
  .then(() => {
    ElMessage.success('操作成功')
    updateData()
    state.visible = false
    state.isEdit = false
    state.useTemplateState = false
    state.addForm = {}
  })
  .catch(() => {
    state.baseCapitalField = JSON.parse(JSON.stringify(state.baseData))
  })
}
function handleClose () {
  emit('close')
}
onMounted(() => {
  getBaseCapitalField()
})
</script>

<style scoped lang="scss">
  .main {
    padding:24px 16px 16px 16px;

    .edit-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    color: #5687FF;
    z-index: 9;
  }
  .header-box {
    color: #666666;
    font-family: "Microsoft YaHei";
    font-size: 14px;
    font-weight: 700;
    line-height: 22px;
    height: 48px;
    display: flex;
    align-items: center;
    background-color: #EBEEF5;
    padding-left: 12px;

    :deep(.el-form-item){
      margin-bottom:0px;
    }
  }
  .filed-box {
    margin-top:16px;
  }
  .label-item {
    display: flex;
    align-items: center;
  }
  .lists {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    &-item{
      padding: 8px 0;
      width: 584px;
      background: #F7F9FC;
      display: flex;
      align-items: center;
      padding-left: 220px;
      font-size: 13px;
      color: #666;
      &-block{
        display: flex;
        align-items: center;
        flex: 1;
        line-height: 20px;
        &:last-child{
          margin-left: 20px;
        }
      }
      &-item{
        margin-left: 20px;
      }
      &-icon{
        margin-right: 16px;
        width: 14px;
        height: 14px;
        cursor: pointer;
      }
    }
    .item-fields{
      width:366px;margin-right: 28px
    }
  }
  }

.config-form {
  padding: 24px;

  .form-input {
    width: 100px;
    margin: 0 4px
  }
}
.content{
    padding: 20px;
    .top {
      display: flex;
    }
}

.icon-item {
  cursor: pointer;
  margin-left: 12px;
}
.lists-item-icon {
  display: flex;
  flex: 1;
  justify-content: flex-end;
}
.layout-shrink {
  .lists {
    flex:1;
    gap:4px;
  }
  .filed-box {
    .lists-item {
      width: 100%;
    }
  }
}
</style>
