import { validateInterestRate } from '@/utils/regExp'
import { isNullOrUndefOrEmpty } from '@/utils/is'
/**
 * @author：张胜
 * @desc: 0~2，且保留2位有效小数
 * */
export function AppValidateInterestRate (rule: any, value: any, callback: any) {
  console.log(value, 'value')
  if (isNullOrUndefOrEmpty(value)) {
    callback(new Error('请输入'))
  } else if (!validateInterestRate(value)) {
    callback(new Error('最高不超过2'))
  } else {
    callback()
  }
}

/**
 * 通用表单验证规则
 * */

/**
 * @author：张胜
 * @desc：验证手机号
 * */
export function validatePhone (rule: any, value: any, callback: any) {
  const reg = /^1\d{10}$/
  if (!value) {
    callback(new Error('请输入手机号'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入11位的手机号'))
  } else {
    callback()
  }
}
/**
 * @author：张胜
 * @desc：验证验证码
 * */
export function validateCode (rule: any, value: any, callback: any) {
  const reg = /^[0-9]\d*|0$/
  if (!value) {
    callback(new Error('请输入验证码'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入正确的验证码'))
  } else {
    callback()
  }
}

/**
 * @author：张胜
 * @desc：验证身份证号
 * */
export function validateIdCard (rule: any, value: any, callback: any) {
  const reg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/
  if (!value) {
    callback(new Error('请输入身份证号码'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入正确的身份证号码'))
  } else {
    callback()
  }
}

/**
 * @author：张胜
 * @desc：验证银行卡
 * */
export function validteBankCard (rule: any, value: any, callback: any) {
  const reg = /^([1-9]{1})(\d{13,18})$/
  if (!value) {
    callback(new Error('请输入银行卡号码'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入正确的银行卡号码'))
  } else {
    callback()
  }
}
/**
 * @author：张胜
 * @desc：验证登录账号
 * */
export function validateAccount (rule: any, value: any, callback: any) {
  const reg = /^.{6,18}$/
  if (!value) {
    callback(new Error('请输入账号'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入6~18位字符'))
  } else {
    callback()
  }
}
/**
 * @author：张胜
 * @desc：验证登录密码
 * */
export function validatePass (rule: any, value: any, callback: any) {
  const reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{6,18}$/
  if (!value) {
    callback(new Error('请输入账号的密码'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入6~18位字母,数字,特殊字符'))
  } else {
    callback()
  }
}
export const commonReg = (reg: RegExp, msg: string) => {
  return (rule: any, value: any, callback: Fn) => {
    if (!(reg.test(value))) {
      callback(new Error(msg))
    } else {
      callback()
    }
  }
}
export function regular (type: string, mes?: string) {
  switch (type) {
    case 'phone': // 手机号码
      return commonReg(/^1\d{10}$/, mes || '请输入正确的电话号码')
    case 'tuominPhone': // 脱敏手机号码
      return commonReg(/^1\d{2}[\d|\*]{4}\d{4}$/, '请输入正确的电话号码')
    case 'phoneNumber': // 手机号码，座机号
      return commonReg(/^(1[3|4|5|6|7|8|9])\d{9}$|^0\d{2,3}-?\d{7,8}$/, '请输入正确的号码')
    case 'ID': // 身份证
      return commonReg(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, '请输入正确的身份证号')
    case 'pwd': // 密码以字母开头，长度在6~16之间，只能包含字母、数字和下划线
      return commonReg(/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/, '请输入正确的密码')
    case 'email': // 邮箱
      return commonReg(/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/, '请输入正确的邮箱')
    case 'URL': // 网址
      return commonReg(/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/, '请输入正确的网址')
    case 'eightNumber':
      return commonReg(/^[0-9]{1,8}$/, '输入最多8位的整数')
    case 'IP': // IP
      return commonReg(/((?:(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d))/, '请输入正确的IP')
    case 'number': // 数字
      return commonReg(/^(\d)*$/, '请输入正确的数字')
    case 'fullName': // 姓名
      return commonReg(/^([a-zA-Z0-9\u4e00-\u9fa5\·]{1,10})$/, '请输入正确的姓名')
    case 'percentage':
      return commonReg(/^(100|[1-9]?\d(\.\d\d?)?)%$/, '请输入百分数如10%')
    case 'floatingNum':
      return commonReg(/^\d+(\.\d{1,2})?$/, '输入最多2位小数且是数字')
    case 'floatingPoint':
      return commonReg(/0\.[0-9]+/, '请输入大于0小于1的小数')
    case 'mechanismPwd':
      return commonReg(/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{6,18}$/, '请输入6~18位需要包含字母,数字,特殊字符')
    case 'mechanismUsn': // 账号以字母开头，长度在6~18之间，只能包含字母、数字和下划线
      return commonReg(/^.{6,18}$/, '请输入6~18位字符')
    case 'mechanismFullName': // 姓名
      return commonReg(/^([\u4E00-\u9FA5\·]{2,8})$/, '请输入正确的姓名')
    case 'couponIds': // 优惠券Id,用","隔开,只能输入数字
      return commonReg(/^[0-9][0-9,]*$/, '只能输入数字,多个ID用","隔开')
    case 'vipDisCount': // vip的会员折扣价,打几折(保留两位小数)
      return commonReg(/^([0-9]\.\d{0,1}|[0-9]?)$/, '请输入0-10之间的数字,保留一位小数')
    case 'vipRepaidGift': // vip会员的充值赠送金币%,百分比
      return commonReg(/^([1-9]{1,2}|[1-9]\d?)$/, '请输入0-100之间的整数')
    case 'coinCount': // 金币扣除
      return commonReg(/^[1-9]\d{0,6}$/, '请输入大于等于1小于等于9999999的正整数')
    case 'numberCheck': // 号码校验数
      return commonReg(/^[1-9]\d{0,7}$/, '请输入大于等于1小于等于99999999的正整数')
    case 'threeNumber':
      return commonReg(/^[1-9]\d{0,2}$/, mes || '请输入大于等于1小于等于999的正整数')
    case 'nineNumber':
      return commonReg(/^[1-9][0-9]{0,8}$/, '输入最多9位的正整数')
    case 'floatingEightNum':
      return commonReg(/^\d{1,8}(\.\d{1,2})?$/, '输入最多2位小数且小于等于99999999.99')
    case 'numOver0':
      return commonReg(/^([0](\.\d{1,4}))$|^([1-9][0-9]*(\.\d{1,4})?)$/, '大于0的数字（最多四位小数）')
    default:
      console.error('No times regular')
      return true
  }
}