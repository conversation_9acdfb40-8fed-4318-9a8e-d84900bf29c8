<template>
	<!-- 分页组件 -->
	<el-pagination :current-page="pageable.pageNum" :page-size="pageable.pageSize" :page-sizes="[10, 20, 50, 100]"
		layout="total, sizes, prev, pager, next, jumper" :total="pageable.total || 0" @size-change="handleSizeChange"
		@current-change="handleCurrentChange"></el-pagination>
</template>

<script lang="ts" name="Pagination">
interface Pageable {
	pageNum: number;
	pageSize: number;
	total: number;
}

interface PaginationProps {
	pageable: Pageable;
	handleSizeChange: (size: number) => void;
	handleCurrentChange: (currentPage: number) => void;
}
</script>
<script setup lang="ts">
defineProps<PaginationProps>();
</script>
