<template>
  <div class="admin-page">
    <div class="tw-flex tw-justify-between tw-items-center tw-flex-wrap tw-gap-2">
      <div class="tw-flex tw-items-center tw-flex-wrap  tw-gap-2">
        <span>登录Admin系统连续失败次数达到</span>
        <el-form :model="ruleForm" :inline="true" ref="ruleFormRef">
          <el-form-item prop="failures" 
                        :rules="[{
                          required: true,
                          message: '请输入次数',
                          trigger: 'blur',
                        }]">
            <el-input
              v-model="ruleForm.failures"
              v-input-number="{ min: 1, digit: 0 }"
              placeholder="请输入"
            >
              <template #suffix>次</template>
            </el-input>
          </el-form-item>
          <span>后， 自动锁定</span>
          <el-form-item prop="lockMinutes">
            <el-input
              v-model="ruleForm.lockMinutes"
              v-input-number="{ min: 1, digit: 0 }"
              placeholder="请输入"
            >
              <template #suffix>分钟</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <el-button type="primary" @click="handleSave" :loading="loading" >保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

import {
  getLoginTimeoutConfig,
  saveLoginTimeoutConfig
} from '@/apis/systemBasicConfig'
import type { FormInstance } from 'element-plus'
interface RuleForm {
  failures: number | string
  lockMinutes: number | string
}
const loading = ref(false)
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<RuleForm>({
  failures: '',
  lockMinutes: ''
})
getLoginTimeoutConfig().then((res) => {
  ruleForm.value = res || {
    failures: 5,
    lockMinutes: 30
  }
})

async function handleSave () {
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      loading.value = true
      saveLoginTimeoutConfig(ruleForm.value).then(() => {
        ElMessage.success('保存成功')
      }).finally(() => loading.value = false)
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style lang="scss" scoped>
.admin-page {
  padding: 20px;
  font-size: 14px;
  color: #333;
  .el-input {
    width: 100px;
    margin: 0 10px;
  }
}
:deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
  .el-form-item__error {
    padding-left: 8px;
  }
}
</style>
