import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'

const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

/* -------------------------------- 渠道管理 -------------------------------- */
// 渠道列表
export const fetchGetCarChannelList = (data?: any) => request.post('/admin/car-channel/selectList', data, jsonHeaderConfig)
// 渠道统计列表-导出
export const fetchChannelStatisticsListExport = '/admin/dataStatistics/export'

// 渠道修改或新增
export const fetchAddOrUpdateCarChannelList = (data?: any) => request.post('/admin/car-channel/addOrUpdate', data, jsonHeaderConfig)

// 渠道详情
export const fetchChannelViewDetail = (data?: any) => request.post('/admin/car-channel/viewDetail', data)

// 渠道商链接
export const fetchGetChannelLink = (data?: any) => request.post('/admin/car-channel/channelLink', data)

// 渠道链接统计列表
export const fetchGetChannelStatisticsList = (data?: any) => request.post('/admin/dataStatistics/geChannelDistributedData', data, jsonHeaderConfig)

// 渠道链接统计列表-导出
export const fetchGetChannelLinkStatisticsListExport = '/admin/dataStatistics/geChannelDistributedDataExport'

// 校验渠道提取码
export const fetchCheckExtractionCode = (data?: any) => request.post('/admin/car-channel/checkExtractionCode', data, jsonHeaderConfig)

/* -------------------------------- 数据统计 -------------------------------- */

// 概览查询：isChannelGroup = false & isDayGroup = true
// 渠道数据查询：isChannelGroup = true & isDayGroup = false
// 渠道数据单日明细查询：isChannelGroup = true & isDayGroup = true & channelId = xxxx
// 渠道数据按日期查询：isChannelGroup = true & isDayGroup = true

// 数据概览-列表
export const fetchGetDataOverviewList = (data?: any) => {
  return request.post('/admin/dataStatistics/dataStatistics', data, jsonHeaderConfig)
}

// 按渠道统计-列表
export const fetchGetChannelStatisList = (data?: any) => {
  return request.post('/admin/dataStatistics/dataStatistics', data, jsonHeaderConfig)
}

// 按渠道统计-单日明细-列表
export const fetchGetChannelStatisDayDetailList = (data?: any) => {
  return request.post('/admin/dataStatistics/dataStatistics', data, jsonHeaderConfig)
}

// 按日期统计-列表
export const fetchGetDateStatisList = (data?: any) => {
  return request.post('/admin/dataStatistics/dataStatistics', data, jsonHeaderConfig)
}

// 第三方数据统计-按日期

export const fetchGetuserProductData = (data?: any) => {
  return request.post('/admin/userProductData/pageQuery', data, jsonHeaderConfig)
}
// 第三方数据统计-推送明细

export const fetchGetPushData = (data?: any) => {
  return request.post('/admin/userProductData/pushDetailPageQuery', data, jsonHeaderConfig)
}
// 用户表-列表
export const fetchGetUserList = (data?: any) => request.post('/admin/carCustomerUser/selectUserList', data, jsonHeaderConfig)
// 用户详情
export const fetchGetUserInfo = (data?: any) => request.post('/admin/carCustomerUser/selectUserInfo', data)
// 用户行为
export const fetchGetUserAction = (data?: any) => request.post('/admin/carCustomerUser/userBehavior', data)

// 数据统计-汇总
export const fetchGetDataStatisticsTotal = (data?: any) => {

  return request.post('/admin/dataStatistics/dataStatisticsTotal', data, jsonHeaderConfig)
}
