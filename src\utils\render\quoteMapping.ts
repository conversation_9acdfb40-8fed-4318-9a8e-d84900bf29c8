import EventEmitter from '@/utils/event'
const subTableEventEmitter = new EventEmitter()

// 处理引用和映射的关系，存储之类的操作
export class QuoteMapping {
  public quoteFieldList: Recordable[] // 所有引用字段
  public mappingFieldList: Recordable[] // 所有映射字段

  constructor () {
    this.quoteFieldList = []
    this.mappingFieldList = []
  }
  // 设置引用字段配置
  setQuoteFieldList (field: Recordable) {
    const index = this.quoteFieldList.findIndex(i => i.id === field.id)
    index > -1 ? this.quoteFieldList.splice(index, 1, field) : this.quoteFieldList.push(field)
  }

  // 设置映射字段配置
  setMappingFieldList (field: Recordable) {
    const index = this.mappingFieldList.findIndex(i => i.id === field.id)
    index > -1 ? this.mappingFieldList.splice(index, 1, field) : this.mappingFieldList.push(field)
  }
}

// 引用字段类
export class Quote {
  public subTableEventEmitter: EventEmitter// 发布订阅
  public field: Recordable // 当前引用字段
  public tableGroupType: number
  constructor (field: Recordable, options?: Recordable) {
    console.log(options, 'options222')
    this.subTableEventEmitter = subTableEventEmitter
    this.field = field
    this.tableGroupType = options?.tableColumnType // 分组类型,代表是分栏还是子表
  }
}

// 映射字段类
export class Mapping {
  public subTableEventEmitter: EventEmitter// 发布订阅
  public field: Recordable // 当前映射字段
  public tableGroupType: number
  constructor (field: Recordable, options?: Recordable) {
    this.subTableEventEmitter = subTableEventEmitter
    this.field = field
    this.tableGroupType = options?.tableColumnType // 分组类型,代表是分栏还是子表
  }
}