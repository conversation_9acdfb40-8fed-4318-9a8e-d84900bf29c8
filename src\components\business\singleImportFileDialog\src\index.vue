<template>
  <RxkDialog
    v-model="dialogVisible"
    :title="title"
    width="480px"
    :show-close="!isUploading"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="upload-container">
      <!-- 标题区 -->
      <div class="header">
        <span>选择文件</span>
        <slot name="btn" />
      </div>
      <!-- 上传区域 -->
      <el-upload
        ref="uploadRef"
        class="upload-area"
        :on-change="handleChange"
        :auto-upload="false"
        :limit="1"
        :show-file-list="false"
        v-bind="$attrs"
        v-show="!selectedFile"
      >
        <div class="upload-trigger">
          <el-icon><Plus /></el-icon>
          <span>点击上传</span>
        </div>
      </el-upload>

      <!-- 已选文件展示 -->
      <div v-if="selectedFile" class="file-info">
        <div class="file-content">
          <i class="excel-icon"/>
          <div class="tw-flex tw-flex-1 tw-flex-col tw-gap-[8px]">
            <div class="tw-flex tw-flex-row">
              <span class="file-name">{{ selectedFile.name }}</span>
              <span class="close-icon icon iconfont icon-guanbi"  @click="removeFile"/>
            </div>
            <!-- 上传进度条 -->
            <div v-if="isUploading" class="progress-bar">
              <el-progress 
                :percentage="uploadProgress" 
                :stroke-width="6"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 提示文本 -->
      <div class="tip-text">{{ tipText }}</div>

      <!-- 模板下载 -->
      <div class="template-download" v-if="downUrl">
        <span class="icon iconfont icon-Frame-6 tw-mr-[4px]"/>
        <span @click="downloadTemplate" class="tw-cursor-pointer tw-underline">点此下载Excel模版</span>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog" :disabled="isUploading">取消</el-button>
        <el-button 
          type="primary" 
          @click="startUpload" 
          :loading="isUploading"
          :disabled="!selectedFile"
        >
          开始导入
        </el-button>
      </div>
    </template>
  </RxkDialog>
</template>

<script setup lang="tsx">
import { ref, computed, unref } from 'vue'
import { RxkDialog } from '@/components/common/RxkDialog'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { UploadFile, UploadInstance } from 'element-plus'
import { excelExport } from '@/utils/index'
import request from '@/utils/axios'

const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  // 弹窗标题
  title: {
    type: String,
    default: '批量导入'
  },
  // 上传接口地址
  uploadUrl: {
    type: String
  },
  // 是否直接上传
  autoUpload: {
    type: Boolean,
    default: true
  },
  // 模板下载地址
  downUrl: {
    type: String,
    default: ''
  },
  // 失败数据下载地址
  failDownUrl: {
    type: String,
    default: ''
  },
  downMethods: {
    type: String,
    default: 'GET'
  },
  // 提示文本
  tipText: {
    type: String,
    default: '一次只能上传一个表格文件，单次最多支持导入1000条数据'
  },
  // 文件大小限制(MB)
  maxSize: {
    type: Number,
    default: 2
  },
  // 是否显示操作记录
  record: {
    type: Boolean,
    default: false
  },
  // 其它额外业务参数
  extraParams: {
    type: Object,
    default: () => ({})
  }
})

const emits = defineEmits(['update:visible', 'success', 'error', 'import', 'downErrorData', 'finish'])
// 上传实例
const uploadRef = ref<UploadInstance>()
// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emits('update:visible', val)
})

// 上传相关状态
let timer:NodeJS.Timeout | null = null
const isUploading = ref(false)
const loading = ref(false)
const uploadProgress = ref(0)
const selectedFile = ref<UploadFile | null>(null)
const uploadResult = ref()

// 文件选择变化
const handleChange = (file: UploadFile) => {
  // 验证文件类型
  const isExcel = /\.(xls|xlsx)$/.test(file.name)
  if(!isExcel) {
    ElMessage.error('请上传Excel文件')
    removeFile() // 避免后续的上传无效问题
    return
  }
  const isLtSize = file.size! / 1024 / 1024 < props.maxSize
  if (!isLtSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB!`)
    removeFile()
    return
  }
  selectedFile.value = file
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  uploadRef.value?.clearFiles()
}

// 开始上传
const startUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  try {
    isUploading.value = true
    loading.value = true
    uploadProgress.value = 0
    timer && clearInterval(timer)
    // 模拟上传进度
    timer = setInterval(() => {
      if (uploadProgress.value < 99) {
        uploadProgress.value += Math.min(10, 99 - uploadProgress.value)
      } else {
        timer && clearInterval(timer)
      }
    }, 300)
    // 非自动上传抛出
    if (!props.autoUpload) {
      emits('import', selectedFile.value.raw, () => {
        timer && clearInterval(timer)
        uploadProgress.value = 100
        isUploading.value = false
      })
      return
    }
    // 调用上传接口
    if (!props.uploadUrl) {
      ElMessage.error('请配置上传接口地址')
      return
    }
    const result = await request.upload({ 
      url: props.uploadUrl, 
      data: { file: selectedFile.value.raw!, ...props.extraParams } 
    })
    uploadResult.value = result || {}
    clearInterval(timer)
    uploadProgress.value = 100

    if(unref(uploadResult).totalCount === unref(uploadResult).successNumber){
      showResultNotification('全部数据已导入成功。', 0)
      emits('success', unref(uploadResult))
    }else{
      showResultNotification('部分数据导入失败，请点击下载导入失败数据。', 1)
      emits('error', unref(uploadResult))
    }

    emits('finish', unref(uploadResult))
    closeDialog()
  } catch (error: any) { /* empty */ } finally {
    if (props.autoUpload) isUploading.value = false
    uploadProgress.value = 0
  }
}

// 下载模板
const downloadTemplate = () => {
  if (props?.downUrl) {
    excelExport(props.downUrl, {}, props?.downMethods)
  }
}

// 下载失败数据
const downErrorHandler = () => {
  if (props?.failDownUrl && uploadResult.value?.id) {
    excelExport(props.failDownUrl + `/${uploadResult.value.id}`, {}, props?.downMethods)
  }else{
    emits('downErrorData', uploadResult.value)
  }

  ElMessage.success('下载中...')
  closeNotifi()
}

// 消息通知实例
const notifiInstance = ref()
// 关闭当前实例消息通知
const closeNotifi = () => notifiInstance.value?.close()
// 显示导入通知
const showResultNotification = (message: string, type:number) => {
  // 新消息弹出前关闭上一条
  closeNotifi()

  notifiInstance.value = ElNotification({
    title: '导入结果',
    customClass: 'import-notification',
    position: 'bottom-right',
    duration: 0,
    dangerouslyUseHTMLString: true,
    message: (
      <div>
        <div class="tw-px-[16px] tw-py-[20px]">{message}</div>
        <div class="import-notification__footer">
          {type === 0 ? (
            <el-button type="text" onClick={closeNotifi}>知道了</el-button>
          ) : (
            <el-button type="text" onClick={downErrorHandler}>
              下载导入失败数据
            </el-button>
          )}
        </div>
      </div>
    )
  })
}
// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
  removeFile()
  isUploading.value = false
  uploadProgress.value = 0
}
</script>

<style lang="scss" scoped>
.upload-container {
  padding: 24px;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 22px;
  }

  .upload-area {
    width: 100%;
    
    >:deep(.el-upload ){
      width: 100%;
    }
    .upload-trigger {
      width: 100%;
      padding: 7px 16px;
      border: 1px dashed #AAC3FF;
      border-radius: 4px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      gap: 4px;
      
      color: #409EFF;
      &:hover {
        border-color: #409EFF;
      }
      
      .el-icon {
        font-size: 16px;
      }
    }
  }

  .file-info {
    margin-bottom: 16px;
    padding: 12px 14px;
    background: #F5F7FA;
    .file-content {
      display: flex;
      align-items: center;
      flex-direction: row;
      .excel-icon {
        display: inline-block;
        width: 40px;
        height: 40px;
        background: url('@/assets/images/icon/excel.png') no-repeat center/contain;
        margin-right: 8px;
        font-size: 20px;
        color: #67C23A;
      }
      
      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .close-icon {
        margin-left: 8px;
        cursor: pointer;
        color: #F44D4D;
      }
    }
  }

  .tip-text {
    margin:16px 0 24px;
    color: #999999;
    font-size: 12px;
  }

  .template-download {
    display: flex;
    align-items: center;
    color: #409EFF;
    font-size: 14px;
    .el-icon {
      margin-right: 4px;
    }
  }
}
.progress-bar {
  :deep(.el-progress__text){
    min-width: auto;
    margin-left: 8px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #EBEEF5;
}
</style>