<template>
  <div class="isTxt" v-if="isTxt">
    {{innerValue || '-'}}
  </div>
  <RxkInput
    v-else
    style="width: 100%;"
    v-model="innerValue"
    :type="renderConfig.inputType"
    :disabled="renderConfig.disabled"
    :placeholder="renderConfig.placeholder"
  />
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkInput } from '@/components/common/RxkInput'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})

</script>