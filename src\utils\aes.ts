import md5 from 'js-md5'
import globalEnv from '@/configs'
import CryptoJS from 'crypto-js'
import { isString } from '@/utils/is'
/**
 * @author：张胜
 * @desc：md5签名加密
 * */
export function createRequestSign (data: Record<string, any>, isJson: boolean, timestamps: number): string {
  let urlParams = ''
  const keyList = Object.keys(data).sort()
  const filterKeyList = keyList.filter(key => {
    if (data[key] !== null && data[key] !== '') return key
  })
  if (isJson) {
    urlParams = JSON.stringify(data) + `&timestamps=${timestamps}`
  } else {
    for(const key of filterKeyList) {
      urlParams += key !== filterKeyList[filterKeyList.length - 1] ? `${key}=${data[key]}&` : `${key}=${data[key]}`
    }
  }
  return (md5 as any)(`${urlParams}&signKey=${globalEnv.signKey}`)
}

const keyStr = globalEnv.secretKey
const ivStr = globalEnv.offset
/**
 * @author：张胜
 * @desc：aes 加密，解密
 * */
export const aes = {
  // 加密CBC模式
  encrypt (word: string) {
    const key = CryptoJS.enc.Utf8.parse(keyStr)
    const iv = CryptoJS.enc.Utf8.parse(ivStr)
    const srcs = CryptoJS.enc.Utf8.parse(word)
    const encrypted = CryptoJS.AES.encrypt(srcs, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    return globalEnv.aes + encrypted.toString()
  },
  // 解密CBC模式
  decrypt (word:string) {
    const key = CryptoJS.enc.Utf8.parse(keyStr)
    const iv = CryptoJS.enc.Utf8.parse(ivStr)
    const decrypt = CryptoJS.AES.decrypt(word, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    return decrypt.toString(CryptoJS.enc.Utf8)
  }
}
/**
 * @author：张胜
 * @desc：解密数据
 * */
export function decryptResponse (responseData: any) {
  let result = null
  if(isString(responseData) && responseData.indexOf(globalEnv.aes) !== -1) {
    const index = responseData.indexOf(globalEnv.aes)
    const tempStr = responseData.slice(index + globalEnv.aes.length, responseData.length)
    result = JSON.parse(aes.decrypt(tempStr))
    return result
  }
  return responseData
}
