<template>
  <div class="upload_warp">
    <el-upload
      :class="[
        'avatar-uploader',

        !isCustom ? 'custom-avatar-uploader' : '',

        disabled ? 'disabled-avatar' : ''
      ]"
      :data="data"
      :action="url"
      name="file"
      :accept="accept"
      :disabled="disabled"
      :show-file-list="false"
      @success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
      @progress="onProgress"
      v-if="!more"
      :headers="userInfo"
    >
      <template v-if="isCustom">
        <slot />
      </template>

      <template v-else>
        <div class="img-box" v-if="showImageUrl">
          <img :src="showImageUrl" >

          <div class="avatar" @click.stop v-if="showOperate">
            <el-icon
              @click.stop="clickHandler(imageUrl)"
              :size="12"
              color="#fff"
            ><View
            /></el-icon>
            <span class="division" v-if="!disabled"/>

            <i
              v-if="!disabled"
              class="icon-shanshu iconfont icon"
              @click.stop="delHandler"
            />
          </div>

          <slot name="extra" />
        </div>

        <slot name="loading" v-else-if="loading">
          <i class="el-icon-loading avatar-uploader-icon" />
        </slot>

        <slot name="plus" v-else>
          <el-icon color="#ccc" :size="24"><Plus /></el-icon>
        </slot>
      </template>
    </el-upload>

    <div class="upload_more" v-else>
      <el-upload
        :class="[
          'avatar-uploader-more',

          !isCustom ? 'custom-avatar-uploader' : '',

          disabled ? 'disabled-avatar' : ''
        ]"
        :data="data"
        :action="url"
        name="file"
        :accept="accept"
        :show-file-list="false"
        :disabled="disabled"
        @success="handleAvatarSuccess"
        :before-upload="beforeAvatarUpload"
        @remove="handleRemove"
        @preview="handlePictureCardPreview"
        :file-list="fileList"
        :limit="limit"
        @exceed="handleExceed"
        :headers="userInfo"
        v-if="!disabled"
      >
        <el-icon color="#ccc" :size="24"><Plus /></el-icon>
      </el-upload>

      <div class="img-container">
        <div class="img-box" v-for="item in moreUrl" :key="item">
          <img :src="picturePath(item)" >

          <div class="avatar" @click.stop v-if="showOperate">
            <i
              class="iconfont icon-xianshi icon"
              @click.stop="clickHandler(item)"
            />

            <span class="division" v-if="!disabled" />

            <i
              v-if="!disabled"
              class="el-icon-delete icon"
              @click.stop="handleRemove(item)"
            />
          </div>

          <slot name="extra" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import globalEnv from '@/configs'

import { Plus, View } from '@element-plus/icons-vue'
import { picturePath } from '@/utils'
import { getImgPx, imgToBase64 } from '@/utils/tools'
import { decryptResponse } from '@/utils/aes'
import { getToken } from '@/utils/auth'

import { isArray, isNullOrUndefOrEmpty } from '@/utils/is'

const props = defineProps({
  isCustom: {
    type: Boolean,

    default: false
  },

  size: {
    type: Number,

    default: 3
  },

  disabled: {
    type: Boolean,
    default: false
  },

  imgPath: {
    type: String,

    default: ''
  },

  accept: {
    type: String,
    default:
      'image/jpeg,image/jpg,image/png,image/svg,image/gif, image/bmp, image/tif'
  },

  showOperate: {
    type: Boolean,

    default: true
  },

  showDefaultImg: {
    type: Boolean,

    default: false
  },

  defaultImg: {
    type: String,

    default: () => ''
  },

  limit: {
    type: Number,

    default: 1
  },

  listType: {
    type: String,

    default: 'text'
  },

  more: {
    type: Boolean,

    default: false
  },

  pxCheck: {
    type: Boolean,

    default: false
  },

  pxObj: {
    type: Object,

    default: () => ({
      with: 0,

      height: 0
    })
  },

  isAvatar: {
    type: Boolean,

    default: true // 是否是头像上传，之前的文案都是头像，为了兼容以前，就默认为true
  }
})

const emit = defineEmits([
  'update:imgPath',
  'change',
  'handleDel',
  'onProgress',
  'success',
  'successMore',
  'imgViewer'
])

const url = ref(globalEnv.uploadApi)

const imageUrl = ref('')

const data = ref({
  pictureType: 'img'
})

const userInfo = ref({
  Authorization: getToken()
})

const fileList = ref([])

const loading = ref(false)

const showImageUrl = computed(() => {
  return imageUrl.value
    ? picturePath(imageUrl.value)
    : props.showDefaultImg
      ? props.defaultImg
      : ''
})

const moreUrl = computed(() => {
  if (isNullOrUndefOrEmpty(props.imgPath)) {
    return []
  }

  return isArray(JSON.parse(props.imgPath)) && JSON.parse(props.imgPath)
})

watch(
  () => props.imgPath,

  value => {
    imageUrl.value = value

    if (props.more) {
      const val = isNullOrUndefOrEmpty(value)
        ? []
        : isArray(JSON.parse(value))
          ? JSON.parse(value)
          : []

      fileList.value = val.map(i => ({
        url: i
      }))
    }
  },

  { immediate: true }
)

// 文件上传时的钩子函数
const onProgress = (event, file, fileList) => {
  loading.value = true
  emit('onProgress', event, file, fileList)
}

// 上传成功的回调函数
const handleAvatarSuccess = (res, file, fileList) => {

  const responseData = decryptResponse(res)
  fileList.forEach(item => {
    item.response = decryptResponse(item.response)
  })
  loading.value = false
  if (responseData.code === 200) {
    const uploadResponseData = responseData.data
    imageUrl.value = uploadResponseData[0].path
    emit(
      'update:imgPath',
      props.more
        ? JSON.stringify([...moreUrl.value, imageUrl.value])
        : imageUrl.value
    )
    emit('change')
    emit(
      'success',
      props.more
        ? JSON.stringify([...moreUrl.value, imageUrl.value])
        : imageUrl.value,
      uploadResponseData
    )
    emit('successMore', fileList)
  } else {
    ElMessage({
      type: 'error',
      message: responseData.msg
    })
  }
}

// 上传前的验证函数
const beforeAvatarUpload = async file => {
  // 验证分辨率
  let checkPx = false
  if (props.pxCheck) {
    const base64 = await imgToBase64(file)
    const pxObj = await getImgPx(base64)
    checkPx =
      pxObj.width <= props.pxCheck.width && pxObj.height <= props.pxCheck.height
  }
  const isJPG =
    file.type === 'image/jpeg' ||
    file.type === 'image/jpg' ||
    file.type === 'image/png'
  const isLtM = file.size / 1024 / 1024 < props.size
  if (!isJPG) {
    ElMessage.error(
      `上传${props.isAvatar ? '头像' : ''}图片只能是 jpg, png 格式!`
    )
  }
  if (!isLtM) {
    ElMessage.error(
      `上传${props.isAvatar ? '头像' : ''}图片大小不能超过${props.size}MB!`
    )
  }
  if (props.pxCheck && !checkPx) {
    ElMessage.error(`上传图片分辨率不能超过${pxObj.width}px*${pxObj.height}px`)
  }
  return new Promise((resolve, reject) => {
    isJPG && isLtM && (props.pxCheck ? checkPx : true)
      ? resolve(true)
      : reject()
  })
}

// 点击图片的处理函数
const clickHandler = url => {
  // this.$store.dispatch('setImageViewer', [picturePath(url)])
  emit('imgViewer', url)
}

// 删除图片的处理函数
const delHandler = () => {
  emit('update:imgPath', '')
  emit('change')
}

// 多图片的删除
const handleRemove = url => {
  emit('update:imgPath', JSON.stringify(moreUrl.value.filter(i => i !== url)))
  emit('change')
}

// 上传超过限定数
const handleExceed = () => {
  this.$message.warning(`最多上传${limit.value}张图片！`)
}

// 多张图片的预览查看
const handlePictureCardPreview = file => {
  console.log(file)
  this.$store.dispatch('setImageViewer', [file.url])
}
</script>

<style scoped lang="scss">
.upload_warp {
  width: 100%;
  height: 100%;
  .upload_more {
    display: flex;
    .img-container {
      width: 100%;
      white-space: nowrap;
      overflow-x: scroll;
    }

    > div {
      display: inline-block;
      margin-right: 8px;
    }
    .img-box {
      width: 80px;
      height: 80px;
      overflow: hidden;
      position: relative;
      display: inline-block;
      margin: 0 5px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      &:hover {
        .avatar {
          visibility: visible;
        }
      }
      .avatar {
        width: 100%;
        height: 100%;
        display: inline-flex;
        position: absolute;
        left: 0;
        top: 0;
        visibility: hidden;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.4);
        z-index: 55;
        overflow: hidden;
        cursor: pointer;
        .icon {
          font-size: 18px;
          color: rgba(255, 255, 255, 0.9);
          cursor: pointer;
        }
        .division {
          width: 1px;
          height: 12px;
          margin: 0 10px;
          background: #fff;
        }
      }
    }
  }
}
.avatar-uploader {
  display: inline-block;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  border: 1px dashed rgba(209, 209, 209, 1);
  background: #f9f9f9;
  :deep(.avatar-uploader-icon) {
    font-size: 24px;
    color: #cccccc;
  }

  :deep(.el-upload) {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .img-box {
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      &:hover {
        .avatar {
          visibility: visible;
        }
      }
      .avatar {
        width: 100%;
        height: 100%;
        display: inline-flex;
        position: absolute;
        left: 0;
        top: 0;
        visibility: hidden;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.4);
        z-index: 55;
        overflow: hidden;
        cursor: pointer;
        .icon {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.9);
          cursor: pointer;
        }
        .division {
          width: 1px;
          height: 12px;
          margin: 0 10px;
          background: #fff;
        }
      }
    }
  }
}
.avatar-uploader-more {
  display: inline-block;
  :deep(.avatar-uploader-icon) {
    position: absolute;
    font-size: 24px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #cccccc;
  }
  :deep(.el-upload) {
    display: inline-block;
    justify-content: center;
    align-items: center;
    position: relative;
    display: inline-block;
    background: #f9f9f9;
    width: 80px;
    height: 80px;
    border-radius: 4px;
    border: 1px dashed rgba(209, 209, 209, 1);
    .avatar-uploader-icon {
      font-size: 24px;
      color: #cccccc;
    }
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    position: relative;
    margin-right: 10px;
    width: 70px;
    height: 70px;
    border-radius: 4px;
  }
}
.disabled-avatar {
  :deep(.el-upload) {
    cursor: not-allowed;
  }
}
</style>
