<template>
  <div class="personnelSelectionPanel">
    <div class="top">
      <div class="title">部门多选</div>
      <i @click="close"
         class="iconfont icon-close"/>
    </div>
    <div class="content">
      <el-tabs 
        v-model="activeName"
        class="personnelSelectionPanelTabs">
        <el-tab-pane 
          label="组织架构"
          name="organizational">
          <Organizational 
            ref="organizationalRef"
            :data="componentData.deComponentTreeVO"
            :checkedDep="checkedDep"
            :isIncludeChild="isIncludeChild"
            @updateValue="updateValue"/>
        </el-tab-pane>
        <el-tab-pane 
          label="动态参数"
          v-if="configuration"
          name="dynamicParameters">
          <slot name="dynamic">
            <DynamicParametersVue
              :data="dynamicParameters"
              @updateParameter="updateParameter"/>
          </slot>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="bottom">
      <div class="left">
        <div v-if="activeName === 'organizational'">
          <el-checkbox 
            v-model="allChecked"
            label="选中所有可见选项" />
          <el-checkbox 
            v-model="isIncludeChild"
            label="父部门不包含子部门员工" />
        </div>
      </div>
      <div class="right">
        <RxkButton 
          type="pain"
          @click="close">取消</RxkButton>
        <RxkButton type="primary" @click="confirm">确定</RxkButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, unref, watch } from 'vue'
import type { RxkButton } from '@/components/common/RxkButton'
import Organizational from './components/organizationalPanel.vue'
import DynamicParametersVue from './components/dynamicParameters.vue'
import type { BasicProps, ComponentData, DeComponentTreeVO } from './type'
import { cloneDeep, recursion } from '@/utils/tools'
import { isArray, isFunc } from '@/utils/is'
import type { PanelActionType } from './hooks/usePanel'

const emit = defineEmits(['close', 'confirm', 'registerPanel', 'update'])
const allChecked = ref<boolean>(false) // 选中所有可见选项
const organizationalRef = ref()

const activeName = ref('organizational')
const isIncludeChild = ref<boolean>(false) // 父部门不包含子部门员工
const configuration = ref<boolean>(false) // 是否展示动态参数配置
const dynamicParameters = ref<Recordable>()
const componentData = ref<ComponentData>({})
const checkedDep = ref<DeComponentTreeVO[]>([])
const cloneCheckedDep = ref<DeComponentTreeVO[]>([])
const orginPropsData = ref<BasicProps>({})
const orginComponentData = ref<ComponentData>({})

async function reload (propsValue: BasicProps, fn?:Fn) {
  const data = cloneDeep(cloneCheckedDep.value)
  updateValue(data)
  fn && fn()
}

async function init (propsValue: BasicProps, fn?:Fn) {
  orginPropsData.value = cloneDeep(propsValue)
  console.log(propsValue, 'propsValueDep')
  activeName.value = propsValue.activeName || 'organizational'
  isIncludeChild.value = propsValue.isIncludeChild || false
  configuration.value = propsValue.configuration || false
  allChecked.value = propsValue.selectedShow || false
  dynamicParameters.value = propsValue.dynamicParameters
  // 获取数据
  const { data, api, menuId } = unref(orginPropsData.value)  
  if(!api && data) {
    componentData.value = unref(data)
    orginComponentData.value = cloneDeep(data)
    await handleCheckedValue(propsValue.showValueData || [])
    return false
  }
  if (!api || !isFunc(api)) return
  try {
    const params = {
      ...propsValue.paramsData
    }
    if(propsValue.showValueData) {
      params.disableDeptId = propsValue.showValueData?.map(item => item.id)
    }
    const res = await api(params, menuId ? { MenuId: menuId } : '')
    if(isArray(res)) {
      componentData.value = res[0]
      orginComponentData.value = cloneDeep(res[0])
    } else {
      componentData.value = unref(res)
      orginComponentData.value = cloneDeep(res)
    }
    await handleCheckedValue(propsValue.showValueData || [])
    fn && fn()
  }catch (err) {
    console.log(err)
  }
}

// 处理回显值
async function handleCheckedValue (showValueData: DeComponentTreeVO[]) {
  
  let arr: DeComponentTreeVO[] = []
  if(showValueData && showValueData.length) {
    const ids = showValueData.map(item => item.id)
    recursion(orginComponentData?.value?.showDeptList || [], (item) => {
      if(ids.includes(item.id)) {
        arr.push(item)
      }
    })
    recursion(orginComponentData?.value?.deComponentTreeVO || [], (item) => {
      if(ids.includes(item.id)) {
        arr.push(item)
      }
    })
  }
  const uniqueData = new Map()
  for (const item of arr) {
    uniqueData.set(item.id, item)
  }

  // 将Map转换回数组形式
  const deduplicatedData = Array.from(uniqueData.values())

  console.log(deduplicatedData, '0-00-0')
  cloneCheckedDep.value = cloneDeep(deduplicatedData)
  updateValue(deduplicatedData)
  emit('update', deduplicatedData)
}

const panelActionType: PanelActionType = {
  init,
  getCheckedDep,
  updateValue,
  getSelectedShow,
  getContainChildren,
  getParameter,
  reload,
  handleCheckedValue
}
emit('registerPanel', panelActionType)

function close () {
  emit('close')
  const clone = cloneDeep(cloneCheckedDep.value)
  emit('update', clone)
}

watch(isIncludeChild, (val) => {
  organizationalRef.value.handleIncludeChild(val)
})

watch(allChecked, (val) => {
  organizationalRef.value.handleAllCheck(val)
})

function confirm (){
  cloneCheckedDep.value = cloneDeep(checkedDep.value)
  emit('confirm')
}

function updateValue (data: DeComponentTreeVO[], type?: string){
  console.log(data, 'updateValue')
  checkedDep.value = data
  if(type && type === 'clear') {
    cloneCheckedDep.value = cloneDeep(checkedDep.value)
  }
}

function getSelectedShow () {
  return unref(allChecked.value)
}
function getContainChildren () {
  return unref(isIncludeChild.value)
}

function getCheckedDep () {
  return unref(checkedDep.value)
}
function updateParameter (params: Recordable) {
  dynamicParameters.value = params 
}
function getParameter () {
  return unref(dynamicParameters.value)
}

</script>

<style lang="scss" scoped>
.personnelSelectionPanel {
  max-height: 50vh;
  overflow-y: hidden;
  .top {
    @include flex-center(row, space-between, center);
    padding: 17px 24px;
    border-bottom: 1px solid #EBEEF5;
    .title {
      font-weight: 700;
      color: $gray;
      font-size: $saas-font-size-small;
    }
    .icon-close {
      cursor: pointer;
    }
  }
  .content {
    max-height: calc(50vh - 120px);
    height: 400px;
    overflow-y: auto;
    // @include flex-center(row, normal, normal);
    border-bottom: 1px solid #EBEEF5;
    padding: 0 16px;
    .personnelSelectionPanelTabs {
      width: 100%;
      height: 100%;
      --el-tabs-header-height: 48px !important;
      :deep(.el-tabs__header) {
        margin: 0;
      }
      :deep(.el-tabs__content) {
        height: calc(100% - 48px);
      }
      :deep(.el-tabs__nav) {
        transform: translateX(16px) !important;
      }
      :deep(.el-tab-pane) {
        height: 100%;
      }
      :deep(.el-tabs__active-bar) {
        background-color: $primary-color;
      }
      :deep(.el-tabs__item.is-active) {
        color: $primary-color;
        font-weight: 700;
      }
      :deep(.el-tabs__item) {
        color: $secondary-text-color;
      }
      :deep(.el-tabs__nav-wrap::after) {
        height: 1px;
        background-color: #EBEEF5;
      }
    }
    .left {
      width: 250px;
      border-right: 1px solid #EBEEF5;
      overflow-y: auto;
    }
  }
  .bottom {
    padding: 16px 24px;
    @include flex-center(row, space-between, center);
    .left {
      :deep(.el-checkbox) {
        margin-right: 16px;
      }
    }
  }
}
</style>