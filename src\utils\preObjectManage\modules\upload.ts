// 附件
import BaseData, { getOperate } from '@/utils/preObjectManage/common'
import type { ComponentType, OptionType } from '@/types/preObjectManage'
import { fieldTypeConfig, fieldTypeEnum, operateListEnum } from '@/enums/preObjectManage'
import type { componentOperateListType } from '@/types/preObjectManage'
import { isNullOrUndefOrEmpty } from '@/utils/is'

// 上传附件1
export class Enclosure extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '上传附件'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.FILE_ONE,
        value: true
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.SHOW,
        value: true
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.enclosure,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.enclosure
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 上传图片
export class Picture extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '上传图片'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.PICTURE_ONE,
        value: true
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.SHOW,
        value: true
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.picture,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.picture
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}