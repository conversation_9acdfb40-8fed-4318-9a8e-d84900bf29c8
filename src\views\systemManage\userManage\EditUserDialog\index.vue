<template>
  <RxkDrawer
    v-model="state.drawer"
    size="480"
    :title="title"
    :btnLoading="btnLoading"
    @close="resetForm(ruleFormRef)"
    @confirm="(fn)=>{submitForm(ruleFormRef, fn)}"
  >
    <template v-slot:content>
      <div class="tw-p-[24px]">
        <el-skeleton :rows="5" animated v-show="loading" />
        <el-form
          v-show="!loading"
          ref="ruleFormRef"
          :model="formState"
          :rules="rules"
          class="reset-form-label"
          label-width="90"
          label-position="left"
        >
          <el-form-item label="用户姓名" prop="realName">
            <el-input v-model="formState.realName"
                      maxlength="10"
                      placeholder="请输入"
                      clearable />
          </el-form-item>
          <el-form-item label="联系电话" prop="mobile">
            <el-input v-model="formState.mobile"
                      maxlength="11"
                      placeholder="请输入"
                      clearable />
          </el-form-item>
          <el-form-item label="所属部门" prop="departmentId">
            <el-tree-select style="width:100%"
                            v-model="formState.departmentId"
                            :data="departmentTree"
                            :props="{ label: 'name', value: 'id', key: 'id' }"
                            check-strictly
                            placeholder="请选择"
                            :render-after-expand="false"
                            @change="departmentChange"
            />
          </el-form-item>
          <el-form-item label="部门负责人" prop="departmentFlag">
            <el-radio-group v-model="formState.departmentFlag">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="直属上级" prop="supervisorId">
            <el-input disabled :model-value="supervisor.supervisorName || '-'"/>
          </el-form-item>
          <el-form-item label="配置角色" prop="roleIdList">
            <el-select v-model="formState.roleIdList"
                       style="width:100%"
                       multiple
                       placeholder="请选择"
                       clearable
                       collapse-tags
                       collapse-tags-tooltip>
              <el-option v-for="item in roleList"
                         :label="item.name"
                         :value="item.id"
                         :key="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="!rowData?.id" label="登录帐号" prop="username">
            <el-input :disabled="rowData?.id?true:false"
                      placeholder="请输入"
                      clearable
                      v-model="formState.username"
            />
          </el-form-item>
          <el-form-item v-if="!rowData?.id" label="登录密码" prop="password">
            <el-input :disabled="rowData?.id?true:false"
                      placeholder="请输入"
                      clearable
                      v-model="formState.password"
            />
          </el-form-item>
          <el-form-item label="状态" prop="state">
            <el-select style="width:100%" v-model="formState.state"  placeholder="请选择">
              <el-option label="启用" value="ENABLED" key="ENABLED" />
              <el-option label="禁用" value="DISABLED" key="DISABLED" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <!--    <template #footer>-->
    <!--      <div style="text-align: left">-->
    <!--        <el-button :loading="btnLoading" @click="submitForm(ruleFormRef)" type="primary">确定</el-button>-->
    <!--        <el-button @click="resetForm(ruleFormRef)">取消</el-button>-->
    <!--      </div>-->
    <!--    </template>-->
  </RxkDrawer>
</template>
<script setup lang="ts">
import { reactive, ref, defineProps, onMounted, computed } from 'vue'
import {
  getDepartmentTree,
  addUser,
  editUser,
  getManagerByDepartmentId,
  getUserDetail,
  createUserPassword
} from '@/apis/userManage'
import { getRoleArrayApi } from '@/apis/roleManage'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { RxkDrawer } from '@/components/common/RxkDrawer'

interface RuleForm {
  departmentFlag: number,
  departmentId: number | undefined,
  mobile: string,
  password: string,
  realName: string,
  roleIdList: Array<number>,
  state: string,
  supervisorId: number | undefined,
  username: string
  externalLoginSwitch: boolean
}

const props = defineProps({
  rowData: Object
})

const emits = defineEmits(['handleClose', 'handleSubmit'])

const title = computed(() => {
  return props.rowData?.id ? '编辑用户' : '添加用户'
})

const ruleFormRef = ref<FormInstance>()
const formState = reactive<RuleForm>({
  realName: '',
  mobile: '',
  departmentFlag: 0,
  departmentId: undefined,
  username: '',
  password: '',
  roleIdList: [],
  state: '',
  externalLoginSwitch: false,
  supervisorId: undefined
})

const rules = reactive<FormRules<RuleForm>>({
  realName: [
    { required: true, message: '请输入用户姓名', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^(?:(?:\+|00)86)?1\d{10}$/,
      message: '请输入合法手机号/电话号',
      trigger: 'blur'
    }
  ],
  departmentId: [
    {
      required: true,
      message: '请选择所属部门',
      trigger: 'change'
    }
  ],
  departmentFlag: [
    {
      required: true,
      message: '请选择是否部门负责人',
      trigger: 'change'
    }
  ],
  supervisorId: [
    {
      required: true,
      message: '请选择直属上级',
      trigger: 'change'
    }
  ],
  roleIdList: [
    {
      required: true,
      message: '请选择角色',
      trigger: 'change'
    }
  ],
  state: [
    {
      required: true,
      message: '请选择状态',
      trigger: 'change'
    }
  ],
  username: [
    { required: true, message: '请输入登录账号', trigger: 'blur' },
    {
      pattern: /^(?=.*\d)(?=.*[a-zA-Z])[a-zA-Z0-9]{6,10}$/,
      message: '请输入6-10位包含数字+字母的组合',
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入登录密码', trigger: 'blur' },
    {
      pattern: /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[_\-.*!@#$%^&*()+~`,<>?/|:;=]).{8,16}$/,
      message: '请输入8-16位包含数字+字母+特殊字符的组合',
      trigger: 'blur'
    }
  ]
})

const departmentTree = ref<any[]>([])

const roleList = ref<any[]>([])

const supervisor = ref<any>({})

const loading = ref(false)

const btnLoading = ref(true)

// const isEdit = computed(() => {
//   return props.rowData?.id
// })

// const disabledComputed = computed(() => {
//   if(!isEdit.value) {
//     return supervisor.value.managerFlag
//   } else {
//     if(supervisor.value?.currentManagerId === props.rowData?.id){
//       return false
//     } else {
//       return supervisor.value.managerFlag
//     }
//   }
// })

const state = reactive({
  drawer: true,
  managerFlag: false
})

onMounted(async () => {
  try {
    loading.value = true
    const data: any = await getDepartmentTree()
    departmentTree.value = data
    const roleArr:any = await getRoleArrayApi()
    roleList.value = roleArr
    if(props.rowData?.id){
      await setFormState(props.rowData)
    } else {
      const res = await createUserPassword()
      formState.username = res.username
      formState.password = res.password
      formState.departmentId = props.rowData?.departmentId
      if(formState.departmentId){
        await departmentChange(formState.departmentId)
      }
    }
    loading.value = false
  } catch(e){
    console.log(e)
  }

})

const setFormState = async (data:any) => {
  try {
    const res = await getUserDetail(data.id)
    formState.realName = res.realName
    formState.mobile = res.mobile
    formState.departmentId = res.departmentVO.id
    formState.departmentFlag = res?.departmentVO?.managerId === data.id ? 1 : 0
    formState.roleIdList = res?.roleVOList?.map((el:any) => el.id) || []
    formState.username = res.username
    formState.password = res.password
    formState.externalLoginSwitch = res.externalLoginSwitch
    formState.state = res.state.name || res.state
    if(formState?.departmentId){
      await departmentChange(formState.departmentId)
    }
  } catch (e) {
    console.log(e)
  }
}

const departmentChange = async (departmentId:number) => {
  supervisor.value = {}
  formState.supervisorId = undefined
  try {
    const res = await getManagerByDepartmentId({ departmentId, userId: props.rowData?.id })
    supervisor.value = res
    formState.supervisorId = res.supervisorId
    if(supervisor.value?.currentManagerId === props.rowData?.id){
      formState.departmentFlag = 1
    } else {
      formState.departmentFlag = 0
    }
  } catch(e){
    console.log(e)
  }
}

const submitForm = async (formEl: FormInstance | undefined, fn:Fn) => {
  if (!formEl) {
    fn && fn()
    return
  }
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        let params:any = { ...formState, id: undefined as any }
        params.departmentFlag = !!params.departmentFlag
        let api = addUser
        if(props?.rowData?.id){
          params.id = props?.rowData?.id
          api = editUser
        }
        await api(params)
        ElMessage({
          message: props?.rowData?.id ? '修改成功' : '新增成功',
          type: 'success'
        })
        emits('handleSubmit')
        resetForm(ruleFormRef.value)
      } finally {
        fn && fn()
      }
    } else {
      fn && fn()
      console.log('error submit!', fields)
    }
  })
}
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  emits('handleClose')
}
</script>
