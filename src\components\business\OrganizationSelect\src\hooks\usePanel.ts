import { ref, unref } from 'vue'
import type { BasicProps, UserVOList } from '../type'
export interface PanelActionType{
  init: (props:BasicProps, fn?: Fn) => void;
  updateValue: (data: UserVOList[], type?: string) => void;
  remove: (value: string, type: string) => void;
}
export function usePanel ():[(instance: PanelActionType) => void, PanelActionType] {
  const panelRef = ref<Nullable<PanelActionType>>(null)
  function registerPanel (instance: PanelActionType, fn?:Fn){
    if (instance === unref(panelRef)) return
    panelRef.value = instance
    fn && fn()
  }
  function getPanelInstance (): PanelActionType {
    const panel = unref(panelRef.value)
    return panel as PanelActionType
  }
  const methods: PanelActionType = {
    init: (props:BasicProps, fn?: Fn) => {
      getPanelInstance()?.init?.(unref(props), fn)
    },
    updateValue: (data: UserVOList[], type?: string) => {
      getPanelInstance()?.updateValue(data, type)
    },
    remove: (value: string, type: string) => {
      getPanelInstance()?.remove(value, type)
    }
  }
  return [registerPanel, methods]
}