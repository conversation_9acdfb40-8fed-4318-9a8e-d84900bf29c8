import type { FormActionType, FormProps } from '@/components/business/searchFilter/src/type'
import { onUnmounted, ref, unref, type Ref, type ComputedRef } from 'vue'
import { getDynamicProps } from '@/utils'
export type DynamicProps<T> = {
  [P in keyof T]: Ref<T[P]> | T[P] | ComputedRef<T[P]>;
};

type Props = Partial<DynamicProps<FormProps>>;
export function useSearch (props?: Props) :[(instance: FormActionType) => void, FormActionType] {
  const formRef = ref<Nullable<FormActionType>>(null)
  function getForm () {
    const form = unref(formRef)
    return form as FormActionType
  }

  function register (instance: FormActionType) {
    onUnmounted(() => {
      formRef.value = null
    })
    if (instance === unref(formRef)) return
    formRef.value = instance
    props && instance.setProps(getDynamicProps(props))
  }

  const methods: FormActionType = {
    setProps: (formProps: Partial<FormProps>) => {
      getForm().setProps(formProps)
    },
    submit: () => {
      getForm().submit()
    },
    resetFormData: () => {
      getForm().resetFormData()
    }
  }
  return [register, methods]
}