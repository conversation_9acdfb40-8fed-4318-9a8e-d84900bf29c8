<template>
  <RxkDialog
    v-model:modelValue="show"
    title="渠道商链接"
    :close-on-click-modal="false"
    custom-class="channel-group-dialog"
    width="480"
    @sure="() => show = false"
  >
    <div class="channel-group-dialog-link" v-loading="loading">
      <p style="height: 66px">
        <span class="title">链接：</span>
        <el-link :href="state.linkUrl" type="primary" target="_blank">
          {{ state.linkUrl }}
        </el-link>
      </p>
      <p>
        <span class="title">提取码：</span>
        <span>{{ state.extractionCode }}</span>
      </p>
      <div class="copy-btn">
        <RxkButton type="primary" @click="handleCopy(state.extractionCode)" >
          复制提取码
        </RxkButton>
      </div>
      <br >
    </div>
  </RxkDialog>
</template>

<script setup lang="tsx">
import { computed, reactive, ref } from 'vue'
import { copyText } from '@/hooks/useCopyText'
import { RxkDialog } from '@/components/common/RxkDialog'
import { fetchGetChannelLink } from '@/apis/carlife'

const emits = defineEmits(['update:modelValue', 'refresh'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const state = reactive({
  linkUrl: '',
  extractionCode: ''
})

const show = computed({
  get: () => props.modelValue,
  set: (modelValue: boolean) => emits('update:modelValue', modelValue)
})

const handleCopy = async (data: string) => {
  copyText(data)
}

const loading = ref(false)
const init = async (data: Recordable) => {
  loading.value = true
  const { channelId, extractionCode, settleType } = data
  state.extractionCode = extractionCode
  
  const res = await fetchGetChannelLink({ channelId })
  state.linkUrl = res.url + `&settleType=${settleType}`
  loading.value = false
}

defineExpose({
  init
})
</script>

<style lang="scss">
.channel-group-dialog {
  .custom-search-item {
    width: 100% !important;
    margin-bottom: 24px;
    padding: 0 24px;
  }
  .channel-group-dialog-link {
    padding: 24px;
    p {
      padding: 12px 0;
      font-size: 16px;
      line-height: 1.5;
      display: flex;
    }
    .el-link {
      display: inline-block;
      word-wrap: break-word;
      flex: 1;
    }
    .el-link__inner {
      flex: 1;
      word-wrap: break-word;
      word-break: break-all;
      display: inline-block;
    }
    .title {
      width: fit-content;
      flex-shrink: 0;
    }
    .copy-btn {
      display: flex;
      justify-content: center;
      padding-top: 12px;
    }
  }
}
</style>
