export type RechargeEnum = {
  label: string
  value: number
  key?: string // 自定义组件
  children?: RechargeEnum[]
}
export const UserCoinChangeTypeEnum: RechargeEnum[] = [
  {
    label: '充值',
    value: 1,
    key: 'rechargeItem'
  },
  // {
  //   label: '赠送',
  //   value: 3,
  //   key: 'giveItem'
  // },
  {
    label: '扣减',
    value: 2,
    key: 'deductionsItem'
  },

  {
    label: '冻结',
    value: 4,
    key: 'freeItem'
  },
  {
    label: '解冻',
    value: 5,
    key: 'thawItem'
  },
  {
    label: '退款',
    value: 6,
    key: 'refundItem'
  },
  {
    label: '补登',
    value: 7,
    key: 'suppleItem'
  }
]
export const RechargeType: RechargeEnum[] = [
  {
    label: '余额充值',
    value: 10011
  },
  {
    label: '消耗赠送',
    value: 10012
  },
  {
    label: '赠送余额',
    value: 10013
  },
  {
    label: '补单返量',
    value: 10014
  },
  {
    label: '活动赠送',
    value: 10015
  },
  {
    label: '特殊补偿',
    value: 10016
  }
]
/**
 * 扣减-交易明细
 */
// 个人账户下拉可选的code
export const accountOptionalEnum = [2008, 4002, 4003, 5002, 5003]
export const DeductionsType: RechargeEnum[] = [
  {
    label: '赠送余额',
    value: 2007,
    children: [{ label: '赠送余额扣减', value: 20071 }]
  },
  {
    label: '特殊费用',
    value: 2008,
    children: [{ label: '罚款', value: 20081 }]
  }
]

export const GiveType: RechargeEnum[] = [
  {
    label: '软件费',
    value: 3001,
    children: [
      { label: '赠送坐席', value: 30011 },
      { label: '赠送坐席时长', value: 30012 },
      { label: '赠送授权账号', value: 30013 },
      { label: '赠送财务账户', value: 30014 }
    ]
  }
]
/**
 * 冻结-交易明细
 */
export const FreeType: RechargeEnum[] = [
  {
    label: '余额冻结',
    value: 4001,
    children: [{ label: '余额冻结', value: 40011 }]
  },
  {
    label: '余额转保证金',
    value: 4002,
    children: [{ label: '余额转保证金', value: 40021 }]
  }
]
/**
 * 解冻-交易明细
 */
export const ThawType: RechargeEnum[] = [
  {
    label: '余额解冻结',
    value: 5001,
    children: [{ label: '余额解冻结', value: 50011 }]
  },
  {
    label: '保证金转余额',
    value: 5002,
    children: [{ label: '保证金转余额', value: 50021 }]
  },
  {
    label: '流量包套餐解冻结',
    value: 5003,
    children: [{ label: '流量包套餐解冻结', value: 50031 }]
  },
  {
    label: '融金通结算余额解冻结',
    value: 5004,
    children: [{ label: '融金通结算余额解冻结', value: 50041 }]
  }
]
/**
 * 退款-交易明细
 */
export const RefundType: RechargeEnum[] = [
  {
    label: '退余额',
    value: 6001,
    children: [{ label: '退余额', value: 60011 }]
  },
  {
    label: '退软件费',
    value: 6002,
    children: [
      { label: '退授权账号费', value: 60021 },
      { label: '退软件费', value: 60022 },
      { label: '退财务账户费', value: 60023 }
      // { label: '退增值服务费-1（月结）', value: 60024 },
      // { label: '退增值服务费-1', value: 60025 }
    ]
  },
  {
    label: '退坐席费',
    value: 6003,
    children: [{ label: '退坐席费', value: 60031 }]
  },
  {
    label: '退增值套餐费',
    value: 6004,
    children: [
      { label: '退录音存储费', value: 60041 },
      { label: '退号码校验套餐费', value: 60042 }
    ]
  },
  {
    label: '退增值应用费',
    value: 6005,
    children: [
      { label: '退自定义品牌费', value: 60051 },
      { label: '退自定义LOGO费', value: 60052 },
      { label: '退增值应用费', value: 60053 },
      { label: '退AI智能视频面签', value: 60056 }
    ]
  },
  {
    label: '退硬件设备费',
    value: 6006,
    children: [{ label: '退话机设备费', value: 60061 }]
  },
  {
    label: '退服务支持费',
    value: 6007,
    children: [
      { label: '退软件部署费', value: 60071 },
      { label: '退软件运维费', value: 60072 }
    ]
  }
]
/**
 * 补登
 */
export const SuppleType: RechargeEnum[] = [
  {
    label: '余额充值补登',
    value: 7001,
    children: [{ label: '余额充值补登', value: 70011 }]
  },
  {
    label: '余额扣减补登',
    value: 7002,
    children: [{ label: '余额扣减补登', value: 70021 }]
  }
]
export const accountStateLabel = [
  {
    value: 1,
    label: '预'
  },
  {
    value: 2,
    label: '活'
  },
  {
    value: 3,
    label: '冻'
  }
]
export enum SeatEnum {
  Renew = 20014,
  buySeat = 30011
}
export const dataSourseType = {
  1: 'A官方充值',
  2: 'A官方购买',
  3: 'A官方操作',
  4: 'B在线充值',
  5: 'B应用商店',
  6: 'B推广产品消耗',
  7: 'A老系统数据'
}
export const auditType = {
  100: '待审批',
  200: '已通过',
  300: '不通过',
  400: '被驳回',
  500: '已超时'
}
