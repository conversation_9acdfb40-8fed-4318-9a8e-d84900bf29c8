import { ref, type Ref } from 'vue'
import { excelExport } from '@/utils/index'
type useExportReturnType = {
  loading: Ref<boolean>
  exportExcel: (
    data?: Record<string, any>,
    method?: 'GET' | 'POST',
    headers?: any
  ) => Promise<void>
}

export function useExportHook(url: string): useExportReturnType {
  const loading = ref(false)
  const exportExcel = async (
    data?: Record<string, any>,
    method: 'GET' | 'POST' = 'POST',
    headers?: any
  ) => {
    loading.value = true
    try {
      await excelExport(url, data, method, headers)
    } finally {
      loading.value = false
    }
  }
  return {
    loading,
    exportExcel
  }
}
