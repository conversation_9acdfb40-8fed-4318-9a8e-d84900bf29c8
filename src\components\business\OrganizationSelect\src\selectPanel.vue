<template>
  <div class="personnelSelectionPanel" v-loading="loading">
    <div class="top">
      <div class="title">{{ orginPropsData.onlyDep ? '部门' : '成员' }}列表</div>
      <i @click="close"
         class="iconfont icon-close"/>
    </div>
    <div class="checked-content">
      <div class="checked-item" v-for="(item) in checkedData.deComponentTree" :key="item.value">
        <span class="tw-mr-4">{{item.label}}</span>
        <i @click="handleRemove(item, 'deComponentTree')"
           class="iconfont icon-close tw-cursor-pointer"/>
      </div>
      <div class="checked-item" v-for="(item) in checkedData.roleList" :key="item.value">
        <span class="tw-mr-4">{{item.label}}</span>
        <i @click="handleRemove(item, 'roleList')"
           class="iconfont icon-close tw-cursor-pointer"/>
      </div>
      <div class="checked-item" v-for="(item) in checkedData.userList" :key="item.value">
        <span class="tw-mr-4">{{item.label}}</span>
        <i @click="handleRemove(item, 'userList')"
           class="iconfont icon-close tw-cursor-pointer"/>
      </div>
      <!--      <div class="checked-item" v-for="(item) in checkedData.userResignList" :key="item.value">-->
      <!--        <span class="tw-mr-4">{{item.label}}</span>-->
      <!--        <i @click="handleRemove(item, 'userResignList')"-->
      <!--           class="iconfont icon-close tw-cursor-pointer"/>-->
      <!--      </div>-->
    </div>
    <div class="content">
      <el-tabs 
        v-model="activeName"
        class="personnelSelectionPanelTabs">
        <el-tab-pane
          label="组织架构"
          name="organizational">
          <Organizational
            ref="organizationalRef"
            v-model:data="queryData.deComponentTree"
            :orginComponentData="queryData.deComponentTree"
            :selectedData="checkedData.deComponentTree"
            :allChecked="allChecked"
            :isNoIncludeChild="isNoIncludeChild"
            :onlyDep="orginPropsData.onlyDep"
            @updateValue="(val)=>{updateValue(val, 'deComponentTree')}"/>
        </el-tab-pane>
        <el-tab-pane
          v-if="!onlyDep"
          label="角色"
          name="role">
          <Role
            ref="roleRef"
            v-model:data="queryData.roleList"
            :selectedData="checkedData.roleList"
            @updateValue="(val)=>{updateValue(val, 'roleList')}"/>
        </el-tab-pane>
        <el-tab-pane
          v-if="!onlyDep"
          label="成员"
          name="members">
          <Members
            ref="membersRef"
            v-model:data="queryData.userList"
            :selectedData="checkedData.userList"
            @updateValue="(val)=>{updateValue(val, 'userList')}"/>
        </el-tab-pane>
        <el-tab-pane 
          v-if="dynamicParameters"
          label="动态参数"
          name="dynamicParameters">
          <DynamicParametersVue
            :onlyDep="onlyDep"
            :data="dynamicParameters"
          >
            <template #dynamic>
              <slot name="dynamic"/>
            </template>
          </DynamicParametersVue>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="bottom">
      <div class="left">
        <template v-if="activeName === 'organizational'">
          <el-checkbox
            v-model="allChecked"
            label="选中所有可见选项" />
          <el-checkbox
            v-model="isNoIncludeChild"
            label="父部门不包含子部门员工" />
        </template>
      </div>
      <div class="right">
        <RxkButton 
          @click="close">取消</RxkButton>
        <RxkButton v-if="showSure" type="primary" @click="confirm">确定</RxkButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, unref, reactive, nextTick } from 'vue'
import type { RxkButton } from '@/components/common/RxkButton'
import Organizational from './components/organizationalPanel.vue'
import Members from './components/membersPanel.vue'
import DynamicParametersVue from './components/dynamicParameters.vue'
import Role from './components/rolePanel.vue'
import type { BasicProps, DynamicParameters } from './type'
import { isFunc } from '@/utils/is'
import { cloneDeep } from '@/utils/tools'
import { DataType } from './type'
defineProps({
  showSure: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['close', 'confirm', 'registerPanel'])
const organizationalRef = ref()
const roleRef = ref()
const membersRef = ref()

const activeName = ref('members')
const configuration = ref<boolean>(true) // 是否展示动态参数配置
const dynamicParameters = ref<DynamicParameters | undefined>(undefined)
const orginPropsData = ref<BasicProps>({})
const onlyDep = ref(false)
const loading = ref(false)

const allChecked = ref(false) // 选中所有可见选项
const isNoIncludeChild = ref(false) // 父部门不包含子部门员工

const queryData = reactive({
  deComponentTree: [] as Recordable[],
  roleList: [] as Recordable[],
  userResignList: [] as Recordable[],
  userList: [] as Recordable[]
})

const checkedData = reactive({
  deComponentTree: [] as Recordable[],
  roleList: [] as Recordable[],
  userResignList: [] as Recordable[],
  userList: [] as Recordable[]
})

async function init (propsValue: BasicProps, fn?:Fn) {
  orginPropsData.value = cloneDeep(propsValue)
  configuration.value = propsValue.configuration || false
  dynamicParameters.value = propsValue.dynamicParameters
  isNoIncludeChild.value = propsValue.isIncludeChild || false
  allChecked.value = propsValue.selectedShow || false
  onlyDep.value = propsValue.onlyDep || false
  activeName.value = onlyDep.value ? 'organizational' : 'members'
  // 获取数据
  const { data, api } = unref(orginPropsData.value)
  if(!api && data) {
    const { deComponentTreeVO, roleVOList, userResignVOList, userVOList } = data
    queryData.deComponentTree = deComponentTreeVO || []
    queryData.roleList = roleVOList || []
    queryData.userResignList = userResignVOList || []
    queryData.userList = userVOList || []
    dealDefault()
  } else if(api && isFunc(api)){
    try {
      loading.value = true
      const res = await api({ checkChildFlag: true, ...propsValue.paramsData, disableUserIds: propsValue.showValueData && propsValue.showValueData?.map(item => item.id) || [] })
      queryData.deComponentTree = res.deComponentTreeVO
      queryData.roleList = res.roleVOList
      queryData.userResignList = res.userResignVOList
      queryData.userList = res.userVOList
      dealDefault()
      fn && fn()
    }catch (err) {
      console.log(err)
    } finally {
      loading.value = false
    }
  }
}

// 处理回显
function dealDefault (){
  nextTick(() => {
    const { defaultValueData } = unref(orginPropsData.value)
    organizationalRef.value?.setDefaultData(defaultValueData?.filter(el => el.type === DataType.DEP ) || [])
    roleRef.value?.setDefaultData(defaultValueData?.filter(el => el.type === DataType.ROLE ) || [])
    membersRef.value?.setDefaultData(defaultValueData?.filter(el => el.type === DataType.USER ) || [])
    nextTick(() => {
      confirm(true)
    })
  })
}

function close (){
  emit('close')
}

function confirm (noClose = false){
  let cloneData = cloneDeep(checkedData)
  let checkedList:Recordable[] = []
  checkedData.userList?.forEach(el => {
    checkedList.push({ ...el, type: DataType.USER })
  })
  checkedData.roleList?.forEach(el => {
    checkedList.push({ ...el, type: DataType.ROLE })
  })
  checkedData.deComponentTree?.forEach(el => {
    checkedList.push({ ...el, type: DataType.DEP })
  })
  emit('confirm', { data: cloneData,
    list: checkedList,
    dynamicParameters: unref(dynamicParameters),
    selectedShow: unref(allChecked),
    containChildren: unref(isNoIncludeChild)
  }, noClose)
}

function updateValue (data: Recordable[], target: 'deComponentTree'|'roleList'|'userResignList'|'userList' ){
  checkedData[target] = data
}

function handleRemove (item: Recordable, target: 'deComponentTree'|'roleList'|'userResignList'|'userList'){
  console.log(target, item, '移除')
  switch (target){
    case 'deComponentTree':
      organizationalRef.value.unChecked(item.value)
      break
    case 'roleList':
      roleRef.value.unChecked(item.value)
      break
    case 'userList':
      membersRef.value.unChecked(item.value)
      break
    case 'userResignList':
      break
  }
}

function remove (value: string, type: string){
  const typeMap:any = {
    DEPART: 'deComponentTree',
    ROLE: 'roleList',
    USER: 'userList'
  }
  handleRemove({ value }, typeMap[type])
  confirm()
}

const panelActionType: Recordable = {
  init,
  updateValue,
  remove
}
emit('registerPanel', panelActionType)

</script>

<style lang="scss" scoped>
.personnelSelectionPanel {
  max-height: 70vh;
  overflow-y: hidden;
  .top {
    @include flex-center(row, space-between, center);
    padding: 17px 24px;
    border-bottom: 1px solid #EBEEF5;
    .title {
      font-weight: 700;
      color: $gray;
      font-size: $saas-font-size-small;
    }
    .icon-close {
      cursor: pointer;
    }
  }
  .checked-content{
    margin: 10px;
    border-radius: 4px;
    height: 105px;
    border: 1px dashed #DCDFE6;
    padding: 8px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    overflow-y: auto;
    .checked-item {
      border-radius: 4px;
      background: #F4F4F5;
      padding: 5px 8px 5px 12px;
      display: flex;
      flex-shrink: 0;
      align-items: center;
      margin-right: 5px;
      margin-bottom: 5px;
      color: #666666;
      font-size: 14px;
      height: 30px;
      span{
        flex-shrink: 0;
      }
    }
  }
  .content {
    max-height: calc(50vh - 120px);
    height: 400px;
    overflow-y: auto;
    // @include flex-center(row, normal, normal);
    border-bottom: 1px solid #EBEEF5;
    padding: 0 16px;
    .personnelSelectionPanelTabs {
      width: 100%;
      height: 100%;
      --el-tabs-header-height: 48px !important;
      :deep(.el-tabs__header) {
        margin: 0;
      }
      :deep(.el-tabs__content) {
        height: calc(100% - 48px);
      }
      :deep(.el-tabs__nav) {
        transform: translateX(16px) !important;
      }
      :deep(.el-tab-pane) {
        height: 100%;
      }
      :deep(.el-tabs__active-bar) {
        background-color: $primary-color;
      }
      :deep(.el-tabs__item.is-active) {
        color: $primary-color;
        font-weight: 700;
      }
      :deep(.el-tabs__item) {
        color: $secondary-text-color;
      }
      :deep(.el-tabs__nav-wrap::after) {
        height: 1px;
        background-color: #EBEEF5;
      }
    }
    .left {
      width: 250px;
      border-right: 1px solid #EBEEF5;
      overflow-y: auto;
    }
  }
  .bottom {
    padding: 16px 24px;
    @include flex-center(row, space-between, center);
    .left {
      :deep(.el-checkbox) {
        margin-right: 16px;
      }
    }
  }
}
</style>
