import { ref, type Ref } from 'vue'
import { base64ToBlob_NotBase64Str, base64ToBlob } from '@/utils'

export function useStreamDownload (): { downloadFn: Fn; percentage: Ref } {
  // 进度百分比
  const percentage = ref(0)

  /**
   * @description: 数据流下载(Base64转Blob)
   * @param streamUrl 数据流地址
   * @param fileName 下载文件名
   * @param strIncludesBase64 base64字符中是否包含“;base64,”字符
   */
  async function downloadFn (streamUrl: string, fileName: string, strIncludesBase64 = false) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      try {
        const utf8decoder = new TextDecoder()
        let base64Str = ''

        const stream = await fetch(streamUrl)
        const streamTotalLth = Number(stream.headers.get('Content-Length'))
        const reader = stream.body?.getReader()

        // eslint-disable-next-line no-constant-condition
        while (true) {
          const res = await reader?.read()
          base64Str += utf8decoder.decode(res?.value)

          percentage.value = (base64Str.length / streamTotalLth) * 100
          if (res?.done) break
        }

        // base64转为blob下载
        const blob = strIncludesBase64
          ? base64ToBlob(base64Str)
          : base64ToBlob_NotBase64Str(base64Str)
        const videoUrl = URL.createObjectURL(blob)

        const a = document.createElement('a')
        document.body.appendChild(a)
        a.href = videoUrl
        a.download = fileName
        a.click()
        window.URL.revokeObjectURL(videoUrl)
        document.body.removeChild(a)

        resolve(true)
      } catch (error) {
        reject(error)
      } finally {
        percentage.value = 0
      }
    })
  }

  return { downloadFn, percentage }
}
