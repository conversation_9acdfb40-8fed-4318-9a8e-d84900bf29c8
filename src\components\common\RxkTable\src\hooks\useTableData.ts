import { ref, watch, type ComputedRef, unref, onMounted, type Ref } from 'vue'
import type { BasicTableProps, PaginationProps } from '../type'
import { isFunc } from '@/utils/is'

interface ActionType {
  getPaginationInfo:ComputedRef<PaginationProps>;
}
export function useTableData (props: ComputedRef<BasicTableProps>, { getPaginationInfo }: ActionType){
  const loading = ref(false)
  // 表格数据
  const tableData = ref<Recordable[]>([])
  // 数据总数
  const total = ref(0)
  // 如果是传递的表格数据
  watch(() => unref(props).data, () => {
    const { data, api } = unref(props)
    !api && data && (tableData.value = data)
  }, {
    immediate: true
  })

  async function reload () {
    return await fetch()
  }

  onMounted(() => {
    console.log(unref(props).immediate, 'unref(props).immediate')
    unref(props).immediate && fetch().then()
  })
  async function fetch () {
    const {
      api,
      searchInfo,
      showPagination
    } = unref(props)
    console.log(props, 'props123')
    if (!api || !isFunc(api)) return
    try {
      const { pageSize, pageNum } = unref(getPaginationInfo) as PaginationProps
      // 请求参数
      const params: Recordable = {
        pageSize,
        pageNum,
        ...searchInfo
      }
      loading.value = true
      const res = await api(params)
      loading.value = false
      console.log(res)
      if(showPagination) {
        tableData.value = res.records || []
        total.value = res.total
      } else{
        tableData.value = res || []
      }

    }catch (err) {
      console.log(err)
      loading.value = false
    }

  }

  // function getTableData () {
  //
  // }
  return {
    tableData,
    fetch,
    reload,
    total,
    loading
  }
}