declare type Recordable<T = any> = Record<string, T>
declare interface ViteEnv {
  VITE_PORT: number;
  VITE_TITLE: string;
  VITE_DROP_CONSOLE: boolean;
  VITE_GLOB_APP_TITLE:string
  VITE_SAAS_ICON:string
}
// 一个函数类型
declare interface Fn<T = any, R = T> {
  (...arg: T[]): R;
}
declare interface PromiseFn<T = any, R = T> {
  (...arg: T[]): Promise<R>
}
// 对象类型
declare interface Obj {
  [propName: string]: any
}
declare let __BUILD_TIME__: string
declare let __GIT_HASH__: string

// label、value类型
declare interface LabelAndValueType {
  label: string;
  value: number;
}

type a = typeof setInterval

// 只读数组类型
type readOnly<T> = ReadonlyArray<T>

type Nullable<T> = T | null

type ValueOf<T> = T[keyof T]

declare module 'element-plus/dist/locale/zh-cn.mjs'

// 将部分属性变成可选
declare type MakeOptional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

declare type EmitType = (event: string, ...args: any[]) => void;