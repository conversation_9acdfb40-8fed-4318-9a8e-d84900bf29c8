<template>
  <el-form-item
    :label="schema.fieldName"
    :prop="schema.key"
    class="custom-search-item"
    :class="{ 'custom-search-item-dialog': isDialog}"
  >
    <div style="width: 100%">
      <RxkInput
        style="width: 100%"
        v-model.trim="getFormData[schema.key]"
        v-if="schema.component === 'Input'"
        v-bind="schema.componentProps"
      />
      <el-date-picker
        style="width: 100%"
        v-if="schema.component === 'DateRange'"
        v-model="getFormData[schema.key]"
        type="daterange"
        range-separator="-"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        v-bind="schema.componentProps"
      />
      <el-date-picker
        style="width: 100%"
        v-if="schema.component === 'Datetimerange'"
        v-model="getFormData[schema.key]"
        type="datetimerange"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="defaultTime"
        v-bind="schema.componentProps"
      />

      <el-select
        style="width: 100%"
        v-if="schema.component === 'Select'"
        v-model="getFormData[schema.key]"
        filterable
        clearable
        collapse-tags
        v-bind="schema.componentProps"
      >
        <el-option
          v-for="item in schema.options"
          :key="item.key"
          :value="item.value"
          :label="item.label"
        />
      </el-select>

      <el-select-v2
        v-if="schema.component === 'Select-v2'"
        style="width: 100%"
        v-model="getFormData[schema.key]"
        filterable
        :options="schema.options"
        v-bind="schema.componentProps"
        placeholder="请选择"
      />

      <FormItemRender
        v-model="getFormData[schema.key]"
        v-if="schema.component === 'Render'"
        :renderFn="schema.render"
      />
      <el-cascader
        v-if="schema.component === 'cascader'"
        v-model="getFormData[schema.key]"
        v-bind="schema.componentProps"
      />
      <RxkRadio
        v-bind="schema.componentProps"
        v-model="getFormData[schema.key]"
        v-if="schema.component === 'Radio'"
        :list="schema.options"
      />
      <RxkCheckboxGroup
        v-model="getFormData[schema.key]"
        v-if="schema.component === 'RxkCheckboxGroup'"
        :list="schema.options"
      />
    </div>
    <template v-if="schema.renderLabel" #label>
      <FormItemRender :renderFn="schema.renderLabel" />
    </template>
  </el-form-item>
</template>
<script lang="tsx" setup>
import { RxkInput } from '@/components/common/RxkInput'
import { RxkRadio } from '@/components/common/RxkRadio'
import { RxkCheckboxGroup } from '@/components/common/RxkCheckbox'
import FormItemRender from './formItemRender.vue'
import type { PropType } from 'vue'
import type { FormSchema } from '../type'
import { computed, onBeforeMount } from 'vue'
import { isArray } from '@/utils/is'
const props = defineProps({
  schema: {
    type: Object as PropType<FormSchema>,
    default: () => {}
  },
  formData: {
    type: Object,
    default: () => {}
  },
  isDialog: {
    type: Boolean,
    default: false
  }
})
const getFormData = computed(() => {
  return props.formData
})

const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59)
]

onBeforeMount(() => {
  if ((props.schema.component === 'Select' || props.schema.component === 'Select-v2') && props.schema.getSelectApi) {
    props.schema.getSelectApi().then((res: any) => {
      let data = res.records ? res.records : res
      if (isArray(data)) {
        data.forEach((e: any) => {
          props.schema.options?.push({
            label: e[props.schema.originalfieldName.label],
            value: e[props.schema.originalfieldName.value]
          })
        })
        console.log()
      } else {
        // map
        let values = Object.values(data)
        let keys = Object.keys(data)
        values.forEach((e, i) => {
          props.schema.options?.push({
            label: e,
            value: keys[i]
          })
        })
        console.log(props.schema.options)
      }
    })
  }
})
</script>
<style scoped lang="scss">
.custom-search-item {
  width: 25%;
  margin-right: 0;
  margin-bottom: 12px;
  padding-right: 24px;
}

.custom-search-item-dialog {
  width: 50%;
}
</style>
