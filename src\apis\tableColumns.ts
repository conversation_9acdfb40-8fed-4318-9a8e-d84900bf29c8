import { ContentTypeEnum } from '@/enums/axios'
import request from '@/utils/axios'

const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

// 获取列表字段分组list
export const getFetchColumnGroupList = (data?: any) => request.get('/admin/field_group/list', data, jsonHeaderConfig, { ignoreCancelToken: true })

// 删除当前分组
export const fetchDeleteColumnGroup = (id: string) => request.get(`/admin/field_group/delete/${id}`, {})

// 拖拽当前分组
export const fetchDragColumnGroup = (data?: any) => request.post('/admin/field_group/drag', data, jsonHeaderConfig)

// 切换选中分组
export const fetchUpdateSelectedGroup = (id: string) => request.get(`/admin/field_group/updateGroup/${id}`, {})