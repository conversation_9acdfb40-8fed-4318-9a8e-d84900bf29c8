<template>
  <DepartmentMultipleSelect 
    :paramsData="commonParams"
    :api="getPageComponentData"
    :showValueData="showValueData"
    :disabled="isTxt || renderConfig.disabled"
    @sure="getDep"
    @getDeptList="getDeptList"
    style="width: 100%"/>
</template>

<script lang="ts" setup>
import { useNavHistory } from '@/stores/modules/routerNav'
import { recursion } from '@/utils/tools'
import { computed, ref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { getPageComponentData } from '@/apis/customerManage'
import { isArray, isNullOrUndefOrEmpty } from '@/utils/is'

const emit = defineEmits(['update:modelValue', 'refreshDataList', 'updadeViewData'])
const props = defineProps({
  ...basicProps
})
const navHistory = useNavHistory()
console.log(navHistory, 'navHistory')
const systemCode = navHistory.activeSysCode
const load = ref<boolean>(true)

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})

const showValueData = computed(() => {
  let value: Recordable[] = []
  if(innerValue.value) {
    if(!isArray(innerValue.value)) {
      value = innerValue.value.split(',').map((item: Recordable) => ({ id: item }))
    } else {
      value = innerValue.value?.map((item: Recordable) => ({ id: item }))
    }
  }
  return value
})

const commonParams = {
  columnId: props.data.id,
  columnUseScene: props.data.columnUseScene,
  tableCode: props.data.tableCode,
  extraAgreements: {
    systemCode: systemCode,
    containChildren: 1,
    useSceneType: props.data.useSceneType,
    disableDeptIds: !isArray(innerValue.value) ? innerValue.value.split(',') : innerValue.value
  },
  tenantId: props.data.tenantId || 0
}

function getDep (data:Recordable) {
  console.log(data, '部门多选')
  let depIds:Recordable[] = []
  let depList :Recordable[] = []
  if(data.containChildren) {
    // 不包含
    depIds = data.departmentList.map((item: Recordable) => item.id) || []
    depList = data.departmentList
  } else {
    recursion(data.departmentList, item => {
      depIds.push(item.id)
      depList.push(item)
    })
  }
  emit('refreshDataList', data.departmentList)
  innerValue.value = depIds
}

function getDeptList (data:Recordable){
  if(!isNullOrUndefOrEmpty(data)) {
    emit('refreshDataList', data)
    emit('updadeViewData', innerValue.value)
  }
}

</script>

<style lang="scss" scoped>
</style>