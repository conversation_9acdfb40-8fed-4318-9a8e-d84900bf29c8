import { usePermissionStore } from '@/stores/modules/permission'
import { router } from '@/router'
import { computed, unref } from 'vue'
import { filterCurrentRoute } from '@/utils/util'
import { recursion } from '@/utils/tools'

export function useResource () {
  const { menuList } = usePermissionStore()
  const currentRoutePath = computed(() => {
    return unref(router.currentRoute).fullPath
  })
  const currentMenu: any = computed(() => {
    return filterCurrentRoute(unref(menuList) as any, unref(currentRoutePath))
  })

  const resourceList: any = computed(() => {
    return unref(currentMenu).resourceList || []
  })

  function getAuth (flag: string) {
    // resourceList为空就都返回false
    if (unref(resourceList).length) {
      const res = unref(resourceList).find((el: any) => el.uri === flag || el.code === flag)
      return !!res
    }
    return false
  }

  /**
 * 获取menuId
 * @param params 不传返回当前菜单的id，传code或path返回对应的menuId
 */
  function getMenuId (params:{code?: string, path?: string}|undefined = undefined){
    if(!params) return unref(currentMenu).id
    let targetMenu: Recordable = {}
    recursion(menuList, (menu) => {
      if (params.code && menu.code === params.code) targetMenu = { ...menu }
      if(params.path && menu.path === params.path) targetMenu = { ...menu }
    })
    return targetMenu?.id
  }

  // 判断是否有菜单权限
  function getMenuAuth (code: string) {
    let auth = false
    recursion(unref(menuList), menu => {
      if (menu.code === code) auth = true
    })
    return auth
  }
  // 通过菜单Code获取按钮权限 eg:tab页的menuCode + 按钮code 获取按钮权限
  function getAuthByMenuCode (menuCode: string, code: string) {
    let currentMenu: Recordable = {}
    recursion(menuList, menu => {
      if (menu.code === menuCode) currentMenu = { ...menu }
    })
    if (currentMenu?.resourceList?.length) {
      const res = unref(currentMenu?.resourceList).find(
        (el: any) => el.code === code
      )
      return !!res
    }
  }
  /**
   * 获取菜单的appCode
   * @param params 不传返回当前菜单的id，传code或path返回对应的menuId
   */
  function getMenuAppCode (params:{code?: string, path?: string, name?: string}|undefined = undefined){
    if(!params) return unref(currentMenu).id
    let targetMenu: Recordable = {}
    recursion(menuList, (menu) => {
      if (params.code && menu.code === params.code) targetMenu = { ...menu }
      if(params.path && menu.path === params.path) targetMenu = { ...menu }
      if(params.name && menu.name === params.name) targetMenu = { ...menu }
    })
    return targetMenu?.id
  }

  return {
    resourceList,
    getAuth,
    getMenuId,
    getMenuAuth,
    getAuthByMenuCode,
    getMenuAppCode
  }
}