import { ref } from 'vue'
import type { ColumnType } from '@/types/table'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { channelTypeOption, settlementWayOption } from '@/enums/carlife'
import timeUtils from '@/utils/libs/time'
import SelectAllV2 from '@/components/business/selectAllV2/index.vue'
import { fetchGetCarChannelList } from '@/apis/carlife'

const channelCommonFiled:ColumnType[] = [
  { key: 'channelId', title: '渠道ID' },
  { key: 'settleType', title: '结算方式', desc: '', width: 120, render ({ cellData }){
    return <>
      <span>{settlementWayOption.find((item) => item.value === cellData.settleType)?.label || '-'}</span>
    </>
  } },
  { key: 'settleNumber', title: '结算数', desc: '', width: 120 },
  { key: 'settleValence', title: '结算价格（元/%）', desc: '', width: 150 },
  { key: 'consumeAmount', title: '渠道消耗', desc: '', width: 120 },

  { key: 'linkVisitPv,linkVisitUv', title: '访问PV|UV', desc: '进入h5页面的次数| 进入h5页面的用户数（未登录时按ip去重，有登录按用户id去重）', width: 100, render ({ cellData }){
    return <>
      <span>{`${cellData.linkVisitPv} | ${cellData.linkVisitUv}`}</span>
    </>
  } },
  { key: 'loginAgreementConfirmUv', title: '手机号留资数', desc: '使用token登录成功的用户数，包含api，撞库联登的动态链接的登录和手机号输入token60天缓存的', width: 120 },
  { key: 'sendCodePv,sendCodeUv', title: '发送验证码pv|uv', desc: '登录页点击【获取验证码】按钮的次数|用户数（ip去重）', width: 150, render ({ cellData }){
    return <>
      <span>{`${cellData.sendCodePv} | ${cellData.sendCodeUv}`}</span>
    </>
  } },
  { key: 'h5LoginPv,h5LoginAll', title: '登录pv|uv', desc: '登录成功的总用户数|新用户数', width: 150, render ({ cellData }){
    return <>
      <span>{`${cellData.h5LoginPv} | ${cellData.h5LoginAll}`}</span>
    </>
  } },
  { key: 'h5LoginRate', title: 'H5登录率', desc: '', width: 120 },
  { key: 'downloadPopupVisitUv', title: '下载弹框访问数', desc: '', width: 120 },
  { key: 'operateAppDownloadUv', title: 'APP下载数', desc: '', width: 120 },
  { key: 'downloadRate', title: 'H5下载率', desc: '', width: 120 },
  { key: 'appLoginAll,appLoginNewUv,appLoginOldUv', title: 'APP登录总|新|老', desc: '', width: 150, render ({ cellData }){
    return <>
      <span>{`${cellData.appLoginAll } | ${cellData.appLoginNewUv} | ${cellData.appLoginOldUv}`}</span>
    </>
  } },
  { key: 'appLoginRate', title: 'APP登录率', desc: '', width: 120 },
  { key: 'loginFirstVisitUv', title: 'APP首页访问次数', desc: '', width: 150 },
  { key: 'appCapitalApplyUv,appCapitalApplyNewUv', title: 'APP留资数总|新', desc: '', width: 150, render ({ cellData }){
    return <>
      <span>{`${cellData.appCapitalApplyUv } | ${cellData.appCapitalApplyNewUv}`}</span>
    </>
  } },
  { key: 'appCapitalRate', title: 'APP留资率', desc: '', width: 150 },
  { key: 'appFirstFilterUv', title: 'APP初筛通过用户数', desc: '', width: 150 },
  { key: 'appFirstFilterRate', title: 'APP初筛通过率', desc: '', width: 120 },
  { key: 'appPreAuditApplyPv', title: 'APP预审申请数', desc: '', width: 120 },
  { key: 'appPreAuditApplyUv', title: 'APP预审申请用户数', desc: '', width: 150 },
  { key: 'appPreAuditApplyRate', title: 'APP预审申请率', desc: '', width: 120 },
  { key: 'appPhoneAuditPv', title: 'APP电核数', desc: '', width: 120 },
  { key: 'appPhoneAuditUv', title: 'APP电核用户数', desc: '', width: 120 },
  { key: 'appPhoneAuditPassRate', title: 'APP电核通过率', desc: '', width: 120 },
  { key: 'appCreditPv', title: 'APP授信数', desc: '', width: 120 },
  { key: 'appCreditUv', title: 'APP授信用户数', desc: '', width: 120 },
  { key: 'appCreditPassRate', title: 'APP授信通过率', desc: '', width: 120 },
  { key: 'appLoanPv', title: 'APP放款数', desc: '', width: 120 },
  { key: 'appLoanUv', title: 'APP放款用户数', desc: '', width: 120 },
  { key: 'appLoanPassRate', title: 'APP放款通过率', desc: '', width: 120 },
  { key: 'clueTransRate', title: '线索转化率', desc: '', width: 120 },
  {
    title: 'APP初筛通过数|APP初筛拒绝数',
    key: 'appFirstScreenPassPv,appFirstScreenRefusePv',
    width: 260,
    render ({ cellData }){
      return <>
        <span>{`${cellData.appFirstScreenPassPv} | ${cellData.appFirstScreenRefusePv}`}</span>
      </>
    }
  },
  {
    title: 'APP初筛通过用户数|APP初筛拒绝用户数',
    key: 'appFirstScreenPassUv,appFirstScreenRefuseUv',
    width: 280,
    render ({ cellData }){
      return <>
        <span>{`${cellData.appFirstScreenPassUv} | ${cellData.appFirstScreenRefuseUv}`}</span>
      </>
    }
  },
  {
    title: 'APP申请预审链接数|APP获取预审链接数',
    key: 'appApplyPreAuditLinkPv,appFirstFilterPv',
    width: 280,
    render ({ cellData }){
      return <>
        <span>{`${cellData.appApplyPreAuditLinkPv} | ${cellData.appFirstFilterPv}`}</span>
      </>
    }
  },
  {
    title: 'APP申请预审链接用户数|APP获取预审链接用户数',
    key: 'appApplyPreAuditLinkUv,appFirstFilterUv',
    width: 340,
    render ({ cellData }){
      return <>
        <span>{`${cellData.appApplyPreAuditLinkUv} | ${cellData.appFirstFilterUv}`}</span>
      </>
    }
  },
  {
    key: 'guideVisitPv,guideVisitUv',
    title: 'guide页访问pv/uv',
    desc: '',
    width: 100,
    render({ cellData }) {
      return (
        <>
          <span>{`${cellData.guideVisitPv} | ${cellData.guideVisitUv}`}</span>
        </>
      )
    }
  },
  {
    key: 'guideClickPv,guideClickUv',
    title: 'guide页点击pv/uv',
    desc: '',
    width: 100,
    render({ cellData }) {
      return (
        <>
          <span>{`${cellData.guideClickPv} | ${cellData.guideClickUv}`}</span>
        </>
      )
    }
  },
  { key: 'guideCtr', title: '曝光点击率（%）', desc: '', width: 120 },
  { key: 'productRequestQuantity', title: '三方请求量', desc: '', width: 150 },
  {
    key: 'productRequestSuccessQuantity',
    title: '请求三方成功量',
    desc: '',
    width: 150
  },
  { key: 'successRequestRate', title: '三方请求成功率', desc: '', width: 150 }
]

const channelNameList = ref<Recordable[]>([])
fetchGetCarChannelList({
  model: {},
  pageSize: 9999,
  pageNum: 1
}).then((res:any) => {
  channelNameList.value = res.records?.map((item:any) => {
    return {
      label: item.name,
      value: item.id
    }
  }) || []
})

/** 按渠道统计 */
export const channelStatistics = {
  searchFormData: ref<FormSchema[]>([
    {
      fieldName: '渠道ID',
      component: 'Input',
      key: 'channelId',
      val: ''
    },
    {
      fieldName: '渠道名称',
      key: 'channelIdList',
      val: [],
      options: channelTypeOption,
      componentProps: {
        clearable: true,
        multiple: true
      },
      component: 'Render',
      render: (modelValue:string) => {
        return <SelectAllV2 v-model={modelValue} list={channelNameList.value}></SelectAllV2>
      }
    },
    {
      fieldName: '渠道类型',
      component: 'Select',
      key: 'channelTypeList',
      val: [],
      options: channelTypeOption,
      componentProps: {
        clearable: true,
        multiple: true
      }
    }, 
    {
      key: 'timeRange',
      fieldName: '日期范围',
      component: 'DateRange',
      val: timeUtils.transTypeToTime('今日', 'yyyy-MM-dd')
    },
    {
      fieldName: '结算方式',
      component: 'Select',
      key: 'channelSettleTypeList',
      val: [],
      options: settlementWayOption,
      componentProps: {
        clearable: true,
        multiple: true
      }
    }
  ]),
  columns: ref<ColumnType[]>([
    { key: 'channelName', title: '渠道名称', fixed: 'left', width: 150 },
    ...channelCommonFiled,
    {
      key: 'operate',
      title: '操作',
      fixed: 'right',
      width: 100,
      slot: 'operateSlot'
    }
  ])
}
/** 按单日明细统计 */
export const channelDayStatistics = {
  searchFormData: ref<FormSchema[]>([
    {
      key: 'timeRange',
      fieldName: '日期范围',
      component: 'DateRange',
      val: timeUtils.transTypeToTime(60, 'yyyy-MM-dd')
    }
  ]),
  columns: ref<ColumnType[]>([
    { key: 'day', title: '日期', fixed: 'left', width: 150 },
    { key: 'channelName', title: '渠道名称', width: 150 },
    ...channelCommonFiled
  ])
}
/** 按日期统计 */
export const channelDateStatistics = {
  searchFormData: ref<FormSchema[]>([
    {
      key: 'timeRange',
      fieldName: '日期范围',
      component: 'DateRange',
      val: timeUtils.transTypeToTime(20, 'yyyy-MM-dd')
    },
    {
      fieldName: '渠道名称',
      key: 'channelIdList',
      val: '',
      options: channelTypeOption,
      componentProps: {
        clearable: true,
        multiple: true
      },
      component: 'Render',
      render: (modelValue:string) => {
        return <SelectAllV2 v-model={modelValue} list={channelNameList.value}></SelectAllV2>
      }
    }
  ]),
  columns: ref<ColumnType[]>([
    { key: 'day', title: '日期', fixed: 'left', width: 150 },
    { key: 'channelName', title: '渠道名称', fixed: 'left', width: 150 },
    ...channelCommonFiled
  ])
}
