<template>
  <RxkDrawer
    v-model="state.drawer"
    size="1200"
    :title="title"
    @close="handleClose"
    @confirm="handleSubmit"
  >
    <template v-slot:content>
      <div class="tw-p-[24px]">
        <el-form
          ref="ruleFormRef"
          :model="formData"
          :rules="commonDrawerRules"
          label-position="top"
        >
          <el-row :gutter="24">
            <el-col :span="24">
              <div class="title">基础信息</div>
            </el-col>
            <el-col :span="rowSpan">
              <el-form-item label="渠道名称" prop="name">
                <el-input
                  v-model="formData.name"
                  maxlength="50"
                  placeholder="请输入"
                  show-word-limit
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="rowSpan">
              <el-form-item label="渠道类型" prop="type">
                <el-select
                  clearable
                  style="width: 100%"
                  v-model="formData.type"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in channelTypeOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="rowSpan">
              <el-form-item label="H5类型" prop="h5Type">
                <el-select
                  clearable
                  style="width: 100%"
                  v-model="formData.h5Type"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in h5TypeOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="rowSpan">
              <el-form-item label="渠道公司主体" prop="otherSubject">
                <el-input
                  v-model="formData.otherSubject"
                  maxlength="50"
                  placeholder="请输入"
                  show-word-limit
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="rowSpan">
              <el-form-item label="域名主体" prop="domainSubject">
                <el-select
                  clearable
                  style="width: 100%"
                  v-model="formData.domainSubject"
                  placeholder="请选择"
                ><el-option
                  v-for="item in doMainOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="rowSpan">
              <el-form-item label="渠道对接人" prop="docker">
                <RemoteSelect
                  :multiple="false"
                  :api="asyncGetUserAllList"
                  v-model="formData.docker"
                />
              </el-form-item>
            </el-col>
            <el-col :span="rowSpan">
              <el-form-item label="状态" prop="status">
                <el-select
                  clearable
                  style="width: 100%"
                  v-model="formData.status"
                  placeholder="请选择"
                  :list="statusOption"
                >
                  <el-option
                    v-for="item in statusOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <div class="title">结算配置</div>
            </el-col>
            <el-col :span="rowSpan">
              <el-form-item label="结算类型" prop="settleType">
                <el-select
                  clearable
                  style="width: 100%"
                  v-model="formData.settleType"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in settlementWayOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :span="rowSpan"
              v-if="[1, 3].includes(Number(formData.settleType))"
            >
              <el-form-item label="结算单价" prop="settlePrice">
                <el-input
                  v-model="formData.settlePrice"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="rowSpan" v-if="Number(formData.settleType) === 2">
              <el-form-item label="结算比例（%）" prop="creditProfitRatio">
                <el-input
                  v-model="formData.creditProfitRatio"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24" v-if="[1].includes(Number(formData.settleType))">
            <el-col :span="24">
              <div class="title">前筛配置</div>
            </el-col>
            <el-col :span="rowSpan">
              <el-form-item label="结算系数（%）" prop="settleFactor">
                <el-input
                  v-model="formData.settleFactor"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
  </RxkDrawer>
</template>
<script setup lang="ts">
import type { Operation } from '../type'
import { RxkDrawer } from '@/components/common/RxkDrawer'
import { RemoteSelect } from '@/components/business/remoteSelect'
import { getUserList } from '@/apis/userManage'
import { useRules } from '../hooks/useRules'
import { reactive, defineExpose, computed, ref, watch } from 'vue'
import { useConfig } from '@/stores/modules/config'
import {
  doMainOption,
  settlementWayOption,
  statusOption,
  channelTypeOption
} from '@/enums/carlife'
import {
  fetchAddOrUpdateCarChannelList,
  fetchChannelViewDetail
} from '@/apis/carlife'
import { useRequest } from '@/hooks/useRequest'
import { dataDetailListFormat } from '@/apis/system'

const emit = defineEmits(['refreshTable'])

const { data: h5TypeOption } = useRequest(() =>
  dataDetailListFormat({ groupCode: 'channel_h5_type', numberValue: true })
)

const config = useConfig()
const rowSpan = config.layout.shrink ? 12 : 8
const state = reactive({
  drawer: false,
  isAdd: true,
  enumCompanySubjectUs: [],
  enumCompanySubjectOther: []
})
const asyncGetUserAllList = async () => {
  const res = await getUserList({
    model: { state: 'ENABLED' },
    pageSize: 5000,
    pageNum: 1
  })
  return new Promise((resolve) => {
    resolve(
      res.records.map((item) => {
        return {
          label: item.realName,
          value: item.id
        }
      })
    )
  })
}
const getBaseData = () => {
  return {
    id: '',
    docker: '',
    name: '',
    type: '',
    settlePrice: '',
    creditProfitRatio: '',
    settleFactor: '',
    otherSubject: '',
    settleType: '',
    status: '',
    domainSubject: '',
    h5Type: ''
  }
}

let formData = reactive(getBaseData())

// 校验规则
const { commonDrawerRules } = useRules(formData)
const ruleFormRef = ref()
const title = computed(() => (state.isAdd ? '添加渠道' : '渠道详情'))

watch(
  () => formData.type,
  (val, preVal) => {
    // 如果preVal有值，则清空当前结算方式 ； 因为枚举发生了改变
    if (preVal) {
      formData.settleType = ''
    }
  }
)

// 获取详情
const getDetails = async (id: any) => {
  const res = await fetchChannelViewDetail({ id })
  Object.keys(formData).forEach((key: string) => {
    formData[key] = res[key]
  })
}

const init = async (data: Operation) => {
  if (!data.isAdd) {
    await getDetails(data.id)
  } else {
    const defaultFormData = getBaseData()
    Object.keys(formData).forEach((key: string) => {
      formData[key] = defaultFormData[key]
    })
  }
  state.drawer = true
  state.isAdd = data.isAdd
}

const handleClose = (done: () => void) => {
  state.drawer = false
  formData.type = ''
  done()
}
const handleSubmit = (done: () => void) => {
  // 校验
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      fetchAddOrUpdateCarChannelList(formData)
      .then(() => {
        state.drawer = false
        formData.type = ''
        // 刷新列表
        emit('refreshTable')
        done()
      })
      .finally(() => {
        done()
      })
    } else {
      done()
    }
  })
}
defineExpose({
  init
})
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  margin-bottom: 16px;
}
:global(.el-tree-node__content .el-select-dropdown__item) {
  padding: 0;
  background: none;
  &:hover {
    background: none !important;
  }
}
:deep(.el-select-v2) {
  width: 100%;
}
.interval {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #606266;
}
.title {
  color: #333333;
  margin-bottom: 21px;
  font-size: 14px;
  font-weight: 700;
  position: relative;
  padding-left: 6px;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    width: 3px;
    height: 14px;
    border-radius: 3px;
    background: #5687ff;
  }
  .btn-text {
    color: #5687ff;
    font-family: 'Microsoft YaHei';
    font-size: 12px;
    line-height: 20px;
  }
}
.time-limit-item {
  :deep(.el-form-item__content) {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    > div {
      flex-shrink: 0;
    }
    .time-limit-value {
      flex: 1;
      margin-left: 20px;
      .el-input-group__append {
        padding: 0 10px;
      }
    }
  }
}
</style>
