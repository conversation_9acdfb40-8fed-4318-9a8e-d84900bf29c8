/**
 * 页面缓存方法
 * @Description: 生命周期： 随用随生效，关闭浏览器标签或窗口清除，退出登录清除
 * @Description: 使用规则： 使用getCache updateCache根据菜单id，菜单code进行页面缓存/读取，也可传入custom自定义参数进行数据缓存
 * @Description: 已预处理的缓存：自定义应用，使用到table和SearchFilter的固定性应用
 * @Description: searchFilter(固定性应用的筛选组件) RxkVTable(通用表格组件) PageContentNav(常用筛选) QuickSearch(快捷筛选) AdvancedFilter(高级筛选)
 * @Description: 缓存处理方式： 查询与筛选缓存分开储存，RxkVTable储存searchInfo,再次进入页面时直接用searchInfo请求接口，筛选组件在初始化时赋值缓存字段值(onMounted, 接口回调等)
 * @Description: 其他缓存处理可根据实际业务自定义处理方式
 * <AUTHOR>
 * @date 2024年3月4日 09点37分
 */
import { ref, unref } from 'vue'
import { usePermissionStore } from '@/stores/modules/permission'
import { deepMergeReplace, recursion } from '@/utils/tools'
import { getCache as getSession, setCache as setSession, removeCache as removeSession } from '@/utils/cache/session'

type CACHE_TYPE = 'session'|'normal'

const Page_Cache_Key = 'MENU_'

const Custom_Cache_Keys = 'CUSTOM_CACHE_KEYS'

const CacheType: CACHE_TYPE = 'normal'

const CacheSource:Recordable = ref({})

// 通过一下几种之一去存取缓存
interface CacheOption {
  menuCode?: string, // 菜单code
  menuId?: string, // 菜单id
  custom?: string // 自定义的缓存
}

// 缓存数据的基本结构 可在此基础上自定义拓展
interface CacheData {
  [key: string]: any,
  table?: { // 表格
    [key: string]: any,
    pageSize?: number,
    pageNum?: number,
    scrollX?: number | string | undefined,
    scrollY?: number | string | undefined,
    selected?: Array<number|string>,
    searchInfo?: Recordable | undefined,
    sectionList?: Recordable[]
  },
  baseSearch?: { // 基本筛选
    [key: string]: any,
  },
  quickSearch?: { // 快捷筛选
    [key: string]: any,
  },
  advancedFilter?: Recordable[],
  usualFilter?: { // 常用筛选
    [key: string]: any,
  }
}

export function usePageCache (){
  const permissionStore = usePermissionStore()

  // 通过菜单id获取缓存
  function getCacheByMenuId (menuId: string){
    return get(Page_Cache_Key + menuId)
  }
  // 通过菜单code获取缓存
  function getMenuIdByCode (code:string){
    let currentMenu:Recordable = {}
    recursion(permissionStore.menuList, (menu) => {
      if(menu.code === code) currentMenu = { ...menu }
    })
    return currentMenu.id
  }

  // 通过菜单id设置缓存
  function setCacheByMenuId (data: CacheData, menuId?: string){
    menuId && set(Page_Cache_Key + menuId, data)
  }

  // 通过菜单id更新缓存  传入的data字段值覆盖缓存已有的data
  function updateCacheByMenuId (data: CacheData, menuId?: string){
    if(!menuId) return
    const cache = getCacheByMenuId(menuId)
    // 缓存与传入值合并
    const newCache = deepMergeReplace(cache, data)
    console.log(cache, data, CacheSource.value, 'cachecache')
    setCacheByMenuId(newCache, menuId)
  }

  // 移除缓存
  function removeCacheByMenuId (menuId?: string){
    menuId && remove(Page_Cache_Key + menuId)
  }

  // 存储自定义cache的Key 用于删除
  function updateCustomCacheKeys (key: string){
    const keyStr = Page_Cache_Key + key
    let keys = []
    try {
      keys = JSON.parse(get(Custom_Cache_Keys))
    } catch(e){
      keys = []
    }
    if(!keys.includes(keyStr)){
      keys.push(keyStr)
      set(Custom_Cache_Keys, JSON.stringify(keys))
    }
  }

  /**
   * 获取缓存
   * @param option 不传就获取当前菜单缓存
   */
  function getCache (option:CacheOption | undefined = undefined):CacheData{
    if(option?.menuCode){
      return getCacheByMenuId(getMenuIdByCode(option.menuCode))
    } else if(option?.menuId){
      return getCacheByMenuId(option.menuId)
    } else if(option?.custom){
      return getCacheByMenuId(option.custom)
    } else {
      return getCacheByMenuId(permissionStore.currentMenu?.id)
    }
  }

  /**
   * 设置缓存
   * @param data 要设置的缓存对象
   * @param option  不传就设置当前菜单缓存
   */
  function setCache (data: CacheData, option:CacheOption | undefined = undefined){
    if(option?.menuId){
      setCacheByMenuId(data, option?.menuId)
    } else if (option?.menuCode){
      setCacheByMenuId(data, getMenuIdByCode(option?.menuCode))
    } else if(option?.custom){
      setCacheByMenuId(data, option.custom)
      updateCustomCacheKeys(option.custom)
    } else {
      setCacheByMenuId(data, permissionStore.currentMenu?.id)
    }
  }

  /**
   * 更新缓存
   * @param data 要更新的缓存属性
   * @param option  不传就设置当前菜单缓存
   */
  function updateCache (data: CacheData, option:CacheOption | undefined = undefined){
    if(option?.menuId){
      updateCacheByMenuId(data, option?.menuId)
    } else if (option?.menuCode){
      updateCacheByMenuId(data, getMenuIdByCode(option?.menuCode))
    } else if(option?.custom){
      return updateCacheByMenuId(data, option.custom)
    } else {
      updateCacheByMenuId(data, permissionStore.currentMenu?.id)
    }
  }

  /**
   * 移除缓存
   * @param option 不传就移除当前菜单缓存
   */
  function removeCache (option:CacheOption | undefined = undefined){
    if(option?.menuCode){
      return removeCacheByMenuId(getMenuIdByCode(option.menuCode))
    } else if(option?.menuId){
      return removeCacheByMenuId(option.menuId)
    } else if(option?.custom){
      return removeCacheByMenuId(option.custom)
    } else {
      return removeCacheByMenuId(permissionStore.currentMenu?.id)
    }
  }

  /**
   * 移除所有页面缓存
   */
  function removeAll (){
    recursion(permissionStore.menuList, (menu) => {
      remove(Page_Cache_Key + menu.id)
    })
    let keys = []
    try {
      keys = JSON.parse(get(Custom_Cache_Keys))
    } catch(e){
      keys = []
    }
    keys.forEach((key:string) => {
      remove(key)
    })
  }

  function get (key: string){
    if(CacheType === 'session'){
      let data = {}
      try {
        data = JSON.parse(getSession(key))
      } catch(e){
        // console.error(e)
        data = {}
      }
      return data
    } else {
      console.log(key, 'cachecache')
      return unref(CacheSource)[key] || {}
    }
  }

  function set (key: string, data: CacheData | string){
    if(CacheType === 'session'){
      setSession(key, JSON.stringify(data))
    } else {
      unref(CacheSource)[key] = data
    }
  }

  function remove (key: string){
    if(CacheType === 'session'){
      removeSession(key)
    } else {
      delete unref(CacheSource)[key]
    }
  }

  return {
    getCache,
    setCache,
    updateCache,
    removeCache,
    removeAll
  }
}