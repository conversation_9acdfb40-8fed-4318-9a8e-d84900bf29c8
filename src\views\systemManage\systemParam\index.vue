<template>
  <!-- 搜索表单 -->
  <SearchFilter @register="registerSetting" @search="search" />
  <div class="tw-ml-[16px] tw-mb-[16px]">
    <RxkButton @click="handleAdd" type="primary">新增</RxkButton>
  </div>
  <!-- 表格 -->
  <RxkVTable @register="registerTable">
    <template #operateSlot="{ row }">
      <div>
        <RxkButton class="table-action-btn"
                   text
                   @click="handleEdit(row)"
        >修改</RxkButton
        >
      </div>
    </template>
  </RxkVTable>
  <!-- 编辑对话框 -->
  <RxkDialog
    v-model="addDialogVisible"
    :title="isAdd ? '新增' : '编辑'"
    width="800px"
    @sure="addSure"
  >
    <div class="tw-m-[16px]">
      <el-form
        ref="formDataDom"
        label-width="auto"
        require-asterisk-position="right"
        :model="formData"
        :rules="addRules"
      >
        <el-form-item label="组名" prop="groupName">
          <RxkInput placeholder="请输入组名" v-model="formData.groupName" />
        </el-form-item>
        <el-form-item label="组编码" prop="groupCode">
          <RxkInput placeholder="请输入组编码" v-model="formData.groupCode" />
        </el-form-item>
        <el-form-item label="参数描述" prop="paramDesc">
          <RxkInput
            type="textarea"
            placeholder="请输入参数描述"
            :autosize="{ minRows: 8, maxRows: 10 }"
            v-model="formData.paramDesc"
          />
        </el-form-item>
        <el-form-item label="编码" prop="paramName">
          <RxkInput :disabled="!isAdd" placeholder="请输入编码" v-model="formData.paramName" />
        </el-form-item>
        <el-form-item label="参数值" prop="paramValue">
          <RxkInput
            type="textarea"
            :autosize="{ minRows: 8, maxRows: 10 }"
            placeholder="请输入参数值"
            v-model="formData.paramValue"
          />
        </el-form-item>
        <el-form-item label="展示端" prop="type">
          <RxkSelect
            placeholder="请输入展示端"
            v-model="formData.type"
            :list="sysParamsTypeList"
          />
        </el-form-item>
        <el-form-item label="修改人" prop="editablePerson">
          <RxkInput
            placeholder="请输入修改人"
            v-model="formData.editablePerson"
          />
        </el-form-item>
      </el-form>
    </div>
  </RxkDialog>
</template>

<script setup lang="tsx">
import { ref, unref } from 'vue'
import type { ColumnType } from '@/types/table'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { SearchFilter } from '@/components/business/searchFilter'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import {
  systemSelectSysParamList,
  systemInsertOrUpdate
} from '@/apis/system'
import { RxkDialog } from '@/components/common/RxkDialog'
import { RxkInput } from '@/components/common/RxkInput'
import { RxkSelect } from '@/components/common'
import { sysParamsTypeList } from '@/enums/settlemenManage'
import { cloneDeep } from '@/utils/tools'
defineOptions({
  name: 'SystemParam'
})

const formDataDom = ref()
const addDialogVisible = ref(false)
const isAdd = ref(false)

interface FormData {
  /** 主键 */
  id?: string
  /** 组名 */
  groupName: string
  /** 组编码 */
  groupCode: string
  /** 参数描述 */
  paramDesc: string
  /** 编码 */
  paramName: string
  /** 参数值 */
  paramValue: string
  /** 展示端 */
  type: string
  /** 修改人 */
  editablePerson?: string
}

const formDataInit = {
  groupName: '',
  groupCode: '',
  paramDesc: '',
  paramName: '',
  paramValue: '',
  type: '',
  editablePerson: ''
}

const formData = ref<FormData>(cloneDeep(formDataInit))

const addRules = ref({
  groupName: [{ required: true, message: '请输入组名', trigger: 'blur' }],
  groupCode: [{ required: true, message: '请输入组编码', trigger: 'blur' }],
  paramDesc: [{ required: true, message: '请输入参数描述', trigger: 'blur' }],
  paramName: [{ required: true, message: '请输入编码', trigger: 'blur' }],
  paramValue: [{ required: true, message: '请输入参数值', trigger: 'blur' }],
  type: [{ required: true, message: '请输入展示端', trigger: 'blur' }]
})

interface SearchCacheType {
  groupCode?: string
  groupName?: string
  paramDesc?: string
  paramName?: string
  editablePerson?: string
}

const searchCache: SearchCacheType = {}

const searchInfo:SearchCacheType = {
  groupCode: searchCache?.groupCode || undefined,
  groupName: searchCache?.groupName || undefined,
  paramDesc: searchCache?.paramDesc || undefined,
  paramName: searchCache?.paramName || undefined,
  editablePerson: searchCache?.editablePerson || undefined
}

function search (data: SearchCacheType) {
  searchInfo.groupName = data.groupName || undefined
  searchInfo.groupCode = data.groupCode || undefined
  searchInfo.paramDesc = data.paramDesc || undefined
  searchInfo.paramName = data.paramName || undefined
  searchInfo.editablePerson = data.editablePerson || undefined
  reload()
}

const searchFormData = ref<FormSchema[]>([
  {
    key: 'groupName',
    component: 'Input',
    val: searchCache?.groupName || '',
    fieldName: '组名'
  },
  {
    key: 'groupCode',
    component: 'Input',
    val: searchCache?.groupCode || '',
    fieldName: '组编码'
  },

  {
    key: 'paramDesc',
    component: 'Input',
    val: searchCache?.paramDesc || '',
    fieldName: '参数描述'
  },
  {
    key: 'paramName',
    component: 'Input',
    val: searchCache?.paramName || '',
    fieldName: '编码'
  },
  {
    key: 'editablePerson',
    component: 'Input',
    val: searchCache?.editablePerson || '',
    fieldName: '修改人'
  }
])

// 表格列配置
const columns = ref<ColumnType[]>([
  {
    key: 'groupName',
    title: '组名'
  },
  {
    key: 'groupCode',
    title: '组编码'
  },
  {
    key: 'paramDesc',
    title: '参数描述'
  },
  {
    key: 'paramName',
    title: '编码'
  },
  {
    key: 'paramValue',
    title: '参数值'
  },
  {
    key: 'type',
    title: '展示端',
    render: ({ cellData }) => {
      return (
        sysParamsTypeList.find((item) => item.value === cellData.type)?.label ||
        '-'
      )
    }
  },
  {
    key: 'edit',
    title: '是否可以修改',
    render: ({ cellData }) => {
      return cellData.editablePerson ? '是' : '否'
    }
  },
  {
    key: 'editablePerson',
    title: '修改人'
  },
  {
    key: 'operate',
    title: '操作',
    fixed: 'right',
    slot: 'operateSlot',
    width: 80
  }
])

const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})

// 表格配置
const [registerTable, { reload }] = useTable({
  api: systemSelectSysParamList,
  columns: getBasicColumns(),
  searchInfo: { model: searchInfo },
  immediate: true
})

function getBasicColumns (): ColumnType[] {
  return unref(columns)
}

/** 编辑参数 */
function handleEdit (scope: any) {
  formData.value = cloneDeep(scope)
  addDialogVisible.value = true
  isAdd.value = false
}

function handleAdd () {
  formData.value = cloneDeep(formDataInit)
  addDialogVisible.value = true
  isAdd.value = true
}

function addSure () {
  formDataDom.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        await systemInsertOrUpdate(formData.value)
        await reload()
        ElMessage.success(isAdd.value ? '新增成功' : '编辑成功')
        addDialogVisible.value = false
      } catch (error) {
        console.log(error)
      }
    }
  })
}
</script>
