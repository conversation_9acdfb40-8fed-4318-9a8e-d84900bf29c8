<template>
  <el-select-v2
    v-model="input"
    v-bind="$attrs"
    :multiple="multiple"
    :filterable="filterable"
    :options="options"
    :props="{ value: 'id', label: 'channelName' }"
  />
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue'
defineOptions({
  name: 'channelSelect'
})
const props = defineProps({
  modelValue: {
    type: [Array, String]
  },
  multiple: {
    type: Boolean,
    default: false
  },
  filterable: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['update:modelValue'])
const input = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})

const options = ref<{ [key: string]: any }[]>([])

</script>

<style lang="scss" scoped>
:deep(.el-select-v2__selected-item:nth-child(1)) {
  max-width: 60%;
  span {
    overflow: hidden;
  }
  .el-select-v2__tags-text {
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }
}
</style>
