import type { Column } from 'element-plus'
import type { ColumnType } from '@/types/table'

export interface BasicColumnType extends MakeOptional<Column, 'width'> {
    key: string
}

export interface BasicTableProps {
    columns: ColumnType[];// 表头
    data?: Recordable[];// 表格数据
    api?: (...arg: any) => Promise<any>;// 表格获取接口
    searchInfo?: Recordable; // 搜索条件传递
    immediate?: boolean; // 是否立刻加载
    showSelection?: boolean; // 是否展示多选
    showPagination?: boolean; // 是否显示分页
}

export interface TableActionType{
    setProps: (props: Partial<BasicTableProps>) => void;
    setSearchInfo: (data: Recordable) => void;
    reload: () => Promise<void>;
    getSectionData: () => any[]; // 获取已选项数据
    getTableData: () => TableData; // 获取表格数据
}

export interface FetchParams{

}

export interface PaginationProps{
    pageSize: number;
    pageNum: number
}

export interface TableData{
    list:Recordable[],
    total: number
}