import type { Slots } from 'vue'
import { isFunc } from '@/utils/is'
/**
 * @desc: 获取插槽，防止空插槽导致的错误
 * */
export function getSlot (slots: Slots, slot:string = 'default', data?: any):any {
  if (!slots || !Reflect.has(slots, slot)) {
    return null
  }
  if (!isFunc(slots[slot])) {
    return null
  }
  const slotFn = slots[slot]
  if (!slotFn) return null
  const params = { ...data }
  return slotFn(params)
}
/**
 * @desc: 处理插槽，使其返回一个普通对象
 * */
export function extendSlots (slots: Slots) {
  const ret:any = {}
  const slotKeys = Object.keys(slots)
  slotKeys.map(key => {
    ret[key] = (data?: any) => getSlot(slots, key, data)
  })
  console.log(ret, 'ret')
  return ret
}
