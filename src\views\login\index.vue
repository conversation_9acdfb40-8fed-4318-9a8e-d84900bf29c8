<template>
  <div class="login-page">
    <div class="login-content">
      <div class="title">{{ VITE_GLOB_APP_TITLE }}</div>
      <div class="name tw-mb-[18px]">
        <span
          :class="{ ac: loginType === 'MOBILE' }"
          @click="loginType = 'MOBILE'"
        >手机号登录</span
        >
        <span
          v-if="!isProdMode()"
          :class="{ ac: loginType === 'PASSWORD' }"
          @click="loginType = 'PASSWORD'"
        >账号登录</span
        >
        <span class="line" :class="{ ac: loginType === 'PASSWORD' }" />
      </div>
      <ul class="login-form">
        <li v-show="loginType === 'MOBILE'">
          <el-form ref="formDataCodeDom" :model="formData" :rules="rulesCode">
            <el-form-item prop="mobile">
              <RxkInput v-model="formData.mobile"
                        placeholder="请输入手机号"
                        size="large"
                        @keydown.enter.stop.prevent="submit">
                <template v-slot:prefix>
                  <span>
                    <span
                      class="border-right iconfont icon icon-leixingshoujiactiveno"
                    />
                  </span>
                </template>
              </RxkInput>
            </el-form-item>
            <el-form-item prop="code">
              <RxkInput v-model="formData.code"
                        size="large"
                        placeholder="请输入验证码"
                        @keydown.enter.stop.prevent="submit">
                <template v-slot:prefix>
                  <span>
                    <span class="border-right iconfont icon icon-lock-on" />
                  </span>
                </template>
                <template v-slot:suffix>
                  <span class="getCode pointer" @click="getCode">
                    {{ getCodeState ? '获取验证码' : timeout + 's' }}
                  </span>
                </template>
              </RxkInput>
            </el-form-item>
          </el-form>
        </li>
        <li v-show="loginType === 'PASSWORD'">
          <el-form
            ref="formDataPasswordDom"
            :model="formData"
            :rules="rulesPassword"
          >
            <el-form-item prop="username">
              <RxkInput
                v-model="formData.username"
                placeholder="请输入用户名"
                @keydown.enter.stop.prevent="submit"
              >
                <template v-slot:prefix>
                  <span>
                    <span
                      class="border-right iconfont icon icon-leixingchengyunshanxuanactiveno"
                    />
                  </span>
                </template>
              </RxkInput>
            </el-form-item>
            <el-form-item prop="password">
              <RxkInput
                v-model="formData.password"
                type="password"
                placeholder="请输入密码"
                @keydown.enter.stop.prevent="submit"
              >
                <template v-slot:prefix>
                  <span>
                    <span class="border-right iconfont icon icon-lock-on" />
                  </span>
                </template>
              </RxkInput>
            </el-form-item>
          </el-form>
        </li>
      </ul>
      <RxkButton
        size="large"
        type="primary"
        class="submit-btn"
        :loading="loginLoading"
        @click="submit"
      >
        登录
      </RxkButton>
    </div>

    <div class="registration-number">
      Copyright ©2025 haoxincd.cn 版权所有 渝ICP备********号-26
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { RxkInput } from '@/components/common/RxkInput'
import { ref, unref } from 'vue'
import { validateCode, validatePhone } from '@/utils/rules'
import { useAccountStore } from '@/stores/modules/account'
import { getCodeApi } from '@/apis/user'

import { createLocalStorage } from '@/utils/cache'
import type { LoginParams } from '@/types/user'
import { isProdMode, getAppEnvConfig } from '@/utils/env'
// import { useNavHistory } from '@/stores/modules/routerNav'
// const navHistory = useNavHistory()
const { VITE_GLOB_APP_TITLE } = getAppEnvConfig()
let clearInter: ReturnType<typeof setTimeout>
const router = useRouter()
const useAccount = useAccountStore()

const loginType = ref<'MOBILE' | 'PASSWORD'>('MOBILE')
const formDataCodeDom = ref()
const formDataPasswordDom = ref()
const timeout = ref(60)
const getCodeState = ref(true)
const ls = createLocalStorage()
const formData = ref<LoginParams>({
  clientType: 'SYSTEM',
  grantType: loginType.value,
  mobile: ls.get('mobile') || '',
  code: '',
  username: '',
  password: '',
  rememberMe: false
})
const rulesCode = ref({
  mobile: [{ validator: validatePhone, trigger: 'blur' }],
  code: [{ validator: validateCode, trigger: 'blur' }]
})
const rulesPassword = ref({
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'blur'
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: 'blur'
    }
  ]
})

const getCode = () => {
  formDataCodeDom.value.validateField('mobile', async (valid: boolean) => {
    if (!valid) return
    if (unref(getCodeState)) {
      try {
        getCodeState.value = false
        timeout.value = 60
        clearInter = setInterval(() => {
          timeout.value--
          if (timeout.value <= 0) {
            clearInterval(clearInter)
            getCodeState.value = true
          }
        }, 1000)
        await getCodeApi({}, { mobile: unref(formData).mobile })
      } catch (e) {
        clearInterval(clearInter)
        getCodeState.value = true
      }
    }
  })
}

const loginLoading = ref(false)
const submit = () => {
  if (loginLoading.value) return
  const validateDom =
    loginType.value === 'PASSWORD'
      ? formDataPasswordDom.value
      : formDataCodeDom.value
  formData.value.grantType = loginType.value
  validateDom.validate((valid: boolean) => {
    if (valid) {
      loginLoading.value = true
      useAccount
      .login(unref(formData))
      .then(() => {
        ls.set('mobile', unref(formData).mobile, null)
        ls.remove('isActive')
        // 跳转
        router.push({
          path: '/'
        })
        // navHistory.addHistory({
        //   name: '工作台',
        //   path: '/dashboard',
        //   closeAble: false
        // })
      })
      .finally(() => {
        loginLoading.value = false
      })
    }
  })
}
</script>
<style lang="scss" scoped>
.login-page {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('@/assets/images/loginBg.png') repeat center;
  background-size: 100%;

  > .login-content {
    width: 400px;
    padding: 40px;
    margin: 0 10px;
    background: #fff;
    border-radius: 8px;

    .title {
      font-size: 20px;
      font-weight: 700;
      color: #333;
      text-align: center;
    }

    .name {
      margin-top: 40px;
      font-size: 14px;
      color: #333333;
      display: flex;
      align-items: center;
      position: relative;
      gap: 12px;

      span {
        cursor: pointer;

        &.ac {
          color: #5687ff;
        }
      }

      .line {
        position: absolute;
        bottom: -8px;
        left: 0;
        height: 3px;
        display: inline-block;
        background: #5687ff;
        width: 28px;
        transition: left 0.3s;

        &.ac {
          left: 82px;
        }
      }
    }

    .login-form {
      display: flex;
      align-items: center;
      overflow: hidden;
      padding-top: 16px;

      &.ac {
        > li {
          transform: translateX(-100%);
        }
      }

      > li {
        min-width: 100%;
        transition: transform 0.3s;
      }

      .getCode {
        color: #5687ff;
      }
      :deep(.el-input__prefix) {
        height: calc(var(--el-input-height, 40px) - 2px)
      }
    }

    .input-box {
      margin-top: 24px;
      display: flex;

      .border-right {
        border-right: 1px solid #dcdfe6;
        padding-right: 9px;
      }

      .getCode {
        color: #5687ff;
      }
    }

    .submit-btn {
      margin-top: 22px;
      width: 100%;
    }
  }

  .registration-number {
    position: absolute;
    bottom: 20px;
    font-size: 12px;
    color: #a4a4a4;
    text-align: center;
  }
}
</style>
