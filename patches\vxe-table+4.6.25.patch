diff --git a/node_modules/vxe-table/es/table/src/props.js b/node_modules/vxe-table/es/table/src/props.js
index 581b0d6..c7e884b 100644
--- a/node_modules/vxe-table/es/table/src/props.js
+++ b/node_modules/vxe-table/es/table/src/props.js
@@ -44,6 +44,8 @@ export default {
     highlightCell: Boolean,
     // 是否显示表尾合计
     showFooter: Boolean,
+    // 是否显示表头合计
+    showHeaderSummary: Boolean,
     // 表尾数据
     footerData: Array,
     // 表尾合计的计算方法
diff --git a/node_modules/vxe-table/es/table/src/table.js b/node_modules/vxe-table/es/table/src/table.js
index e0f63bd..635aeb8 100644
--- a/node_modules/vxe-table/es/table/src/table.js
+++ b/node_modules/vxe-table/es/table/src/table.js
@@ -1411,7 +1411,7 @@ export default defineComponent({
             updateAfterDataIndex();
         };
         const updateStyle = () => {
-            const { border, showFooter, showOverflow: allColumnOverflow, showHeaderOverflow: allColumnHeaderOverflow, showFooterOverflow: allColumnFooterOverflow, mouseConfig, spanMethod, footerSpanMethod, keyboardConfig } = props;
+            const { border, showFooter, showHeaderSummary, showOverflow: allColumnOverflow, showHeaderOverflow: allColumnHeaderOverflow, showFooterOverflow: allColumnFooterOverflow, mouseConfig, spanMethod, footerSpanMethod, keyboardConfig } = props;
             const { isGroup, currentRow, tableColumn, scrollXLoad, scrollYLoad, scrollbarWidth, scrollbarHeight, columnStore, editStore, mergeList, mergeFooterList, isAllOverflow } = reactData;
             let { visibleColumn, fullColumnIdData, tableHeight, tableWidth, headerHeight, footerHeight, elemStore, customHeight, customMinHeight, customMaxHeight } = internalData;
             const containerList = ['main', 'left', 'right'];
@@ -1532,9 +1532,10 @@ export default defineComponent({
                         // 如果是固定列
                         if (fixedWrapperElem) {
                             if (isNodeElement(wrapperElem)) {
-                                wrapperElem.style.top = `${headerHeight}px`;
+                                wrapperElem.style.top = showHeaderSummary ? `${headerHeight + footerHeight - 1}px` : `${headerHeight}px`;
                             }
-                            fixedWrapperElem.style.height = `${(customHeight > 0 ? customHeight - headerHeight - footerHeight : tableHeight) + headerHeight + footerHeight - scrollbarHeight * (showFooter ? 2 : 1)}px`;
+                            const wHeight = (customHeight > 0 ? customHeight - headerHeight - footerHeight : tableHeight) + headerHeight + footerHeight - scrollbarHeight * (showFooter ? 2 : 1)
+                            fixedWrapperElem.style.height = `${showHeaderSummary ? wHeight - 8 : wHeight}px`;
                             fixedWrapperElem.style.width = `${fixedColumn.reduce((previous, column) => previous + column.renderWidth, isFixedLeft ? 0 : scrollbarWidth)}px`;
                         }
                         let tWidth = tableWidth;
@@ -1586,7 +1587,7 @@ export default defineComponent({
                         if (isNodeElement(wrapperElem)) {
                             // 如果是固定列
                             if (fixedWrapperElem) {
-                                wrapperElem.style.top = `${customHeight > 0 ? customHeight - footerHeight : tableHeight + headerHeight}px`;
+                                wrapperElem.style.top = showHeaderSummary ? `${headerHeight}px`:`${customHeight > 0 ? customHeight - footerHeight : tableHeight + headerHeight}px`;
                             }
                             wrapperElem.style.marginTop = `${-Math.max(1, scrollbarHeight)}px`;
                         }
@@ -6786,7 +6787,7 @@ export default defineComponent({
             tablePrivateMethods.preventEvent(null, 'unmounted', { $table: $xetable });
         });
         const renderVN = () => {
-            const { loading, stripe, showHeader, height, treeConfig, mouseConfig, showFooter, highlightCell, highlightHoverRow, highlightHoverColumn, editConfig, editRules } = props;
+            const { loading, stripe, showHeader, showHeaderSummary, height, treeConfig, mouseConfig, showFooter, highlightCell, highlightHoverRow, highlightHoverColumn, editConfig, editRules } = props;
             const { isGroup, overflowX, overflowY, scrollXLoad, scrollYLoad, scrollbarHeight, tableData, tableColumn, tableGroupColumn, footerTableData, initStore, columnStore, filterStore, customStore } = reactData;
             const { leftList, rightList } = columnStore;
             const loadingSlot = slots.loading;
@@ -6852,6 +6853,15 @@ export default defineComponent({
                             tableColumn,
                             tableGroupColumn
                         }) : createCommentVNode(),
+                        
+                        /**
+                         * 表头合计
+                         */
+                        showFooter && showHeaderSummary ? h(TableFooterComponent, {
+                            ref: refTableFooter,
+                            footerTableData,
+                            tableColumn
+                        }) : createCommentVNode(),
                         /**
                          * 表体
                          */
@@ -6863,7 +6873,7 @@ export default defineComponent({
                         /**
                          * 表尾
                          */
-                        showFooter ? h(TableFooterComponent, {
+                        showFooter && !showHeaderSummary ? h(TableFooterComponent, {
                             ref: refTableFooter,
                             footerTableData,
                             tableColumn
