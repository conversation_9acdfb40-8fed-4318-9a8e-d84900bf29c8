<template>
  <div class="isTxt" v-if="isTxt">
    {{innerValue ? getTime(innerValue) : '-'}}
  </div>
  <el-time-picker
    v-else
    v-model="innerValue"
    :format="renderConfig.format"
    :placeholder="renderConfig.placeholder"
    tyle="width: 100%"
  />
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { getTime } from '@/utils/date'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})

</script>