import { getStoragePrefix, isProdMode } from '@/utils/env'
import { createStorage } from '@/utils/cache/storage'
import type { CreateStorageParams } from '@/utils/cache/storage'

export type Options = Partial<CreateStorageParams>
export const DEFAULT_CACHE_TIME = 60 * 60 * 24 * 7
// 生成storage实例所需参数
const createStorageOptions = (storage: Storage, options: Options = {}): Options => {
  return {
    // hasEncrypt: isProdMode(), // 生产环境下才进行加密
    hasEncrypt: false, // 生产环境下才进行加密
    preKey: getStoragePrefix(), // 本地缓存前缀key
    storage,
    ...options
  }
}

// 创建localStorage实例对象
export function createLocalStorage (options: Options = {}) {
  return createStorage(createStorageOptions(localStorage, { ...options, timeout: DEFAULT_CACHE_TIME }))
}

// 创建sessionStorage实例对象
export function createSessionStorage (options: Options = {}) {
  return createStorage(createStorageOptions(sessionStorage, { ...options, timeout: DEFAULT_CACHE_TIME }))
}
