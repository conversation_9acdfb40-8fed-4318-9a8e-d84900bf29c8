<template>
  <!-- <LayoutFeatures /> -->
  <el-container :class="{'layout-shrink': config.layout.shrink}">
    <SideMenu />
    <el-main>
      <Header />
      <TopNav />
      <div class="main-container" id="main-container-id">
        <Main />
      </div>
    </el-main>
  </el-container>
</template>
<script setup lang="ts">
import { onMounted, watchEffect } from 'vue'
import SideMenu from './components/sideMenu/index.vue'
import Main from './components/main/index.vue'
import Header from './components/header/index.vue'
import TopNav from './components/nav/index.vue'
import watermark from '@/utils/libs/waterMark'
import { useConfig } from '@/stores/modules/config'
import dayjs from 'dayjs'
import { useAccountStore } from '@/stores/modules/account'

const accountStore = useAccountStore()
const config = useConfig()
// const LayoutFeatures = defineAsyncComponent(
//   () => import('@/views/layout/feature/index.vue')
// )
watchEffect(() => {
  const body = document.body
  if (config.layout.shrink) {
    body.classList.add('layout-shrink')
  } else {
    body.classList.remove('layout-shrink')
  }
})
onMounted(() => {
  watermark.set(
    `${accountStore?.userInfo?.realName}-${
      accountStore?.userInfo?.clientIp
    }-${dayjs().format(
      'YYYY-MM-DD HH:mm'
    )},版权所有：重庆市渝中区科融小额贷款有限责任公司`,
    'main-container-id'
  )
})
</script>
<style lang="scss" scoped>
.el-container {
  height: 100%;
  overflow: hidden;
  .el-main {
    background: #f4f4f5;
    padding: 0;
    .main-container {
      height: calc(100% - 105px);
      padding: 0 16px 12px 16px;
      overflow-y: auto;
    }
  }
}
</style>
