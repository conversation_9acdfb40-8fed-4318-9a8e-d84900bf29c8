import type { List } from '@/types/common'
import type { ComponentType, OptionType } from '@/types/preObjectManage'
import BaseData, { getOperate } from '@/utils/preObjectManage/common'
import { fieldTypeConfig, fieldTypeEnum, operateListEnum } from '@/enums/preObjectManage'
import type { componentOperateListType } from '@/types/preObjectManage'
import { GenNonDuplicateID } from '@/utils/tools'
import { isNullOrUndefOrEmpty } from '@/utils/is'

// 检验其value和label是否有重复, 若value重复,可传入相应的函数处理
// const validateArrLabelRepeat = (list: List[], validateResult:ValidNameType, fn: Fn) => {
//   const tempArr: any[] = []
//   const tempValueArr:any[] = []
//   const repeatValueIndexArr:any[] = []
//   let maxValue = 0
//
//   const flag = list.every((i, index) => {
//     const label = i.label
//     const value = String(i.value)
//     if (!tempValueArr.includes(value)) {
//       tempValueArr.push(value)
//     } else {
//       repeatValueIndexArr.push(index)
//     }
//     const valueNumberStr = value.startsWith('选项') ? value.slice(2) : 0
//     const valueNumber = Number(valueNumberStr)
//     if (valueNumber > maxValue) {
//       maxValue = valueNumber
//     }
//     if (!tempArr.includes(label)) {
//       tempArr.push(label)
//     } else {
//       validateResult.msg = `选项内容第${index + 1}项内容有重复`
//       validateResult.success = false
//       return false
//     }
//     return true
//   })
//   if (flag) {
//     // 表示有重复的选项,需要修改list的value值
//     if (repeatValueIndexArr.length > 0) {
//       repeatValueIndexArr.forEach((idx) => {
//         list[idx].value = `选项${++maxValue}`
//       })
//     }
//     validateResult.msg = ''
//     validateResult.success = true
//     fn && fn(list)1
//   }
// }

function getJson (): List[] {
  return [{
    label: '选项1',
    value: GenNonDuplicateID(3)
  },
  {
    label: '选项2',
    value: GenNonDuplicateID(3)
  },
  {
    label: '选项3',
    value: GenNonDuplicateID(3)
  }]
}

// 单选框按钮
export class SingleRadio extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '单选按钮'

    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.radio,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        columnDataList: data.columnInfo?.columnDataList && data.columnInfo?.columnDataList.length ? data.columnInfo?.columnDataList : getJson(),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 1,
        precisions: data.columnInfo?.precisions || 2
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.radio
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 复选框组
export class MultipleCheckbox extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '复选按钮'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.checkbox,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        columnDataList: data.columnInfo?.columnDataList && data.columnInfo?.columnDataList.length ? data.columnInfo?.columnDataList : getJson(),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 1,
        precisions: data.columnInfo?.precisions || 2
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.checkbox
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 下拉单选框
export class Select extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '下拉单选框'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.select,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        columnDataList: data.columnInfo?.columnDataList && data.columnInfo?.columnDataList.length ? data.columnInfo?.columnDataList : getJson(),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 1
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.select
  }

  // 检验自身数据
  // validate (){
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   if (validateResult.success) {
  //     // 检查选项内容是否重复
  //     // const list = this.fieldBaseData.defaultData ? JSON.parse(this.fieldBaseData.defaultData) : []
  //     // validateArrLabelRepeat(list, validateResult, () => {
  //     //   this.fieldBaseData.defaultData = JSON.stringify(list)
  //     // })
  //   }
  //   return validateResult
  // }
}
// 下拉复选框
export class MultipleChoice extends BaseData {
  public fieldProperty: string
  constructor (data:ComponentType, options:OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '下拉复选框'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.multipleChoice,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        columnDataList: data.columnInfo?.columnDataList && data.columnInfo?.columnDataList.length ? data.columnInfo?.columnDataList : getJson(),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 1
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.multipleChoice
  }

  // 检验自身数据
  // validate (){
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   if (validateResult.success) {
  //     // 检查选项内容是否重复
  //     // const list = this.fieldBaseData.defaultData ? JSON.parse(this.fieldBaseData.defaultData) : []
  //     // validateArrLabelRepeat(list, validateResult, () => {
  //     //   this.fieldBaseData.defaultData = JSON.stringify(list)
  //     // })
  //   }
  //   return validateResult
  // }
}

// 成员单选
export class Person extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '成员单选'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.person,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: 2
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.person
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 成员多选
export class PersonMultiple extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '成员多选'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.personMultiple,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: 2
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.personMultiple
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
  // }
}

// 部门单选
export class Department extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '部门单选'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.department,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: 2
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.department
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
// 部门多选
export class MultipleDepartment extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '部门多选'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.multipleDepartment,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: 2
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.multipleDepartment
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
// 多级联动
export class Cascader extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '多级联动'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    const jsonArr = [{
      label: '1级+选项1',
      value: GenNonDuplicateID(3),
      level: '1',
      parent: ''
    },
    {
      label: '1级+选项2',
      value: GenNonDuplicateID(3),
      level: '1',
      parent: ''
    },
    {
      label: '1级+选项3',
      value: GenNonDuplicateID(3),
      level: '1',
      parent: ''
    }]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.cascader,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        columnDataList: data.columnInfo?.columnDataList && data.columnInfo?.columnDataList.length ? data.columnInfo?.columnDataList : jsonArr,
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 1,
        precisions: data.columnInfo.precisions || 1
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.cascader
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   if (validateResult.success) {
  //     validateCascade(validateResult)
  //   }
  //   return validateResult
  // }
}

// 地址
export class Address extends BaseData {
  public fieldProperty: string
  constructor (data:ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '地址'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.address,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        precisions: data.columnInfo.precisions || 1,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.address
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}