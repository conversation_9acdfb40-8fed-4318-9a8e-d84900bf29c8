<template>
  <div>
    <el-upload
      class="upload-demo"
      :action="state.fileUpload"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :before-upload="hookBeforeUpload ? hookBeforeUpload : beforeUpload"
      :accept="accept"
      multiple
      :limit="limit"
      :on-exceed="handleExceed"
      :show-file-list="showFileList"
      :file-list="fileList"
      :disabled="disabled"
      :headers="state.userInfo"
      :auto-upload="autoUpload"
      :on-change="handleChange"
      :list-type="listType"
      ref="RxkUploadFileRef">
      <slot name="upload">
        <RxkButton v-if="!disabled" class="button" icon="el-icon-upload">上传附件
        </RxkButton>
      </slot>
    </el-upload>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, type PropType } from 'vue'
import { RxkButton } from '@/components/common/RxkButton'
import { getBasParams } from '@/utils/tools'
import globalEnv from '@/configs'
import { getToken } from '@/utils/auth'
import { isNullOrUndefOrEmpty } from '@/utils/is'
import { ElMessage } from 'element-plus'
const props = defineProps({
  limit: {
    type: Number,
    default: 2
  },
  accept: {
    type: String,
    default: ''
    // default: '.csv, application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  },
  maxSize: {
    type: Number,
    default: 30
  },
  fileList: {
    type: Array,
    default: () => ([])
  },
  showFileList: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否自动上传
  autoUpload: {
    type: Boolean,
    default: true
  },
  listType: {
    type: String as PropType<'text' | 'picture' | 'picture-card'>,
    default: ''
  },
  hookBeforeUpload: {
    type: Function as unknown as (file: File) => (file: File) => boolean || undefined,
    default: undefined
  }
})
const RxkUploadFileRef = ref()
const emit = defineEmits(['handleSuccess', 'startUpload', 'handleRemove', 'preview', 'change'])
const state = reactive({
  fileInfo: {
    name: ''
  },
  fileUpload: globalEnv.uploadApi,
  data: getBasParams({ pictureType: 'common' }),
  userInfo: {
    // ignoreCancelToken: true,
    Authorization: getToken()
  }
})
function beforeUpload (file: File) {
  if (isNullOrUndefOrEmpty(props.maxSize) || props.maxSize === 0) {
    emit('startUpload', file)
    return true
  } else {
    const size = props.maxSize * 1024 * 1024
    if (file.size > size) {
      ElMessage.warning(`上传文件不能大于${props.maxSize}M`)
      return false
    }
    emit('startUpload', file)
    return true
  }
}
function handleRemove (file: File, fileList: any[]) {
  emit('handleRemove', fileList, file)
}
function handlePreview (file: File) {
  console.log(file)
  emit('preview', file)
}
function handleExceed (files: File[], fileList: any[]) {
  ElMessage.warning(`当前限制选择 ${props.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
}
function handleChange (uploadFile: any, uploadFiles: any){
  emit('change', uploadFile, uploadFiles)
}
function handleSuccess (res:any, file: File, fileList:any) {
  console.log('上传成功', res)

  // const responseData = decryptResponse(res)
  emit('handleSuccess', fileList, file, res)
}
function handleClearFiles (){
  RxkUploadFileRef?.value?.clearFiles()
}
defineExpose({
  handleClearFiles
})
</script>

<style scoped lang="scss">
.upload-demo {
  //overflow: hidden;
  //font-size: 0;

  :deep(.el-upload-list__item-name) {
    font-size: 12px;
  }

  :deep(.el-upload) {
    margin-top: 2px;
    height: 28px;
  }
}

.button {
  height: 24px;
  padding: 0 11px;
  font-size: 12px;
}
</style>
