<template>
  <RxkDialog
    v-model="addVisible"
    :title="titleText"
    width="480px"
    :showFooter="false"
    :close-on-click-modal="false"
    >
    <div class="setting-box">
      <el-form :model="state.form"  ref="ruleFormRef" labelPosition="left" :rules="state.rules" label-width="130" :disabled="type === 'detail'">
        <el-form-item label="参数名称：" prop="name">
          <RxkInput v-model="state.form.name" placeholder="请输入" maxlength="10" />
        </el-form-item>
        <el-form-item label="参数描述：" prop="description">
          <RxkInput v-model="state.form.description" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label="参数代码：" prop="code">
          <RxkInput v-model="state.form.code" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label="参数值：" prop="value">
          <RxkInput type="textarea" resize="none" v-model="state.form.value" placeholder="请输入" maxlength="1000" />
        </el-form-item>
        <el-form-item label="参数分组名称：" prop="groupId">
          <RxkSelect
            v-model="state.form.groupId"
            placeholder="请选择"
            filterable
            :list="state.paramsGroupOptions"
          />
        </el-form-item>
        <el-form-item label="是否只读：" prop="readOnly">
          <el-radio-group v-model="state.form.readOnly">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer-content">
      <template v-if="type !== 'detail'">
        <RxkButton @click="handleCancel">取消</RxkButton>
        <RxkButton type="primary" @click="handleConfirm">确定</RxkButton>
      </template>
      <template v-else>
        <RxkButton @click="handleCancel">关闭</RxkButton>
      </template>
    </div>
  </RxkDialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from "vue" 
import { isEmptyObject } from "@/utils/is";
import { RxkButton } from '@/components/common/RxkButton'
import { RxkSelect } from "@/components/common/RxkSelect";
import { RxkInput } from "@/components/common/RxkInput/index";
import { RxkDialog } from "@/components/common/RxkDialog/index";
import type{ paramsItem } from "../../type";
import { paramGroupListApi } from "@/apis/paramsManage";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: ()=>{}
  },
  type: {
    type: String,
    default: ''
  }
})
const titleText = computed(()=>{
  return {
    'add': '系统参数添加',
    'edit': '编辑',
    'detail': '详情',
  }[props.type]
})
const emit = defineEmits(['update:visible', 'close', 'confirm'])
const addVisible = computed({
  get(){
    return props?.visible
  },
  set: (value: any) => emit('update:visible', value)
})
const state = reactive<any>({
  form: {
    code: '',
    description: '',
    groupId: '',
    name: '',
    readOnly: '',
    value: '',
    id: ''
  } as paramsItem,
  paramsGroupOptions: [],
  rules: {
    name: [
      { required: true, message: '请输入参数名称', trigger: 'blur' }
    ],
    code: [
      { required: true, message: '请输入参数代码', trigger: 'blur' }
    ],
    value: [
      { required: true, message: '请输入参数值', trigger: 'blur' }
    ],
    groupId: [
      { required: true, message: '请选择参数分组名称', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '请输入参数描述', trigger: 'blur' }
    ],
    readOnly: [
      { required: true, message: '请选择', trigger: 'blur' }
    ]
  },
  seatIds: [] as string[],
  showBindLine: false
})
watch(()=> props?.data, (val)=>{
  if(!isEmptyObject(val)){
    const {code, description, groupId, name, readOnly, value, id} = val as paramsItem
    state.form = {code, description, groupId, name, readOnly, value, id}
  }else{
    state.form = {
      code: '',
      description: '',
      groupId: '',
      name: '',
      readOnly: '',
      value: '',
      id: ''
    }
  }
}, { immediate: true})

const ruleFormRef = ref()

function handleConfirm (fn: Fn) {
  ruleFormRef.value?.validate?.((res: boolean)=>{
    if(res){
      emit('confirm', state.form, ()=>{
        fn?.()
      })
    }
  })
}
function handleCancel (fn: Fn, sign?: string) {
  fn?.()
  emit('close');
}

// 获取参数分组
function getParamsGroups () {
  paramGroupListApi().then((res)=>{
    state.paramsGroupOptions = res.length ? res.map((ele)=>({value: ele.id, label: ele.name})) : []
  })
}
getParamsGroups()
</script>


<style scoped lang="scss">
.setting-box{
  overflow-y: auto;
  padding: 16px 24px 0;
}
.footer-content{
  border-top: 1px solid #EBEEF5;
  padding: 16px 24px;
  @include flex-center(row, flex-end, center);
}
</style>