<template>
  <div class="yearPicker" ref="yearPicker" :width="width">
    <i class="dateIcon iconfont icon-leixingrijishijianactiveno"/>
    <input
      class="_inner"
      ref="inputLeftRef"
      v-model="state.startShowYear"
      @focus="onFocus"
      type="text"
      name="yearInput"
      @keyup="checkStartInput()"
      placeholder="开始时间"
    >
    <span>{{ sp }}</span>
    <input
      class="_inner"
      ref="inputRightRef"
      v-model="state.endShowYear"
      @focus="onFocus"
      type="text"
      name="yearInput"
      @keyup="checkEndInput()"
      placeholder="结束时间"
    >
    <!-- <i class="dateIcon el-icon-date"></i> 按照自己标准库里面的图标设置-->
    <!-- <span class="_inner labelText">{{ labelText }}</span> -->
    <div class="_inner floatPanel" v-if="state.showPanel">
      <div class="_inner leftPanel">
        <div class="_inner panelHead">
          <i class="_inner el-icon-d-arrow-left" @click="onClickLeft"/>
          <div style="font-size: 16px;color: #666;">{{ leftYearList[0] + " - " + leftYearList[9] }}</div>
          <span/>
        </div>
        <div class="_inner panelContent">
          <div
            :class="{
              oneSelected: item === state.startYear && oneSelected,
              startSelected: item === state.startYear,
              endSelected: item === state.endYear,
              betweenSelected: state.startYear && state.endYear && item > state.startYear && item < state.endYear,
            }"
            v-for="item in leftYearList"
            :key="item"
          >
            <a
              :class="{
                cell: true,
                _inner: true,
                selected: item === state.startYear || item === state.endYear,
              }"
              @click="onClickItem(item)"
              @mouseover="onHoverItem(item)"
            >
              {{ item }}
            </a>
          </div>
        </div>
      </div>
      <div class="_inner rightPanel">
        <div class="_inner panelHead">
          <span/>
          <div style="font-size: 16px;color: #666;">{{ rightYearList[0] + " - " + rightYearList[9] }}</div>
          <i class="_inner el-icon-d-arrow-right" @click="onClickRight"/>
        </div>
        <div class="_inner panelContent">
          <div
            :class="{
              startSelected: item === state.startYear,
              endSelected: item === state.endYear,
              betweenSelected: state.startYear && state.endYear && item > state.startYear && item < state.endYear,
            }"
            v-for="item in rightYearList"
            :key="item"
          >
            <a
              :class="{
                cell: true,
                _inner: true,
                selected: item === state.endYear || item === state.startYear,
              }"
              @click="onClickItem(item)"
              @mouseover="onHoverItem(item)"
            >
              {{ item }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, computed, onBeforeMount, onMounted, reactive, nextTick, ref } from 'vue' 
import dayjs from 'dayjs'
const SELECT_STATE = {
  unselect: 0,
  selecting: 1,
  selected: 2
}
const props = defineProps({
  width: {
    default: 200
  },
  labelWidth: {
    default: 80
  },
  sp: {
    default: '至'
  },
  value: {
    default: Number(dayjs().format('YYYY'))
  }
})
const emit = defineEmits(['change'])
const state = reactive({
  itemBg: {},
  startShowYear: null as null | number,
  endShowYear: null as null | number,
  yearList: [] as number[],
  showPanel: false,
  startYear: null as null | number,
  endYear: null as null | number,
  curYear: 0,
  curSelectedYear: 0,
  curState: SELECT_STATE.unselect
})
const inputLeftRef = ref()
const inputRightRef = ref()
const oneSelected = computed(() => {
  return (
    state.curState === SELECT_STATE.selecting &&
        (state.startYear === state.endYear || state.endYear == null)
  )
})
const startDate = computed(() => {
  return state.startYear
})
const leftYearList = computed(() => {
  return state.yearList.slice(0, 10)
})
const rightYearList = computed(() => {
  return state.yearList.slice(10, 20)
})
function checkStartInput () {
  if (state.startShowYear && isNaN(state.startShowYear)) {
    state.startShowYear = state.startYear
  } else {
    state.startYear = state.startShowYear ? state.startShowYear * 1 : Number(dayjs().format('YYYY'))
    changeYear()
  }
}

function checkEndInput () {
  if (state.endShowYear && isNaN(state.endShowYear)) {
    state.endShowYear = state.endYear
  } else {
    state.endYear = state.endShowYear ? state.endShowYear * 1 : Number(dayjs().format('YYYY'))
    changeYear()
  }
}
function changeYear () {
  if (state.startYear && state.endYear && state.startYear > state.endYear) {
    const tmp = state.endYear
    state.endYear = state.startYear
    state.startYear = tmp
    state.startShowYear = state.startYear
    state.endShowYear = state.endYear
  }
  if (state.startYear && state.endYear) {
    emit('change', {
      startYear: state.startYear,
      endYear: state.endYear
    })
  } else {
    console.warn('WARN:年份不合法', state.startYear, state.endYear)
  }
}
function onHoverItem (iYear: number) {
  if (state.curState === SELECT_STATE.selecting) {
    const tmpStart = state.curSelectedYear
    state.endYear = Math.max(tmpStart, iYear)
    state.startYear = Math.min(tmpStart, iYear)
  }
}
function onClickItem (iYear: number) {
  if (
    state.curState === SELECT_STATE.unselect ||
    state.curState === SELECT_STATE.selected
  ) {
    state.startYear = iYear
    state.curSelectedYear = iYear
    state.endYear = null
    state.curState = SELECT_STATE.selecting
  } else if (state.curState === SELECT_STATE.selecting) {
    if (state.endYear === null) {
      state.endYear = iYear
    }
    state.endShowYear = state.endYear
    state.startShowYear = state.startYear
    state.curState = SELECT_STATE.selected
    emit('change', {
      startYear: state.startYear,
      endYear: state.endYear
    })
    state.showPanel = false
  }
}
function onFocus () {
  nextTick(() => {
    state.showPanel = true
  })
}
function updateYearList () {
  let iStart = Math.floor(state.curYear / 10) * 10 - 10
  iStart = iStart < 0 ? 0 : iStart
  state.yearList = []
  for (let index = 0; index < 20; index++) {
    state.yearList.push(iStart + index)
  }
}
function closePanel (e: any) {
  if (!state.showPanel) {
    return
  }
  if (typeof e.target.className !== 'string') {
    nextTick(() => {
      state.showPanel = false
    })
    return
  }
  if (
    e.target.className.indexOf('_inner') === -1 ||
    (e.target.name === 'yearInput' &&
      e.target !== inputLeftRef.value &&
      e.target !== inputRightRef.value)
  ) {
    nextTick(() => {
      state.showPanel = false
    })
  }

  e.stopPropagation()
  return false
}
function onClickLeft () {
  state.curYear = state.curYear * 1 - 10
  updateYearList()
}
function onClickRight () {
  state.curYear = state.curYear * 1 + 10
  updateYearList()
}
function setYear (startYearStamp: string | number | Date, endYearStamp: string | number | Date) {
  if (startYearStamp && !isNaN(Number(startYearStamp)) && endYearStamp && !isNaN(Number(endYearStamp))) {
    const startYear = Number(dayjs(startYearStamp).format('YYYY'))
    const endYear = Number(dayjs(endYearStamp).format('YYYY'))
    state.startYear = startYear * 1
    state.endYear = endYear * 1
    state.endShowYear = endYear * 1
    state.startShowYear = startYear * 1
  }
}
watch(() => props.value, () => {
  
  if(props.value){
    state.curYear = props.value * 1
    state.startYear = props.value * 1
    state.endYear = props.value * 1
    state.endShowYear = props.value * 1
    state.startShowYear = props.value * 1
    updateYearList()
  }
}, { immediate: true })

onBeforeMount(() => {
  document.removeEventListener('click', closePanel.bind(this))
})
onMounted(() => {
  document.addEventListener('click', closePanel.bind(this))
})
defineExpose({
  setYear
})
</script>

<style lang="scss" scoped>
.yearPicker {
  font-size: 14px;
  display: inline-block;
  position: relative;
  transition: all 0.3s;
  // input:first-child {
  //   text-align: right;
  // }
  .labelText {
    position: absolute;
    left: 8px;
  }
  background-color: #fff;
  span {
    height: 32px;
    line-height: 32px;
    color: #666666;
    font-size: 12px;
  }
  border: 1px solid #DCDFE6;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  padding: 0 28px 0 8px;
  box-sizing: border-box;
  .floatPanel {
    > div {
      width: 50%;
      padding: 0 16px;
    }
    position: absolute;
    display: flex;
    background-color: #fff;
    z-index: 2000;
    border-radius: 4px;
    width: 626px;
    height: 250px;
    top: 40px;
    left: -346px;
    border: 1px solid #d2d2d2;
    .panelContent {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: calc(100% - 60px);
      .oneSelected {
        border-top-right-radius: 24px;
        border-bottom-right-radius: 24px;
      }
      .startSelected {
        background-color: #ECF2FE;
        border-top-left-radius: 24px;
        border-bottom-left-radius: 24px;
      }
      .endSelected {
        background-color: #ECF2FE;
        border-top-right-radius: 24px;
        border-bottom-right-radius: 24px;
      }
      .betweenSelected {
        background-color: #ECF2FE;
      }
      > div {
        width: 70px;
        height: 40px;
        line-height: 40px;
        margin: 3px 0;
        text-align: center;
        font-size: 12px;

        a {
          display: inline-block;
          width: 52px;
          height: 32px;
          cursor: pointer;
          line-height: 32px;
          border-radius: 18px;
        }
        .selected {
          background-color: #409EFF;
          color: #fff;
          font-weight: 700;
        }
      }
    }
    .panelHead {
      position: relative;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0 8px;
      i {
        cursor: pointer;
        &:hover {
          color: #409EFF;
        }
      }
    }
    .leftPanel .panelHead i {
      left: 20px;
    }
    .rightPanel .panelHead i {
      right: 20px;
    }
  }
  .floatPanel::before {
    content: "";
    height: 100%;
    position: absolute;
    left: 50%;
    width: 1px;
    border-left: 1px solid #e4e4e4;
  }
}
input {
  width: 99px;
  border: none;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
  background-color: transparent;
  color: #606266;
  text-align: center;
}
input:focus {
  outline: none;
  background-color: transparent;
}
input::placeholder {
  color: #adb2bc;
  font-size: 13px;
}
.yearPicker:hover {
  border-color: #409EFF;
}
.dateIcon {
  // position: absolute;
  // left: 10px;
  // top: 9px;
  color: #adb2bc;
}
</style>