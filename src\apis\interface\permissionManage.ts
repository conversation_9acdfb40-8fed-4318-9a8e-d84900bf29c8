import type { ReqPage } from './index'

export interface MenuResourceVOList {
  id: string;
  name: string;
  requestMethod: string;
  uri: string;
  checkFlag: boolean;
  dataPermission: boolean;
  isResource: boolean;
  className?: string;
  disabled: boolean
}

export enum EDataScope {
  SELF = 'SELF',
  DEPARTMENT = 'DEPARTMENT',
  DEPARTMENT_SUB = 'DEPARTMENT_SUB',
  DEPARTMENT_CUSTOM = 'DEPARTMENT_CUSTOM',
  ALL = 'ALL'
}

// * 用户管理模块
export namespace Permission {

  export interface ReqRoleParams extends ReqPage {
    username: string;
    state: string;
    departmentId: number;
  }

  export interface ReqRoleListReturn {
    id: string;
    name: string;
    roleType: {
      code: number;
      name: string;
      value: string;
    },
    roleSource: {
      code: number;
      name: string;
      value: string;
    },
    remarks: string;
    state: {
      code: number;
      name: string;
      value: string;
    }
  }

  export interface ReqRoleMenuReturn {
    id: string;
    parentId: string;
    sortValue: string;
    children?: ReqRoleMenuReturn[],
    name: string;
    code: number;
    treeGrade: number;
    path: string;
    icon: string;
    resourceVOList: MenuResourceVOList[];
    departmentIdList: string[];
    dataScopeList: {
      code: 3,
      name: EDataScope,
      value: string
    }[];
    checkFlag: boolean;
    dataPermission?: boolean
    isCheckItem?: boolean
    isHalf?: boolean
    className?: string
    isResource?: boolean
    disabled?:boolean
  }

  export interface ReqPermissionListReturn {
    code: number;
    name: string;
    value: string;
    isChecked: boolean;
  }

  export interface ReqMenuPermissionParams {
    menuResourceList: {
      menuId: number;
      resourceIds: string[]
    }[];
    roleId: string;
  }

  export interface ReqSetDesensitizeItem {
    appCode: string;
    fieldList: {
      columnSerial: string;
      fieldCode: string;
      fieldName: string;
      checkFlag: boolean;
    }[],
  }

  export interface ReqSetDesensitizeParams {
    appList: ReqSetDesensitizeItem[],
    roleId: string;
  }

  export interface ReqGetDesensitizeReturn {
    name: string;
    appCode: string;
    fieldList: {
      fieldName: string;
      fieldCode: string;
      columnSerial: string;
      checkFlag: boolean;
      disabled: boolean;
    }[]
  }

  export interface ReqGetDesensitizeParams {
    roleId: string;
    systemCode: string;
  }

  export interface ReqScopeDataParams {
    dataScopeList: string[]
    resourceIdList: number[]
    departmentIdList: string[]
    menuId: string;
    roleId: string;
  }

  export interface ReqRoleDetailReturn {
    id: string;
    name: string;
    roleType: {
      code: number;
      name: string;
      value: string;
    },
    roleSource: {
      code: number;
      name: string;
      value: string;
    },
    remarks: string;
    state: {
      code: number;
      name: string;
      value: string;
    },
    dataScopeList: [],
    departmentIdList: [],
  }
}