import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { CreateOptions, RequestOptions, Result } from '@/utils/axios/axiosModel'
import { AxiosCanceler } from './axiosCancel'
import { isFunc, isFormData } from '@/utils/is'
import { ContentTypeEnum } from '@/enums/axios'
import { getToken } from '@/utils/auth'
import { turnDataToFormData } from './utils'
import { cloneDeep } from '../tools'
/**
 * @author：张胜
 * @desc：网络请求封装
 * */
export class RxkAxios {
  private readonly options: CreateOptions
  private instance: AxiosInstance // 实例
  constructor (options: any) {
    this.options = options
    this.instance = axios.create(options)
    this.setupInterceptors()
  }
  // 拦截器
  setupInterceptors () {
    const transform = this.getTransform()
    if (!transform) {
      return
    }
    const {
      requestInterceptors,
      responseInterceptors,
      responseInterceptorsCatch
    } = transform
    const axiosCanceler = new AxiosCanceler()
    // axios请求拦截处理
    this.instance.interceptors.request.use((config) => {
      // @ts-ignore
      const { ignoreCancelToken } = config.requestOptions
      // 是否添加ignoreCancelToken字段判断是否需要忽略cancelToken
      const ignoreCancel = ignoreCancelToken !== undefined ? ignoreCancelToken : this.options.requestOptions?.ignoreCancelToken
      // 如果需要使用cancelToken
      !ignoreCancel && axiosCanceler.addPending(config)
      if (requestInterceptors && isFunc(requestInterceptors)) {
        config = requestInterceptors(config, this.options)
      }
      return config
    }, undefined)
    // axios响应拦截处理
    this.instance.interceptors.response.use((res) => {
      // 请求完成后移除对应的cancelToken
      res && axiosCanceler.removePending(res.config)
      if (responseInterceptors && isFunc(responseInterceptors)) {
        res = responseInterceptors(res)
      }
      return res
    }, (e) => {
      if (responseInterceptorsCatch && isFunc(responseInterceptorsCatch)) {
        responseInterceptorsCatch(e)
      }
    })
  }
  /**
   * @description: 上传接口,一般上传接口不同于其他接口
   * @param {AxiosRequestConfig} config
   * @param {UploadParams} uploadParams
   * @return {*}
   */
  upload (config:any, requestOptions = {}) {
    const { data, headers = {} } = config
    const formData = isFormData(data) ? data : turnDataToFormData(data)
    return this.request({
      method: 'POST', // 默认post接求方式
      ...config,
      data: formData,
      headers: Object.assign(headers, {
        'Content-Type': ContentTypeEnum.FORM_DATA,
        ignoreCancelToken: true,
        token: getToken()
      })
    }, { isNeedSign: false, ...requestOptions })
  }
  /**
   * post请求
   * T:返回数据类型
   * D:请求参数类型
   * */
  post<T = any, D = any> (url: string, data: any, headerConfig?: AxiosRequestConfig, requestOptions?: RequestOptions): Promise<T> {
    const postData: any = {}
    if (data && data.params) {
      postData.params = data.params
      if (data.data) {
        postData.data = data.data
      }
    } else {
      postData.data = data
    }

    return this.request<T, D>({ ...cloneDeep(headerConfig), url, method: 'POST', ...postData, requestOptions }, requestOptions)
  }
  get<T = any, D = any> (url: string, data: any, headerConfig?: AxiosRequestConfig, requestOptions?: RequestOptions): Promise<T> {
    return this.request<T, D>({ ...cloneDeep(headerConfig), url, method: 'GET', params: data, requestOptions }, requestOptions)
  }
  request<T = any, D = any> (config: AxiosRequestConfig<D>, requestOptions?: RequestOptions): Promise<T> {
    let conf = config
    const defaultOptions = this.options.requestOptions
    const opt = Object.assign({}, defaultOptions, requestOptions)
    const transform = this.getTransform()
    const { beforeRequestHook, afterResponseHook } = transform || {}
    // 请求拦截处理
    if (beforeRequestHook && isFunc(beforeRequestHook)) {
      conf = beforeRequestHook(conf, opt)
    }
    return new Promise((resolve, reject) => {
      this.instance.request<any, AxiosResponse<Result>>(conf).then((res:AxiosResponse<Result>) => {
        // 响应拦截处理
        if (afterResponseHook && isFunc(afterResponseHook)) {
          try {
            const ret = afterResponseHook(res, opt)
            resolve(ret)
          }catch (err) {
            reject(err || new Error('request error'))
          }
          return
        }
        resolve(res as unknown as Promise<T>)
      }).catch(err => {
        // 错误处理
        reject(err)
      })
    })
  }
  getTransform () {
    const { transform } = this.options
    return transform
  }
}
