import type { AppRouteRecordRaw } from '@/router/types'
import { systemManage } from '@/router/modules/systemManage'
import { dataStatistics } from '@/router/modules/dataStatistics'
import { channelManagement } from '@/router/modules/channelManagement'
import { appManage } from '@/router/modules/appManage'
// import './routeToSql'
export const rootRoute: AppRouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/layout/index.vue'),
    meta: {
      title: '布局',
      isLogin: true
    },
    children: [
      {
        path: '/:pathMatch(.*)',
        component: () => import('@/views/404/index.vue'),
        meta: {
          title: '404',
          isLogin: false
        }
      },
      {
        path: '/502',
        component: () => import('@/views/502/index.vue'),
        meta: {
          title: '502',
          isLogin: false
        }
      },
      {
        path: '/securityPasswordSet',
        name: 'SecurityPasswordSet',
        component: () => import('@/views/securityPasswordSet/index.vue'),
        meta: {
          title: '安全密码设置',
          isLogin: true
        }
      },
      ...systemManage,
      ...appManage,
      ...dataStatistics,
      ...channelManagement
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', isLogin: false }
  },
  {
    path: '/link',
    name: 'Link',
    component: () => import('@/views/channelManagement/list/link.vue'),
    meta: { title: '链接', isLogin: false }
  }
]

/**
 * 需要配置菜单的路由
 */
export const menuRoute: AppRouteRecordRaw[] = [
  ...systemManage
]
