import { type ColumnProps } from '@/components/common/RxkProTable/interface'
import { type User } from '@/apis/interface/userManage'
import { type SearchConfig } from '@/components/common/RxkSearchBar/type'
import { reactive } from 'vue'

export const stateEnum = [
  { label: '启用', value: 'ENABLED' },
  { label: '禁用', value: 'DISABLED' }
]

export const Config = reactive<SearchConfig[]>([
  {
    code: 'roleName',
    type: 'input',
    value: '',
    label: '角色名称',
    placeholder: '请输入'
  }
])

// 表格配置项
export const columns: ColumnProps<User.ResUserList>[] = [
  { prop: 'id', label: '角色ID', width: 80 },
  { prop: 'username', label: '角色名称' },
  { prop: 'mobile', label: '角色描述' },
  {
    prop: 'state',
    label: '状态',
    enum: stateEnum,
    render: (scope: { row: User.ResUserList }) => {
      return <div class={'status-style'}>{scope.row.state?.value}</div>
    },
    width: 60
  },
  { prop: 'createTime', label: '添加时间', width: 180 },
  { prop: 'updateTime', label: '最近登录时间', width: 180 },
  { prop: 'operation', label: '操作', fixed: 'right', width: 160 }
]
