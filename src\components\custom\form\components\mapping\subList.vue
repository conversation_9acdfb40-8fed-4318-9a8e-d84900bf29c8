<template>
  <div class="quote-box">
    <div class="isTxt" v-if="isTxt">
      {{innerValue || '-'}}
    </div>
    <template v-else>
      <el-select
        v-model="innerValue"
        filterable
        remote
        remote-show-suffix
        :placeholder="renderConfig.placeholder"
        :remote-method="remoteMethod"
        :loading="loading"
        style="width: 100%"
        @change="changeValue"
      >
        <el-option
          v-for="item in list"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <div class="showDialogOverlay" @click="handleShow" v-if="isShowDialog"/>
    </template>
  </div>
  <RxkDialog v-model="showDialog">
    <div>111</div>
  </RxkDialog>
</template>
<script lang="ts" setup>
import { computed, onMounted, type PropType, ref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkDialog } from '@/components/common/RxkDialog'
import { getQuoteValue } from '@/apis/objectManage'
import { isEmpty } from '@/utils/is'
import { fieldTypeEnum } from '@/enums/preObjectManage'

const props = defineProps({
  ...basicProps,
  quoteMappingConfig: {
    type: Object as PropType<Recordable>,
    default: () => {}
  },
  formData: {
    type: Object as PropType<Recordable>,
    default: () => { }
  },
  index: {
    type: Number,
    default: 0
  }
})
const loading = ref(false)
const showDialog = ref(false)
const allListValue = ref<Recordable[]>([])

const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})

// 当前关联字段是弹窗显示还是下拉搜索显示
const isShowDialog = computed(() => {
  console.log(props.data, 'opop')
  return false
})
function handleShow () {
  showDialog.value = true
}

const list = ref<Recordable[]>([])

// 该状态决定是否根据其他引用或者映射字段改变传递进来的list
const sendVisible = ref(false)
// let flag = ref(true)
onMounted(() => {
  console.log(`refreshMapping${props.data.columnQuoteInfo?.group}`, '`refreshMapping${props.data.columnQuoteInfo?.group}`')
  props.renderConfig.fieldClass.subTableEventEmitter.on(`refreshMapping${props.data.columnQuoteInfo?.group}`, (field: Recordable, fn: Fn, arr: Recordable[], res: Recordable[], index: any) => {
    // 只执行field与当前字段相同的，所以要加判断
    if (props.data.fieldSerial === field.fieldSerial && props.index === index) {
      innerValue.value = fn()
      list.value = arr || []
      sendVisible.value = true
      allListValue.value = res
      // if (flag.value) {
      //   flag.value = false
      //   relevanceOtherField()
      // }
    }
  })
})

// 正查
function remoteMethod (query: string) {
  if (query !== '') {
    loading.value = true
    // group相同的引用字段和映射字段的集合
    const sameGroup = getGroupField(props.data.columnQuoteInfo?.group)
    const quoteColumnList = sameGroup.map((field) => {
      return {
        columnSerial: field.columnSerial,
        quoteColumnSerial: field.columnQuoteInfo.quoteColumnSerial,
        unionQueryColumnSerials: field.columnQuoteInfo.unionQueryColumnSerials || '',
        queryValue: ''
      }
    })
    console.log(props.quoteMappingConfig.quoteFieldList, 'props.quoteMappingConfig.quoteFieldList', props.data)
    // 找到该映射字段对应的引用字段
    const quoteField = props.quoteMappingConfig.quoteFieldList.find(item => item.columnQuoteInfo.group === props.data.columnQuoteInfo?.group)
    console.log(quoteField, 'quoteField')
    const conditionInfo = JSON.parse(JSON.stringify(quoteField.columnQuoteInfo.conditionInfo)) || null
    if (conditionInfo) {
      conditionInfo.conditionList.forEach((condis: Recordable) => {
        condis.conditions.forEach((condi: Recordable) => {
          // 如何去拿当前条件对应基础字段对应的值
          const field = findField(condi.rightColumnSerial)
          if (!isEmpty(field)) {
            condi.rightValue = props.formData[field.fieldCode] || ''
          }
        })
      })
    }
    const postData = {
      quoteTableCode: props.data.columnQuoteInfo.quoteTableCode, // 关联表单序列号
      quoteColumnList,
      conditionInfo
    }
    getQuoteValue(postData).then((res) => {
      allListValue.value = res
      loading.value = false
      list.value = res.map((item: Recordable) => {
        const key = Object.keys(item).find(key => (key === props.data.fieldSerial))
        if (key) {
          const val = item[key]
          return {
            label: val,
            value: val,
            id: item.id
          }
        }
      })
      sendVisible.value = false
    })
  }
}

const currentObj = ref<Recordable>({})
function getCurrentObj (id: string) {
  console.log(allListValue, 'allListValue123', id)
  const current = allListValue.value.find(item => item.id === id)
  if (current) {
    currentObj.value = current
  }
  console.log(currentObj, 'currentObj666')
}

// 修改值
function changeValue (value: any) {
  console.log(value, 'pppp')
  // 取出当前选择的数据所在的对象
  const current = list.value.find(item => item.value === value)
  console.log(current, 'current4446', allListValue)
  if (current) {
    getCurrentObj(current.id)
  }
  // nextTick(() => {
  //   relateMappingField(val => () => val)
  //   relevanceOtherField()
  // })
  relateMappingField(val => () => val)
  relevanceOtherField()
}
// 修改引用和其他映射字段的值
function relateMappingField (fn?: Fn) {
  // 要给group相同的排除自己之后所有的映射字段赋值
  const mappingFieldList = props.quoteMappingConfig.mappingFieldList.filter(item => item.fieldSerial !== props.data.fieldSerial)
  const relateMappingFields: Recordable[] = []
  // 取出与当前引用字段的group相同的映射字段
  mappingFieldList.forEach((i: Recordable) => {
    if (i.columnQuoteInfo.group === props.data.columnQuoteInfo.group) {
      relateMappingFields.push({ ...i })
    }
  })
  console.log(relateMappingFields, 'relateMappingFields123')
  if (relateMappingFields.length > 0) {
    relateMappingFields.forEach(field => {
      // 不仅要刷新映射字段对应的值，而且还要传递映射字段的下拉数据，如果映射字段的下拉有多个，那映射字段对应的值取第一个
      console.log(allListValue, 'allListValue777')
      const arr = allListValue.value.map(item => {
        const key = Object.keys(item).find(key => (key === field.fieldSerial))
        if (key) {
          const val = item[key]
          return {
            label: val,
            value: val,
            id: item.id
          }
        }
      })
      console.log(currentObj, 'currentObj444666', arr)
      props.renderConfig.fieldClass.subTableEventEmitter.emit(`refreshMapping${props.data.columnQuoteInfo?.group}`, field, fn && fn(currentObj.value[field.fieldSerial]), arr, allListValue.value, props.index)
    })
  }

  // 要给当前映射字段对应的引用字段赋值
  const quoteFieldList = props.quoteMappingConfig.quoteFieldList
  const relateQuoteFields: Recordable[] = []
  // 取出与当前映射字段的group相同的引用字段
  quoteFieldList.forEach((i: Recordable) => {
    if (i.columnQuoteInfo.group === props.data.columnQuoteInfo.group) {
      relateQuoteFields.push({ ...i })
    }
  })
  console.log(relateQuoteFields, 'relateQuoteFields123')
  if (relateQuoteFields.length > 0) {
    relateQuoteFields.forEach(field => {
      const arr = allListValue.value.map(item => {
        const key = Object.keys(item).find(key => (key === field.fieldSerial))
        if (key) {
          const val = item[key]
          return {
            label: val,
            value: val,
            id: item.id
          }
        }
      })
      props.renderConfig.fieldClass.subTableEventEmitter.emit(`refreshQuote${props.data.columnQuoteInfo?.group}`, field, fn && fn(currentObj.value[field.fieldSerial]), arr, allListValue.value, props.index)
    })
  }
}

// 修改与当前组关联的其他组的信息
function relevanceOtherField () {
  // 去查询关联得其他组，并做回显
  // relevanceQuoteList：其他组引用字段
  // flag.value = false
  const { relevanceQuoteList, relevanceFieldSerials } = relevanceFn()
  console.log(relevanceQuoteList, 'relevanceQuoteList111')
  console.log(relevanceFieldSerials, 'relevanceFieldSerials222')
  if (relevanceQuoteList.length) {
    console.log(currentObj, 'currentObj')
    relevanceQuoteList.forEach((item: Recordable, index: any) => {
      // group相同的引用字段和映射字段的集合
      const sameGroup = getGroupField(item.columnQuoteInfo.group)
      console.log(sameGroup, 'sameGroupoooo')
      // 拿到当前引用字段，去调接口，拿到该组的所有数据，分别去触发引用字段和映射字段的回显
      const quoteColumnList = sameGroup.map((field) => {
        return {
          columnSerial: field.columnSerial,
          quoteColumnSerial: field.columnQuoteInfo.quoteColumnSerial,
          unionQueryColumnSerials: field.columnQuoteInfo.unionQueryColumnSerials || '',
          queryValue: currentObj.value[field.fieldSerial] || ''
        }
      })
      // 要处理对应条件中rightValue的值
      const conditionInfo = JSON.parse(JSON.stringify(item.columnQuoteInfo.conditionInfo)) || null
      console.log(conditionInfo, 'conditionInfo222')
      if (conditionInfo) {
        conditionInfo.conditionList.forEach((condis: Recordable) => {
          condis.conditions.forEach((condi: Recordable) => {
            // 如何去拿当前条件对应基础字段对应的值
            const field = findField(condi.rightColumnSerial)
            if (!isEmpty(field)) {
              condi.rightValue = props.formData[field.fieldCode] || ''
            } else {
              condi.rightValue = currentObj.value[condi.rightColumnSerial] || ''
            }
          })
        })
      }
      const postData = {
        quoteTableCode: item.columnQuoteInfo.quoteTableCode, // 关联表单序列号
        quoteColumnList,
        conditionInfo
      }
      console.log(postData, 'postData')
      getQuoteValue(postData).then((res) => {
        console.log(res)
        // 获取到当前引用字段对应的所有映射字段
        const relateMappingFields = getMappingFieldList(item.columnQuoteInfo.group)
        if (relateMappingFields.length > 0) {
          relateMappingFields.forEach(field => {
            // 不仅要刷新映射字段对应的值，而且还要传递映射字段的下拉数据，如果映射字段的下拉有多个，那映射字段对应的值取第一个
            const arr = res.map(item => {
              const key = Object.keys(item).find(key => (key === field.fieldSerial))
              if (key) {
                const val = item[key]
                return {
                  label: val,
                  value: val,
                  id: item.id
                }
              }
            })
            const fn = (val: any) => () => val
            // 触发刷新当前引用字段对应的映射字段的值
            props.renderConfig.fieldClass.subTableEventEmitter.emit(`refreshMapping${item.columnQuoteInfo.group}`, field, fn(arr[0]?.value), arr, res, props.index)
          })
        }
        // 处理引用字段的值
        const arr = res.map(item1 => {
          const key = Object.keys(item1).find(key => (key === item.fieldSerial))
          if (key) {
            const val = item1[key]
            return {
              label: val,
              value: val,
              id: item.id
            }
          }
        })
        console.log(arr, 'arr222')
        const fn = (val: any) => () => val
        props.renderConfig.fieldClass.subTableEventEmitter.emit(`refreshQuote${item.columnQuoteInfo?.group}`, item, fn && fn(arr[0]?.value), arr, res, props.index)
      })
    })
  }
}

// 取出与当前引用字段的group相同的映射字段
function getMappingFieldList (group: string) {
  // 要给group相同的所有的映射字段赋值
  const mappingFieldList = props.quoteMappingConfig.mappingFieldList
  const relateMappingFields: Recordable[] = []
  // 取出与当前引用字段的group相同的映射字段
  mappingFieldList.forEach((i) => {
    if (i.columnQuoteInfo.group === group) {
      relateMappingFields.push({ ...i })
    }
  })
  return relateMappingFields
}

// 根据当前引用或者映射字段，找到group相同的一组字段
function getGroupField (group: string) {
  console.log(props.positions, 'props.positions111')
  const sameGroup: Recordable[] = []
  props.positions.forEach(container => {
    if (container.code === 'TABLE') {
      container.children.forEach((child: Recordable) => {
        child.modules.forEach((module: Recordable) => {
          module.componentList.forEach((field: Recordable) => {
            if (field?.columnQuoteInfo?.group === group) {
              sameGroup.push({ ...field })
            }
          })
        })
      })
    }
    if (container.code === 'BLANK') {
      container.modules.forEach((module: Recordable) => {
        module.componentList.forEach((field: Recordable) => {
          if (field?.columnQuoteInfo?.group === group) {
            sameGroup.push({ ...field })
          }
        })
      })
    }
  })
  return sameGroup
}
// 通过当前组去匹配其他组是否有关联
function relevanceFn () {
  // 有关联的其他组的引用字段数据组
  const relevanceQuoteList: Recordable[] = []
  // 匹配成功的fieldSerial
  const relevanceFieldSerials: string[] = []
  // 当前组的映射字段
  const relateMappingFields = getMappingFieldList(props.data.columnQuoteInfo.group)
  // 要找到当前映射字段对应的引用字段
  const currentQuote = getCurrentQuoteField(props.data.columnQuoteInfo.group)
  // 排除当前引用字段之后剩余的其他引用字段,
  const quoteFieldList = props.quoteMappingConfig.quoteFieldList.filter((item: Recordable) => item.fieldSerial !== currentQuote.fieldSerial)
  quoteFieldList.forEach(item => {
    const columnQuoteInfo = item.columnQuoteInfo
    // 正查
    if (columnQuoteInfo && columnQuoteInfo.conditionInfo && columnQuoteInfo.conditionInfo.conditionList && columnQuoteInfo.conditionInfo.conditionList.length) {
      columnQuoteInfo.conditionInfo.conditionList.forEach(condition => {
        condition.conditions.forEach(condi => {
          // 当前引用字段是否和条件匹配
          if (currentQuote.fieldSerial === condi.rightColumnSerial) {
            const fieldSerialIndex = relevanceFieldSerials.findIndex(fieldSerial => fieldSerial === condi.rightColumnSerial)
            if (fieldSerialIndex === -1) {
              relevanceFieldSerials.push(condi.rightColumnSerial)
            }
            const relevanceIndex = relevanceQuoteList.findIndex(relevance => relevance.fieldSerial === item.fieldSerial)
            if (relevanceIndex === -1) {
              relevanceQuoteList.push(item)
            }
          }
          // 当前其他映射字段是否和条件匹配
          const current = relateMappingFields.find(mapping => mapping.fieldSerial === condi.rightColumnSerial)
          if (current) {
            const fieldSerialIndex = relevanceFieldSerials.findIndex(fieldSerial => fieldSerial === condi.rightColumnSerial)
            if (fieldSerialIndex === -1) {
              relevanceFieldSerials.push(condi.rightColumnSerial)
            }
            const relevanceIndex = relevanceQuoteList.findIndex(relevance => relevance.fieldSerial === item.fieldSerial)
            if (relevanceIndex === -1) {
              relevanceQuoteList.push(item)
            }
          }
        })
      })
    }
    // 反查，根据当前编辑的映射字段找到对应的引用字段的条件去查抄其他group组字段相关
    const currentColumnQuoteInfo = currentQuote.columnQuoteInfo
    if(currentColumnQuoteInfo && currentColumnQuoteInfo.conditionInfo && currentColumnQuoteInfo.conditionInfo.conditionList && currentColumnQuoteInfo.conditionInfo.conditionList.length) {
      // 这是当前编辑的引用字段的条件
      currentColumnQuoteInfo.conditionInfo.conditionList.forEach(condition => {
        condition.conditions.forEach(condi => {
          // 根据当前条件去匹配其他group的字段的fieldSerial
          // 这是其他group对应的映射字段list
          const otherMappingList = getMappingFieldList(item.columnQuoteInfo.group)
          const current = otherMappingList.find(mapping => mapping.fieldSerial === condi.rightColumnSerial)
          if (current) {

            const fieldSerialIndex = relevanceFieldSerials.findIndex(fieldSerial => fieldSerial === condi.rightColumnSerial)
            if (fieldSerialIndex === -1) {
              relevanceFieldSerials.push(current.fieldSerial)
            }

            const relevanceIndex = relevanceQuoteList.findIndex(relevance => relevance.fieldSerial === item.fieldSerial)
            if (relevanceIndex === -1) {
              relevanceQuoteList.push(item)
            }
          }
          // 其他group的引用字段. item就是其他引用字段
          if(condi.rightColumnSerial === item.fieldSerial) {

            const fieldSerialIndex = relevanceFieldSerials.findIndex(fieldSerial => fieldSerial === condi.rightColumnSerial)
            if (fieldSerialIndex === -1) {
              relevanceFieldSerials.push(item.fieldSerial)
            }

            const relevanceIndex = relevanceQuoteList.findIndex(relevance => relevance.fieldSerial === item.fieldSerial)
            if (relevanceIndex === -1) {
              relevanceQuoteList.push(item)
            }
          }
        })
      })
    }
  })
  console.log(relevanceQuoteList, 'relevanceQ2233')
  return { relevanceQuoteList, relevanceFieldSerials }
}

// 根据当前映射字段的group，去找到该group对应的引用字段
function getCurrentQuoteField (group: string) {
  return props.quoteMappingConfig.quoteFieldList.find((quote: Recordable) => quote.columnQuoteInfo.group === group)
}

// 根据传入的serial去找到对应的字段，该字段必须是普通字段
function findField (columnSerial: string): Recordable {
  let obj: Recordable = {}
  props.positions.forEach(container => {
    if (container.code === 'BLANK') {
      container.modules.forEach((module: Recordable) => {
        module.componentList.forEach((field: Recordable) => {
          if (field.fieldSerial === columnSerial &&
              field.fieldType !== fieldTypeEnum.SYS_QUOTE &&
              field.fieldType !== fieldTypeEnum.CUSTOM_QUOTE &&
              field.fieldType !== fieldTypeEnum.SYS_MAPPING &&
              field.fieldType !== fieldTypeEnum.CUSTOM_MAPPING &&
              field.fieldType !== fieldTypeEnum.SON_SYS_QUOTE &&
              field.fieldType !== fieldTypeEnum.SON_CUSTOM_QUOTE &&
              field.fieldType !== fieldTypeEnum.SON_SYS_MAPPING &&
              field.fieldType !== fieldTypeEnum.SON_CUSTOM_MAPPING
          ) {
            obj = field
            return
          }
        })
      })
    }
    if (container.code === 'TABLE') {
      container.children.forEach(child => {
        child.modules.forEach(module => {
          module.componentList.forEach((field: Recordable) => {
            if (field.fieldSerial === columnSerial &&
                field.fieldType !== fieldTypeEnum.SYS_QUOTE &&
                field.fieldType !== fieldTypeEnum.CUSTOM_QUOTE &&
                field.fieldType !== fieldTypeEnum.SYS_MAPPING &&
                field.fieldType !== fieldTypeEnum.CUSTOM_MAPPING &&
                field.fieldType !== fieldTypeEnum.SON_SYS_QUOTE &&
                field.fieldType !== fieldTypeEnum.SON_CUSTOM_QUOTE &&
                field.fieldType !== fieldTypeEnum.SON_SYS_MAPPING &&
                field.fieldType !== fieldTypeEnum.SON_CUSTOM_MAPPING
            ) {
              obj = field
              return
            }
          })
        })
      })
    }
  })
  return obj
}

</script>
<style lang="scss" scoped>
.quote-box {
  position: relative;
  width: 100%;
  .showDialogOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    bottom: 0;
  }
}
</style>