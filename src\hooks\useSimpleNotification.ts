import { ref } from 'vue'

export function useSimpleNotification (){
  const instance = ref<any>(null as any)
  function open (options:any = null){
    console.log(instance.value)
    if(instance.value) return
    // @ts-ignore
    instance.value = ElNotification.error({
      // title: '授权失败',
      // message: '登录凭证已过期，请重新登录！',
      // showClose: false,
      // duration: 1500,
      ...options
    })
    setTimeout(() => {
      instance.value = null
    }, options.duration || 1500 )
  }
  return {
    open
  }
}