import { type FormConfig, renderRows } from './FormItems'
import { defineComponent, ref, type PropType } from 'vue'
import { ElForm } from 'element-plus'
const RxkProForm = defineComponent({
  props: {
    formData: Object,
    formConfig: Object as PropType<FormConfig> // defineComponent标注类型比较特殊
  },
  setup(props, context) {
    const formRef = ref()
    const submitForm = () => {
      return new Promise(async (resolvue) => {
        if (!formRef) {
          resolvue({
            type: false,
            data: '获取dom失败'
          })
        }
        console.log(formRef)
        // TODO: 在这里一定要注意加上.value
        await formRef.value.validate((valid: boolean, fields: any) => {
          if (valid) {
            console.log('submit!')
            resolvue({
              type: true,
              data: props.formData
            })
          } else {
            resolvue({
              type: false,
              data: fields
            })
          }
        })
      })
    }
    const resetFields = () => {
      if (!formRef) return
      formRef.value.resetFields()
    }
    // 使用 expose 暴露组件内部的方法
    context.expose({
      submitForm,
      resetFields,
      formRef
    })
    const autoPlaceholder = () => {
      props.formConfig?.colsList.forEach((elItem) => {
        // 设置默认placeholder和校验提示
        let placeholder = ''
        const isInput = ['el-input', 'el-input-number'].includes(elItem.type)
        if (isInput) {
          placeholder = `请输入${elItem.label}`
        } else {
          placeholder = `请选择${elItem.label}`
        }
        if (!elItem.bindObj) {
          elItem.bindObj = { placeholder }
        } else {
          if (!elItem.bindObj.placeholder) {
            elItem.bindObj.placeholder = placeholder
          }
        }
        if (elItem.required) {
          const ruleItem = {
            required: true,
            message: placeholder,
            trigger: isInput ? 'blur' : 'change'
          }
          if (elItem.rules) {
            elItem.rules.push(ruleItem)
          } else {
            elItem.rules = [ruleItem]
          }
        }
      })
    }
    autoPlaceholder()

    return () => (
      <ElForm
        ref={formRef}
        {...props.formConfig?.formConfigBind}
        model={props.formData}
      >
        {renderRows(
          props.formConfig as FormConfig,
          props.formData as Object,
          context
        )}
      </ElForm>
    )
  }
})
export default RxkProForm
