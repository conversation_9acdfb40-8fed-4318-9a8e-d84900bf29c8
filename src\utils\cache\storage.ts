import { aes } from '@/utils/aes'
import { isNullOrUnDef } from '@/utils/is'
export interface CreateStorageParams {
    preKey: string;
    storage: Storage;
    hasEncrypt: boolean;
    timeout?: number | null;
}
/**
 * 创建storage实例对象
 * */
export function createStorage ({
  storage = localStorage,
  preKey = '',
  timeout = null,
  hasEncrypt = true
}: Partial<CreateStorageParams>) {
  // WebStorage类
  class WebStorage {
    public storage: Storage
    public preKey: string
    readonly hasEncrypt: boolean
    constructor () {
      this.storage = storage
      this.preKey = preKey
      this.hasEncrypt = hasEncrypt
    }
    getKey (key: string) {
      return `${this.preKey}${key}`.toUpperCase()
    }
    set (key: string, value:any, expire: number | null = timeout) {
      const stringData = JSON.stringify({
        value,
        time: Date.now(),
        expire: !isNullOrUnDef(expire) ? new Date().getTime() + expire * 1000 : null
      })
      const aesData = this.hasEncrypt ? aes.encrypt(stringData) : stringData
      this.storage.setItem(this.getKey(key), aesData)
    }
    get (key: string, def: any = null): any {
      const val = this.storage.getItem(this.getKey(key))
      if(!val) return
      try {
        const aceVal = this.hasEncrypt ? aes.decrypt(val) : val
        const data = JSON.parse(aceVal)
        const { value, expire } = data
        // expire 为null，或者大于当前时间则返回数据
        if (isNullOrUnDef(expire) || expire >= new Date().getTime()) {
          return value
        } else {
          // 删除当前缓存
          this.remove(key)
        }
      }catch (e) {
        return def
      }
    }
    remove (key: string) {
      this.storage.removeItem(this.getKey(key))
    }

  }
  return new WebStorage()
}
