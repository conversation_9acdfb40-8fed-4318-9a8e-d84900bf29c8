<template>
  <div class="organizational">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索部门"
        clearable
        :prefix-icon="Search"/>
      <div class="treeList">
        <el-tree 
          ref="treeRef"
          :data="treeData"
          node-key="id"
          :filter-node-method="filterNode"
          :check-strictly="true"
          :expand-on-click-node="false"
          :props="defaultProps"
          @node-expand="nodeToggle"
          @node-collapse="nodeToggle">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="custom-tree-node-item" :class="data.id === treeData?.[0].id ? 'treetop' : ''">
                <div class="custom-tree-node-item-block">
                  <span @click.stop>
                    <el-checkbox 
                      v-model="data.checked"
                      :disabled="data.disabled"
                      :indeterminate="data.indeterminate"
                      style="margin-right: 8px;"
                      @change="(val:boolean) => changeDepartCheck(val, data, node)"
                    />
                  </span>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="`${data.name}${!onlyDep ? ('(' + getLength(data) + ')') : ''}`"
                    placement="top-start"
                  >
                    <span>{{data.name}}
                      <template v-if="!onlyDep">({{getLength(data)}})</template>  
                    </span>
                  </el-tooltip>
                </div>
                <!-- 只在第一层展示 -->
                <span class="treeoperate" v-if="data.id === treeData?.[0].id" @click.prevent="handleToggle">
                  {{treeStatue ? '收起' : '展开'}}子部门
                </span>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { ref, watch, unref, computed, type PropType } from 'vue'
import type { ElTree } from 'element-plus'
import { recursion } from '@/utils/tools'
import type { DeComponentTreeVO, UserVOList } from '../type'

const emit = defineEmits(['update:data', 'updateValue'])
const treeRef = ref<InstanceType<typeof ElTree>>()
const treeStatue = ref<boolean>(false) // 组织架构展开收起状态，默认收起
const personnelSelectionInput = ref('')
const defaultProps = {
  disabled: (data: { number: Number }) => {
    return !data.number
  }
}

const props = defineProps({
  data: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  },
  selectedData: { // 已选择项
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  },
  allChecked: Boolean,
  isNoIncludeChild: Boolean,
  onlyDep: {
    type: Boolean,
    default: false
  }
})

const treeData = computed({
  get: () => {
    return unref(props.data)
  },
  set: (newValue) => {
    emit('update:data', newValue )
  }
})

function setDefaultData (list:any[] = []){
  // 对list做兼容
  const ids = list?.map(el => {
    if(el instanceof Object){
      return el?.value
    }
    if(typeof el === 'string'){
      return el
    }
  })
  recursion(unref(treeData), (item) => {
    if(ids.includes(item.id)){
      item.checked = true
    }
  })
  emit('updateValue', getCheckList())
}

function getLength (data:Recordable){
  return data?.userVOList?.length || 0
}

/**
 * 复选框点击
 */
async function changeDepartCheck (val: boolean, data: Recordable) {
  checkDepartList(data)
  emit('updateValue', getCheckList())
}

/**
 * 选中子节点
 */
function checkDepartList (data: Recordable) {
  // 父部门是否包含子部门
  if(!props.isNoIncludeChild){ // 包含处理
    data?.children?.length && data.children.forEach((child:Recordable) => {
      child.checked = data.checked
      if(child?.children?.length){
        checkDepartList(child)
      }
    })
  } else {
    // 不做任何处理
  }
}

/**
 * 展开收起全部
 */
function handleToggle () {
  treeStatue.value = !treeStatue.value
  let nodesData = treeRef.value?.store.nodesMap
  for (let i in nodesData) {
    if(nodesData[i].childNodes && nodesData[i].childNodes.length > 0) {
      nodesData[i].expanded = treeStatue.value
    }
  }
}

function nodeToggle () {
  setTimeout(() => {
    const nodesData = treeRef.value?.store.nodesMap
    let expandedVal = false
    for (let i in nodesData) {
      if(nodesData[i].expanded) {
        expandedVal = true
      }
    }
    treeStatue.value = expandedVal
  }, 0)
}

const filterNode = (value: string, data: Recordable) => {
  if (!value) return true
  return data?.name.includes(value)
}

watch(personnelSelectionInput, (val) => {
  treeRef.value!.filter(val)
})

watch(() => props.allChecked, (val) => {
  handleAllCheck(val)
})

/**
 * 选中可见选项 ，自动勾选所有展开项的成员，取消勾选后则取消已勾选的数据
 */
async function handleAllCheck (val:boolean){
  if(!val) {
    // 清空
    clearAll()
  }
  if(val) {
    const nodesMap = treeRef.value?.store.nodesMap
    Object.values(nodesMap as Recordable).forEach(node => {
      if(node.expanded) {
        node.data.checked = true
        node?.data?.children?.forEach((child:Recordable) => {
          child.checked = true
          checkDepartList(child)
        })
      }
    })
  }
  emit('updateValue', getCheckList())
}

function clearAll (){
  recursion(unref(treeData), (item) => {
    item.checked = false
  })
}

function unChecked (id:string){
  recursion(unref(treeData), (item) => {
    if(item.id === id) item.checked = false
  })
  emit('updateValue', getCheckList())
}

// 获取已选中的项
function getCheckList (){
  let list:Recordable[] = []
  recursion(unref(treeData), (item) => {
    item.checked && list.push({ value: item.id, label: item.name })
  })
  return list
}

defineExpose({
  unChecked,
  setDefaultData
})

</script>

<style lang="scss" scoped>
.organizational {
  @include flex-center(row, normal, normal);
  height: 100%;
  .left {
    width: 100%;
    border-right: 1px solid #EBEEF5;
    padding: 16px 16px 16px 0;
    .custom-tree-node {
      line-height: 12px;
      // display: inline-block;
      // min-width: 100%;
      &-item {
        .treelabel {
          width: 100%;
          :deep(.custom-tooltip-content) {
            text-align: left !important;
          }
        }
        .treeoperate {
          font-size: $font-size-mini;
          color: $primary-color;
          margin-right: 16px;
        }
        &-block {
          @include flex-center(row, space-between, center);
        }
      }
      .treetop {
        @include flex-center(row, space-between, center);
        width: 180px;
      }
    }
    .treeList {
      overflow-y: auto;
      height: calc(100% - 32px);
      :deep(.el-tree-node.is-expanded>.el-tree-node__children) {
        display: block;
        min-width: 100%;
      }
    }
  }
  .personnelSelectionInput {
    border-radius: 4px;
    :deep(.el-input__wrapper) {
      box-shadow: none;
      background: #F4F4F5;
    }
    margin-bottom: 13px;
  }
}
</style>
