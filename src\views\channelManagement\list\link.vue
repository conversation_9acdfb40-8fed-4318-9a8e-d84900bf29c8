<template>
  <CommonLinkList
    v-if="!state.isShow"
    :tableProConfig="state.tableProConfig"
    :api="state.api"
    :type="state.type"
    :exportType="state.exportType"
    :settleType="state.settleType"
    :encodeId="state.encodeId"
    :showCityVisitorStatistics="state.type === 1 && state.filedType === 1"
    :exportApi="fetchGetChannelLinkStatisticsListExport"
    @checkCityVisitorStatistics="checkCityVisitorStatistics"
  />
  <el-dialog
    custom-class="extraction-code"
    v-model="state.isShow"
    :showClose="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="config.layout.shrink"
    title="车生活管理后台"
    width="480"
  >
    <p>分享已加密，请输入提取码进入网页</p>
    <el-input v-model="state.extractionCode" />

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" size="large" @click="handleSure">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    custom-class="city-visitor-statistics-dialog"
    v-model="state.showCityVisitorStatistics"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    title="渠道城市获客统计"
    width="1000"
  >
    <div class="flex-table">
      <RxkVTable @register="registerTable" />
    </div>
  </el-dialog>
</template>

<script setup lang="tsx">
import { reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import CommonLinkList from './components/commonLinkList.vue'
import { useTablePro } from './hooks/useTablePro'
import { useRoute } from 'vue-router'
import { getCustomerCityCount } from '@/apis/marketingData'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useConfig } from '@/stores/modules/config'
import { fetchCheckExtractionCode, fetchGetChannelStatisticsList, fetchGetChannelLinkStatisticsListExport } from '@/apis/carlife'
import timeUtils from '@/utils/libs/time'

const config = useConfig()
const route = useRoute()

const tableProConfig = {
  searchFormData: [
    {
      key: 'timeRange',
      fieldName: '日期范围',
      component: 'DateRange',
      val: timeUtils.transTypeToTime(60, 'yyyy-MM-dd')
    }
  ],
  columns: [
    { key: 'day', title: '日期' },
    { key: 'channelId', title: '渠道ID' },
    { key: 'channelName', title: '渠道名称' },
    { key: 'consumeAmount', title: '结算数' }
  ]
}

const state = reactive({
  isShow: true,
  encodeId: '',
  extractionCode: '',
  settleType: '1',
  tableProConfig,
  api: fetchGetChannelStatisticsList,
  checkApi: fetchCheckExtractionCode,
  type: 1, // 渠道商链接类型（0：单个渠道，1：渠道组）
  exportType: 1, // 导出类型(1:信息流渠道，2：普通渠道)
  filedType: 0, // 字段类型 0 普通结算 1 信息流结算
  showCityVisitorStatistics: false,
  currentChannelId: ''
})
const currentType = route.query.settleType === 'link' ? 1 : 0
const { cityVisitorStatistics } = useTablePro(currentType)
const [
  registerTable,
  { reload, setSearchInfo }
] = useTable({
  api: getCustomerCityCount,
  columns: cityVisitorStatistics.columns,
  showSetting: false,
  immediate: false, // 是否立刻请求
  customTableConfig: false
})
const handleSure = async () => {
  if (!state.extractionCode) {
    ElMessage({
      type: 'warning',
      message: '请输入提取码'
    })
    return
  }
  const res = await state.checkApi({
    encodeId: state.encodeId,
    checkExtractionCode: state.extractionCode
  })
  state.filedType = res?.filedType || 0
  state.type === 1 
  state.isShow = false
}

const init = () => {
  state.encodeId = route.query.encodeId as string
  state.settleType = route.query.settleType as string
}
function checkCityVisitorStatistics (row: Recordable, searchInfo: Recordable){
  if(state.type === 1 && state.filedType === 1 && row?.channelId){
    state.showCityVisitorStatistics = true
    state.currentChannelId = row?.channelId
    nextTick(() => {
      setSearchInfo({
        model: {
          channelIds: [row?.channelId],
          startDay: searchInfo.model.startDay,
          endDay: searchInfo.model.endDay
        }
      })
      reload()
    })
  }
}
onMounted(() => {
  init()
})
</script>

<style lang="scss" scoped></style>
<style lang="scss">
.extraction-code {
  .el-dialog__header {
    text-align: center;
    margin-right: 0;
  }
  .el-dialog__body {
    padding: 20px;
  }
  p {
    padding-bottom: 20px;
    text-align: center;
  }
  .dialog-footer {
    .el-button {
      width: 100%;
    }
  }
}
.city-visitor-statistics-dialog{
  max-height: 75vh;
  height: 100%;
  overflow: hidden;
  @include flex-center(column, normal, normal);
  .el-dialog__body {
    flex: 1;
    padding: 20px 0;
    overflow: hidden;
    @include flex-center(column, normal, normal);
  }
  .flex-table{
    flex: 1;
    overflow: hidden;
  }
}
</style>
