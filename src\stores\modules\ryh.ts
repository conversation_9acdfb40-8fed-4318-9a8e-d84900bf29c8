import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useRYHStore = defineStore('useRYHStore', () => {
  const strategyQuery = ref<Recordable>()
  const setStrategyQuery = (data: Recordable<any> | undefined) => {
    strategyQuery.value = data
  }
  const clearStrategyQuery = () => {
    strategyQuery.value = {}
  }
  return {
    strategyQuery,
    setStrategyQuery,
    clearStrategyQuery
  }
})
