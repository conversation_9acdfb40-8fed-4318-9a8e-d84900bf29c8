<template>
  <div class="personnelSelection">
    <!-- 自定义触发内容 -->
    <div v-if="custom"
         style="width: inherit;"
         ref="customRef">
      <slot name="custom"/>
    </div>
    <div v-else style="width: inherit;">
      <!-- 默认触发内容 -->
      <el-select 
        style="width: 100%"
        v-model="showValue"
        ref="selectRef"
        @focus="focus"
        :collapse-tags="collapseTags " 
        multiple
        @visible-change="visibleChange"
        @remove-tag="removeTag"
        v-bind="$attrs"
        :disabled="disabled"
        placeholder="请选择">
        <el-option 
          v-for="(item, index) in personelTree.data"
          :key="index"
          :label="item.name"
          :disabled="item.disable"
          :value="item.id"/>
      </el-select>
    </div>
    
    <el-popover
      :visible="custom ? innerVisible : undefined"
      :virtual-ref="virtualRef"
      ref="popoverRef"
      trigger="click"
      title=""
      popper-class="personnelSelectionPopper"
      width="600"
      :placement="placement"
      destroy-on-close
      :popper-options="{
        modifiers: [{enabled:true}],
        strategy: 'fixed',
        placement:'auto'
      }"
      virtual-triggering>
      <SelectPanel 
        @registerPanel="registerPanel"
        @close="close"
        @update="update"
        @confirm="confirm">
        <template #dynamic>
          <slot name="dynamic"/>
        </template>
      </SelectPanel>
    </el-popover>
  </div>
</template>

<script lang="ts" setup>
import {
  ref, unref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import SelectPanel from './selectPanel.vue'
import type { BasicProps, DeComponentTreeVO } from './type.ts'
import { isFunc } from '@/utils/is'
import { usePanel } from './hooks/usePanel'

const emit = defineEmits(['update:visible', 'update:value', 'sure', 'getDeptList'])
const personelTree = reactive<any>({
  data: {}
})

const popoverRef = ref()
const selectRef = ref()
const customRef = ref()
const virtualRef = ref()
const showPanel = ref<boolean>(false)
const showValue = ref<string[]>([])
const loadNum = ref<number>(0)

const innerVisible = computed({
  get: () => props.visible,
  set: (visible: boolean) => emit('update:visible', visible)
})

const props = withDefaults(defineProps<BasicProps>(), {
  custom: false,
  visible: false,
  placeholder: '请选择',
  placement: 'bottom',
  collapseTags: false,
  disabled: false,
  menuId: ''
})

const [registerPanel, { init, updateValue, getCheckedDep, getContainChildren, getParameter, getSelectedShow, reload, handleCheckedValue }] = usePanel(unref(props))

watch(() => props, () => {
  console.log(props.showValueData, '组件已渲染DD', loadNum.value, props.visible)
  if(!props.disabled) {
    virtualRef.value = props.custom ? customRef.value : selectRef.value
  } else {
    unref(popoverRef)?.hide?.()
    virtualRef.value = ''
  }

  const fn = () => {
    nextTick(() => {
      init(unref(props))
      loadNum.value++
    })
  }
  if (loadNum.value === 0) {
    if ((props.showValueData && props.showValueData.length > 0) || props.data || props.visible) {
      fn()
    }
  } else {
    if (props.data && !props.api) {
      fn()
    } else {
      handleCheckedValue(props.showValueData || [])
    }
  }
}, {
  deep: true,
  immediate: true
})

function focus (){
  selectRef.value.blur()
}

async function visibleChange (){
  console.log(props.disabled)
  selectRef.value.blur()
  if(props.visibleBeforeFn && isFunc(props.visibleBeforeFn)) {
    const flag = await props.visibleBeforeFn()
    if(!flag){
      close()
    }
  } else {
    if(loadNum.value === 0) {
      init(props)
      loadNum.value++
    } else {
      reload(props)
    }
  }
  unref(popoverRef).popperRef?.delayHide?.()
  showPanel.value = true
}

function close () {
  if(props.custom) {
    innerVisible.value = false
  } else {
    unref(popoverRef).hide?.()
  }
  showPanel.value = false
}

function getData (){
  return {
    departmentList: getCheckedDep(),
    dynamicParameters: getParameter(),
    selectedShow: getSelectedShow(),
    containChildren: getContainChildren()
  }
}

function update (data: DeComponentTreeVO[]){
  console.log('update444')
  personelTree.data = data || []
  showValue.value = data.map(item => item.id) || []
  emit('getDeptList', data)
}

function confirm () {
  personelTree.data = getCheckedDep() || []
  showValue.value = getCheckedDep().map(item => item.id) || []
  emit('sure', getData())
  close()
}

function removeTag (tagValue: any) {
  console.log(tagValue)
  const checkedValue = unref(getCheckedDep())
  const findIndex = checkedValue.findIndex(item => item.id === tagValue)
  if(findIndex !== -1) {
    checkedValue.splice(findIndex, 1)
  }
  updateValue(checkedValue, 'clear')
  emit('sure', getData())
}

onMounted(async () => {
  if(!props.disabled) {
    virtualRef.value = props.custom ? customRef.value : selectRef.value
  }
})
</script>

<style scoped lang="scss">

</style>

<style lang="scss">
.personnelSelectionPopper {
  padding: 0 !important;
  overflow-x: hidden;
  overflow-y: auto;
  height: auto;
}
</style>