export interface SourceItem {
    key?: string | number;
    label?: string;
    [k: string]: string | number | undefined
}

export interface SearchConfig {
    code: string;
    label: string;
    type: 'input' | 'select' | 'radio' | 'switch' | 'date-picker' | 'checkbox'; // 这里有几个枚举就表示能兼容几种输入选项
    value?: any;
    source?: SourceItem[];
    sourceKey?: string;
    sourceLabel?: string;
    placeholder?: string;
    itemConfig?: { // 表单元素内的设置项  如multiple  clearable 类型设置等
        [k: string]: any
    }

}
