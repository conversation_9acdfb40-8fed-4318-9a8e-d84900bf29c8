<template>
  <div class="personnelSelectionPanel">
    <div class="top">
      <div class="title">成员单选</div>
      <i @click="close"
         class="iconfont icon-close"/>
    </div>
    <div class="content">
      <el-tabs 
        v-model="activeName"
        class="personnelSelectionPanelTabs">
        <el-tab-pane 
          label="组织架构"
          name="organizational">
          <Organizational 
            ref="organizationalRef"
            :data="componentData.deComponentTreeVO"
            :checkedUser="checkedUser"
            @updateValue="updateValue"
          />
        </el-tab-pane>
        <el-tab-pane 
          label="成员"
          name="members">
          <Members 
            ref="membersRef" 
            v-model:data="componentData.userVOList"
            :checkedUser="checkedUser"
            @updateValue="updateValue"
          />
        </el-tab-pane>
        <el-tab-pane 
          v-if="configuration"
          label="动态参数"
          name="dynamicParameters">
          <slot name="dynamic">
            <DynamicParameters
              :data="dynamicParameters"
              @updateParameter="updateParameter"
            />
          </slot>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="bottom">
      <div
        class="left">
        <el-checkbox 
          v-model="isIncludeChild"
          v-if="activeName === 'organizational'"
          label="父部门不包含子部门员工" />
      </div>
      <div class="right">
        <RxkButton 
          type="pain"
          @click="close">取消</RxkButton>
        <RxkButton type="primary" @click="confirm">确定</RxkButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, unref, watch } from 'vue'
import type { RxkButton } from '@/components/common/RxkButton'
import Organizational from './components/organizationalPanel.vue'
import Members from './components/membersPanel.vue'
import DynamicParameters from './components/dynamicParameters.vue'
import type { BasicProps, ComponentData, UserVOList } from './type'
import type { PanelActionType } from './hooks/usePanel'
import { isFunc, isArray } from '@/utils/is'
import { cloneDeep, recursion } from '@/utils/tools'

const emit = defineEmits(['close', 'confirm', 'registerPanel', 'update'])
const organizationalRef = ref()
const membersRef = ref()

const activeName = ref('members')
const isIncludeChild = ref<boolean>(false) // 父部门不包含子部门员工
const configuration = ref<boolean>(false) // 是否展示动态参数配置
const componentData = ref<ComponentData>({})

const orginPropsData = ref<BasicProps>({})
const orginComponentData = ref<ComponentData>({})
const dynamicParameters = ref<Recordable>()
const checkedUser = ref<UserVOList[]>([])
const cloneCheckedUser = ref<UserVOList[]>([])

async function reload (propsValue: BasicProps, fn?:Fn) {
  console.log('orginPropsDataReload', cloneCheckedUser.value)
  const data = cloneDeep(cloneCheckedUser.value)
  updateValue(data)
  fn && fn()
}

async function init (propsValue: BasicProps, fn?:Fn) {
  orginPropsData.value = cloneDeep(propsValue)
  console.log(propsValue, 'propsValuepropsValueMember', orginPropsData.value)
  activeName.value = propsValue.activeName || 'members'
  isIncludeChild.value = propsValue.isIncludeChild || false
  configuration.value = propsValue.configuration || false
  dynamicParameters.value = propsValue.dynamicParameters
  // 获取数据
  const { data, api, menuId } = unref(orginPropsData.value)
  if(!api && data) {
    componentData.value = unref(data)
    orginComponentData.value = cloneDeep(data)
  
    // 处理部门人数数据
    await handleDepartData()
    await handleCheckedValue(propsValue.showValueData || [])
    return false
  }
  if (!api || !isFunc(api)) return
  try {
    const res = await api({ checkChildFlag: true, ...propsValue.paramsData, disableUserIds: propsValue.showValueData && propsValue.showValueData?.map(item => item.id) || [] }, menuId ? { MenuId: menuId } : '')
    if(isArray(res)) {
      componentData.value = res[0]
      orginComponentData.value = cloneDeep(res[0])
    } else {
      componentData.value = unref(res)
      orginComponentData.value = cloneDeep(res)
    }
    await handleDepartData()
    await handleCheckedValue(propsValue.showValueData || [])
    fn && fn()
  }catch (err) {
    console.log(err)
  }
}

// 处理回显值
async function handleCheckedValue (showValueData: UserVOList[]) {
  const arr: UserVOList[] = []
  if(showValueData && showValueData.length > 0) {
    const ids = showValueData.map(item => item.id)
    const mergedUsers = (orginComponentData.value?.showUserList || []).concat((orginComponentData.value?.userVOList || []))
    const uniqueUsers = mergedUsers.reduce((acc: UserVOList[], current: UserVOList) => {
      const x = acc.find(item => item.id === current.id)
      if (!x) {
        return acc.concat([current])
      } else {
        return acc
      }
    }, [])
    uniqueUsers.forEach(item => {
      if(ids.includes(item.id)) {
        arr.push(item)
      }
    })
  }
  console.log(arr)
  cloneCheckedUser.value = cloneDeep(arr)
  checkedUser.value = cloneDeep(arr)
  emit('update', arr)
}

// 处理部门人数数据
async function handleDepartData () {
  const data = cloneDeep(orginComponentData.value.deComponentTreeVO)
  recursion(data, (item) => {
    const len = []
    if (isIncludeChild.value) {
      item.len = item.userVOList.length
    } else {
      recursion(item.children, (children) => {
        item.userVOList.push(...children.userVOList)
      })
      item.len = len.length + item.userVOList.length
    }
  })
  componentData.value.deComponentTreeVO = cloneDeep(data)
}

const panelActionType: PanelActionType = {
  init,
  getContainChildren,
  getCheckedUser,
  getParameter,
  updateValue,
  reload,
  handleCheckedValue
}
emit('registerPanel', panelActionType)

function updateValue (data: UserVOList[], type?: string){
  console.log('updateValue666', data, checkedUser.value)
  checkedUser.value = data
  if(type && type === 'clear') {
    cloneCheckedUser.value = cloneDeep(checkedUser.value)
  }
}

function close () {
  emit('close')
  const clone = cloneDeep(cloneCheckedUser.value)
  emit('update', clone)
}

function confirm (){
  cloneCheckedUser.value = cloneDeep(checkedUser.value)
  emit('confirm')
}

function getContainChildren () {
  return unref(isIncludeChild.value)
}

function getCheckedUser () {
  return unref(checkedUser.value)
}
function updateParameter (params: Recordable) {
  dynamicParameters.value = params 
}
function getParameter () {
  return unref(dynamicParameters.value)
}

watch(isIncludeChild, async (val) => {
  await handleDepartData()
  organizationalRef.value.handleIncludeChild(val)
})

</script>

<style lang="scss" scoped>
.personnelSelectionPanel {
  max-height: 50vh;
  overflow-y: hidden;
  .top {
    @include flex-center(row, space-between, center);
    padding: 17px 24px;
    border-bottom: 1px solid #EBEEF5;
    .title {
      font-weight: 700;
      color: $gray;
      font-size: $saas-font-size-small;
    }
    .icon-close {
      cursor: pointer;
    }
  }
  .content {
    max-height: calc(50vh - 120px);
    height: 400px;
    overflow-y: auto;
    // @include flex-center(row, normal, normal);
    border-bottom: 1px solid #EBEEF5;
    padding: 0 16px;
    .personnelSelectionPanelTabs {
      width: 100%;
      height: 100%;
      --el-tabs-header-height: 48px !important;
      :deep(.el-tabs__header) {
        margin: 0;
      }
      :deep(.el-tabs__content) {
        height: calc(100% - 48px);
      }
      :deep(.el-tabs__nav) {
        transform: translateX(16px) !important;
      }
      :deep(.el-tab-pane) {
        height: 100%;
      }
      :deep(.el-tabs__active-bar) {
        background-color: $primary-color;
      }
      :deep(.el-tabs__item.is-active) {
        color: $primary-color;
        font-weight: 700;
      }
      :deep(.el-tabs__item) {
        color: $secondary-text-color;
      }
      :deep(.el-tabs__nav-wrap::after) {
        height: 1px;
        background-color: #EBEEF5;
      }
    }
    .left {
      width: 250px;
      border-right: 1px solid #EBEEF5;
      overflow-y: auto;
    }
  }
  .bottom {
    padding: 16px 24px;
    @include flex-center(row, space-between, center);
  }
}
</style>