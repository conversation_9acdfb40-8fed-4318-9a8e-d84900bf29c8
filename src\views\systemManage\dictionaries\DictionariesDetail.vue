<template>
  <RxkDialog
    v-model="showDialog"
    title="字典明细"
    width="1200px"
    :showFooter="false"
  >
    <SearchFilter @register="registerSetting" @search="search" />
    <div class="tw-mb-[16px] tw-ml-[16px]">
      <RxkButton type="primary" @click="handleAdd">新增</RxkButton>
    </div>
    <!-- 表格 -->
    <RxkVTable @register="registerTable">
      <template #operateSlot="{ row }">
        <div class="tw-flex">
          <RxkButton class="table-action-btn"
                     text
                     @click="handleModify(row)"
          >修改</RxkButton
          >
          <RxkButton class="table-action-btn" text @click="handleDisable(row)">
            {{ row.status === 1 ? '禁用' : '启用' }}
          </RxkButton>
        </div>
      </template>
    </RxkVTable>
  </RxkDialog>
  <RxkDialog
    v-model="addDialogVisible"
    :title="isAdd ? '新增' : '编辑'"
    width="800px"
    @sure="addSure"
  >
    <div class="tw-m-[16px]">
      <el-form
        ref="formDataDom"
        require-asterisk-position="right"
        :model="formData"
        :rules="addRules"
        label-width="80px"
      >
        <el-form-item label="Key值" prop="value">
          <RxkInput
            :disabled="!isAdd"
            placeholder="请输入Key值"
            v-model="formData.value"
          />
        </el-form-item>
        <el-form-item label="字典明细" prop="labelName">
          <RxkInput placeholder="请输入字典明细" v-model="formData.labelName" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <RxkInput
            type="number"
            placeholder="请输入排序"
            v-model="formData.sort"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <RxkSelect
            placeholder="请选择是否启用"
            v-model="formData.status"
            :list="dataDetailStatusList"
          />
        </el-form-item>
        <el-form-item label="补充数据" prop="supplementDesc">
          <RxkInput
            type="textarea"
            placeholder="请输入补充数据"
            :autosize="{ minRows: 6, maxRows: 8 }"
            v-model="formData.supplementDesc"
          />
        </el-form-item>
        <el-form-item label="明细描述" prop="remark">
          <RxkInput
            type="textarea"
            placeholder="请输入明细描述"
            :autosize="{ minRows: 6, maxRows: 8 }"
            v-model="formData.remark"
          />
        </el-form-item>
      </el-form>
    </div>
  </RxkDialog>
</template>

<script setup lang="tsx">
import { computed, ref, unref } from 'vue'
import { RxkButton } from '@/components/common'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import type { ColumnType } from '@/types/table'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import {
  dataDetailInsert,
  dataDetailPageList,
  dataDetailUpdate
} from '@/apis/system'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkDialog } from '@/components/common/RxkDialog'
import { dataDetailStatusList } from '@/enums/settlemenManage'
import { RxkInput } from '@/components/common/RxkInput'
import { RxkSelect } from '@/components/common'
import { cloneDeep } from '@/utils/tools'

const addDialogVisible = ref(false)
const isAdd = ref(false)
const formDataDom = ref()

const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  },
  groupCode: {
    type: String,
    required: true
  },
  type: {
    type: [String, Number],
    required: true
  },
  visible: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:visible'])

const showDialog = computed({
  get () {
    return props.visible
  },
  set (value: boolean) {
    emit('update:visible', value)
  }
})

defineOptions({
  name: 'DictionariesDetail'
})

// 表格列配置
const columns = ref<ColumnType[]>([
  {
    key: 'value',
    title: 'Key值'
  },
  {
    key: 'labelName',
    title: '字典明细'
  },
  {
    key: 'sort',
    title: '排序'
  },
  {
    key: 'status',
    title: '状态',
    render: ({ cellData }) => {
      return dataDetailStatusList.find((item) => item.value === cellData.status)
      ?.label
    }
  },
  {
    key: 'supplementDesc',
    title: '补充数据'
  },
  {
    key: 'remark',
    title: '明细描述'
  },
  {
    key: 'operate',
    title: '操作',
    fixed: 'right',
    slot: 'operateSlot',
    width: 120
  }
])

interface SearchCacheType {
  value?: string
  labelName?: string
  status?: string
  groupCode?: string
}

interface FormDataType {
  value: string
  labelName: string
  sort: string | number
  status: string | number
  supplementDesc: string
  remark: string
}

const formDataInit = {
  value: '',
  labelName: '',
  sort: '',
  status: '',
  supplementDesc: '',
  remark: ''
}

const formData = ref<FormDataType>(cloneDeep(formDataInit))

const addRules = ref({
  value: [{ required: true, message: '请输入Key值', trigger: 'blur' }],
  labelName: [{ required: true, message: '请输入字典明细', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  remark: [{ required: true, message: '请输入明细描述', trigger: 'blur' }]
})

const searchCache: SearchCacheType = {}

const searchInfo: SearchCacheType = {
  value: searchCache?.value || undefined,
  labelName: searchCache?.labelName || undefined,
  status: searchCache?.status || undefined,
  groupCode: props.groupCode || undefined
}

const [registerTable, { reload }] = useTable({
  api: dataDetailPageList,
  columns: getBasicColumns(),
  searchInfo: { model: searchInfo },
  immediate: true
})

function search (data: SearchCacheType) {
  searchInfo.value = data.value || undefined
  searchInfo.labelName = data.labelName || undefined
  searchInfo.status = data.status ?? undefined
  reload()
}

function getBasicColumns (): ColumnType[] {
  return unref(columns)
}

const searchFormData = ref<FormSchema[]>([
  {
    key: 'value',
    component: 'Input',
    val: searchCache?.value || '',
    fieldName: 'Key值'
  },
  {
    key: 'labelName',
    component: 'Input',
    val: searchCache?.labelName || '',
    fieldName: '字典明细'
  },
  {
    key: 'status',
    component: 'Select',
    val: searchCache?.status || '',
    fieldName: '状态',
    options: dataDetailStatusList
  }
])

const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})

function handleModify (row: any) {
  formData.value = cloneDeep(row)
  isAdd.value = false
  addDialogVisible.value = true
}

function handleDisable (row: any) {
  const info = cloneDeep(row)
  info.status = info.status === 1 ? 0 : 1
  dataDetailUpdate(info).then(() => {
    ElMessage.success(info.status === 1 ? '禁用成功' : '启用成功')
    reload()
  })
}

function handleAdd () {
  formData.value = cloneDeep(formDataInit)
  isAdd.value = true
  addDialogVisible.value = true
}

function addSure () {
  formDataDom.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const request = isAdd.value
          ? () =>
            dataDetailInsert({
              ...formData.value,
              groupCode: props.groupCode,
              id: props.id,
              type: props.type
            })
          : () =>
            dataDetailUpdate({
              ...formData.value
            })

        await request()
        await reload()
        ElMessage.success(isAdd.value ? '新增成功' : '编辑成功')
        addDialogVisible.value = false
      } catch (error) {
        console.log(error)
      }
    }
  })
}
</script>
