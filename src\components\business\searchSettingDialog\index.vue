<template>
  <RxkDialog
    v-model="visible"
    title="设置常用筛选条件"
    width="700px"
  >
    <div class="all-field-content">
      <div class="quick-left">
        <div class="title">
          <RxkCheckbox @change="changeAll" :indeterminate="indeterminate" v-model="checkAll"/>
          <span>全部字段({{formListData.length}})</span>
        </div>
        <RxkInput class="custom-input" :prefix-icon="Search" v-model="searchTxt"/>
        <ul class="field-ul">
          <li class="field-item"
              v-for="(item, index) in (searchAllFieldList || formListData)"
              :key="index">
            <RxkCheckbox :key="item.key" @change="changeSelected($event, item)" v-model="item.showed"/>
            <span class="name">{{item.fieldName}}</span>
          </li>
        </ul>
      </div>
      <div class="quick-right">
        <div class="title">
          <span>已选字段({{selectedList.length}})</span>
        </div>
        <draggable
          :list="selectedList"
          tag="ul"
          animation="300"
          class="drag-ul"
          item-key="tId"
          :group="state.groupA">
          <template #item="{element}">
            <li class="drag-item">
              <el-icon style="margin-right: 8px"><Rank /></el-icon>
              <span class="name">{{element.fieldName}}</span>
              <span @click="handleDelete(element)" class="pointer iconfont icon icon-shanshu"/>
            </li>
          </template>
        </draggable>
      </div>
    </div>

    <template #footer>
      <div class="custom-footer">
        <div class="txt">
          注：此处字段将在列表展示，锁定后将在冻结列展示 最多支持锁定两个字段
        </div>
        <div>
          <RxkButton @click="close">取消</RxkButton>
          <RxkButton type="primary" @click="submit">确定</RxkButton>
        </div>
      </div>
    </template>
  </RxkDialog>
</template>

<script lang="ts" setup>
import { cloneDeep } from 'lodash-es'
import { ref, reactive, watch, unref, computed, type PropType, nextTick } from 'vue'
import draggable from 'vuedraggable'
import { Rank } from '@element-plus/icons-vue'
import { Search } from '@element-plus/icons-vue'

import { RxkCheckbox } from '@/components/common/RxkCheckbox'
import { RxkInput } from '@/components/common/RxkInput'
import { RxkDialog } from '@/components/common/RxkDialog'
import { getInstitutionOption, setInstitutionOption } from '@/apis/institutionManage'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { useResource } from '@/hooks/useResource'

const { getMenuId } = useResource()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  // 默认展示搜索项key
  defaultSearchKeys: {
    type: Array as PropType<string[]>,
    default: () => [] 
  },
  // 具体场景额外指定值
  extraKey: {
    type: String,
    default: ''
  },
  // 所有可展示的字段数据
  options: {
    type: Array as PropType<FormSchema[]>,
    default: () => []
  }
})
const emit = defineEmits(['update:modelValue', 'searchLoaded'])
const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit('update:modelValue', val)
})

/* ----------------------组件操作相关---------------------- */
const state = reactive({
  groupA: { name: 'form', put: false, pull: false }
})
const formListData = ref<FormSchema[]>([])
const indeterminate = ref(false)
const searchTxt = ref('')
const checkAll = ref(false)
const selectedList = ref<Recordable[]>([])
const searchAllFieldList = ref<any>(null)
// 表单搜索项配置，数据查询所需参数的全局唯一key值
const businessType = computed(() => 'search-' + getMenuId() + (props.extraKey ? `-${props.extraKey}` : ''))
watch(
  () => searchTxt.value, 
  () => {
    searchAllFieldList.value = searchTxt.value 
      ? formListData.value.filter(item => (item.fieldName.indexOf(unref(searchTxt)) > -1)) 
      : null
  }
)
watch(
  () => props.options, 
  val => formListData.value = cloneDeep(val),
  { deep: true, immediate: true }
)

getConfigData(true)

/**
 * @description: 设置搜索栏配置
 */
async function getConfigData () {
  const res = await getInstitutionOption({ businessType: businessType.value })
  // 若未进行过配置，接口整体返回null，采用原始默认数据
  const data = JSON.parse(res)
  const searchKeys = data?.search ? data.search : props.defaultSearchKeys
  const searchList: FormSchema[] = []
  // 搜索条件初始化
  unref(props.options).forEach((item) => (item.showed = false))
  searchKeys.forEach((key: string) => {
    const res = unref(props.options).find((item) => item.key === key)
    if (res) {
      res.showed = true
      searchList.push(res)
    }
  })

  nextTick(() => {
    selectedList.value = cloneDeep(searchList)
    dealCheck()
  })
  
  emit('searchLoaded', { options: searchList, searchKeys })
}
function changeSelected (bool: boolean, item: Recordable) {
  if(bool) {
    selectedList.value.push({ ...item })
  } else {
    const index = selectedList.value.findIndex(sub => item.key === sub.key)
    selectedList.value.splice(index, 1)
  }
  dealCheck()
}
function dealCheck () {
  checkAll.value = formListData.value.every(item => item.showed)
  indeterminate.value = !!(selectedList.value.length && selectedList.value.length < formListData.value.length)
}
function changeAll () {
  formListData.value.forEach(item => {
    item.showed = checkAll.value
  })
  selectedList.value = formListData.value.filter(item => item.showed)
  dealCheck()
}
function handleDelete (item: Recordable) {
  if (selectedList.value.length === 1) {
    ElMessage.warning('至少保留一个')
    return
  }
  formListData.value.forEach(sub => {
    if (sub.key === item.key) {
      sub.showed = false
    }
  })
  const index = selectedList.value.findIndex(current => current.key === item.key)
  selectedList.value.splice(index, 1)
}

function close (){
  emit('update:modelValue', false)
}
async function submit () {
  const params = {
    search: selectedList.value.filter(item => item.showed).map(item => item.key) || [],
    column: [],
    lock: []
  }
  await setInstitutionOption({ 
    businessType: businessType.value, 
    fieldJson: JSON.stringify(params) 
  })
  getConfigData()
  close()
}

</script>

<style lang="scss" scoped>
.all-field-content {
  height: 400px;
  display: flex;
  .quick-left {
    flex: 1;
    height: 100%;

    .title {
      height: 40px;
      display: flex;
      align-items: center;
      background-color: #F5F7FA;
      color: #666666;
      padding-left: 24px;
    }
    .custom-input {
      padding: 16px;
    }
    .field-ul {
      display: flex;
      flex-wrap: wrap;
      padding: 0 16px;
      overflow-y: auto;
      max-height: calc(100% - 104px);
      .field-item {
        width: 33.33%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        .name {
          font-size: 14px;
          color: #666666;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .quick-right {
    width: 248px;
    height: 100%;
    .title {
      height: 40px;
      display: flex;
      align-items: center;
      background-color: #F5F7FA;
      color: #666666;
      padding-left: 24px;
    }
    .drag-ul {
      height: calc(100% - 40px);
      overflow-y: auto;
      padding: 12px 16px;
      .drag-item {
        height: 40px;
        display: flex;
        align-items: center;
        background-color: #F5F7FA;
        color: #666666;
        margin-bottom: 8px;
        padding: 0 12px;
        cursor: move;
        &:hover {
          background-color: #EEF3FF;
          .icon {
            display: block;
          }
        }
        .name {
          font-size: 14px;
          color: #666666;
          flex: 1;
        }
        .icon {
          display: none;
          color: #F44D4D;
        }
      }
    }
  }
}
.custom-footer {
  display: flex;
  justify-content: space-between;
  height: 64px;
  border-top: 1px solid #ebeef5;
  align-items: center;
  padding: 0 24px;
  .txt {
    font-size: 12px;
    color: #aaaaaa;
  }
}
</style>