$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;
$white:#ffffff;
$gray:#333333;


//sidebar
$menuBg:#304156;
$subMenuBg: #fff;
$menuFontColor: #fff;
$menuHover: #4C5896;
$menuHoverFontColor: #333;
/*分割线*/
$saas-dividing-line-color:#F3F3F5;
/*按钮颜色*/
$saas-button-color: #2E377C;;
/*按钮hover颜色*/
$saas-button-hover-color:#ECF3FF;

/*字体*/
/*mini字号*/
$saas-font-size-mini: 12px;
/*small字号*/
$saas-font-size-small: 14px;
/*normal字号*/
$saas-font-size-normal: 16px;

$backgroundColor:#ffffff;
$sidebarBackgroundColor: #2E377C;;


/*色调*/
$primary-color: #5687ff; //主色系，主要使用于图标、导航栏、强调和突出文字小面积使用（其色值透明度降低用于其他次要信息）
$secondary-color: #E7B123; // 辅助色，应用于重要文案信息与主色搭配使用
$third-color: #2E8FE4; //辅助色，应用于公司名称、个人职务、点击状态提示
$tip-color: #E74671; // 提示色，应用于重要文字信息、表格标题和提示信息
$menu-bg-color: #2E377C;
$highlight-primary-color: #007CF9;
$table-header-color: #F4F5F8; // 表格头部背景色
$table-stripe-color: #f5f5f9; // 表格斑马纹颜色
$table-hover-color: rgba(242,243,250,0.3); // 表格hover颜色
$btn-color: #017BF9; // 按钮颜色
/*文字颜色*/
$main-text-color: #333333; // 主要文字信息展示，一级文字标题文字
$secondary-text-color: #666666; // 二级文字信息、列表页面文字
$third-text-color: #999999; // 三级文案信息，图表时间数字信息，次要提示信息
$weak-color: #DDDDDD; // 用于侧栏、内容栏、描边一级分割线
$border-color: #D8D8D8; // 用于title边框线

/*字号*/
$font-size-mini: 12px; // mini
$font-size-small: 14px; // small
$font-size-normal: 16px; // normal

/*间距*/
$distance-mini: 5px; // mini
$distance-small: 10px; // small
$distance-normal: 15px; // normal
$distance-large: 20px; // large


:export {
  saasPrimaryColor: #2E377C;
  menuHover: $menuHover;
  menuFontColor: $menuFontColor;
}