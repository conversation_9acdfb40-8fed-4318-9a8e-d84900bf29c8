<template>
  <el-checkbox class="rxk-checkbox"
               :size="size"
               v-model="checked"
               @change="change"
               :disabled="disabled"
               :indeterminate="indeterminate"
               :label="label" />
</template>
<script lang="ts" setup>
import { computed, type PropType } from 'vue'
defineOptions({
  name: 'RxkCheckbox',
  inheritAttrs: false
})

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: ''
  },
  size: {
    type: String as PropType<'large' | 'default' | 'small'>,
    default: 'default'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  indeterminate: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue', 'change'])
const checked = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})
function change (val: any) {
  emit('change', val)
}
</script>
<style lang="scss">
@import "./index.scss";
</style>