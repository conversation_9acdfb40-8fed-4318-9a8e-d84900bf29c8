<template>
  <div class="group-manage">
    <SearchFilter @register="registerSetting" @search="search" />
    <div class="operate-content">
      <div class="right-content">
        <RxkButton type="primary" @click="handleAddDetail('add')">添加参数分组</RxkButton>
      </div>
    </div>
    <div class="table-box">
      <RxkVTable @register="registerTable" />
    </div>
    <AddDraw v-if="state.showAddDetailVisible" v-model:visible="state.showAddDetailVisible" :type="state.operateType" :data="state.operateItem" @close="state.showAddDetailVisible = false,state.operateItem=null" @confirm="handleAddEdit" />
  </div>
</template>

<script lang="tsx" setup>
import { reactive, ref, unref } from 'vue'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { SearchFilter } from '@/components/business/searchFilter'
import type { ColumnType } from '@/types/table'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { RxkButton } from '@/components/common/RxkButton'
import { useResource } from '@/hooks/useResource'
import type { paramsItem } from "../type";
import { addParamsGroupApi, paramGroupPageApi, updateParamsGroupApi } from "@/apis/paramsManage";
import AddDraw from "./components/add.vue";

const { getAuth } = useResource()
const state = reactive({
  operateType: '',
  operateItem: null as paramsItem | null,
  showAddDetailVisible: false,
  activeTab: '',
  paramsGroupOptions: [] as {id: string, name: string}[]
})
const searchInfo = {
  count: false,
  model: {
    name: '',
  },
  orderBys: [],
  searches: ''
}
// 自定义表单数据
const searchFormData = ref<FormSchema[]>([
  {
    fieldName: '参数名称',
    component: 'Input',
    key: 'name',
    val: '',
    componentProps: {
      clearable: true,
      placeholder: '请输入参数名称',
      maxlength: 50
    }
  }
])
const columns = ref<ColumnType[]>([
  { key: 'id', title: 'ID', },
  { key: 'name', title: '参数组名', width: 300,},
  { key: 'description', title: '描述', width: 120 },
  { key: 'updateTime', title: '最近修改时间' },
  { key: 'updater', title: '最近修改人', width: 140, },
  { 
    key: 'operate',
    title: '操作',
    width: 120,
    render: ({ cellData }) => {
      return (
        <>
          { <RxkButton class="operate-btn" text onClick={() => handleAddDetail('edit', cellData)} >编辑</RxkButton> }
        </>
      )
    }
  },
])
const getBasicColumns = () => unref(columns)
const [registerTable, { reload, setProps }] = useTable({
  api: paramGroupPageApi,
  columns: getBasicColumns(),
  searchInfo: searchInfo,
  immediate: true, // 是否立刻请求
  showRowIndex: false
})


const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})
const search = (val: { [k: string]: any }) => {
  searchInfo.model.name = val.name
  setProps({searchInfo: {...searchInfo}})
  reload()
}

const handleAddDetail = (sign: string, row?: paramsItem)=>{
  state.operateType = sign
  state.operateItem = row ? row : null
  state.showAddDetailVisible = true
}
function handleAddEdit(form: any, fn: Fn){
 const apiFn = state.operateType === 'add' ? addParamsGroupApi : updateParamsGroupApi
 apiFn(form).then(()=>{
  ElMessage.success(`${state.operateType === 'add' ? '新增' : '编辑'}成功`);
  state.showAddDetailVisible = false
  reload()
 }).finally(()=>{
  fn?.()
 })
}

</script>

<style lang="scss" scoped>
.group-manage{
  height: 100%;
  overflow: hidden;
  @include flex-center(column, normal, normal);

  .operate-content{
    @include flex-center(row, flex-end);
    padding: 0 16px 16px;
  }
  .table-box{
    flex: 1;
    overflow: hidden;
  }
  :deep(.operate-btn){
    padding: 0;
    &:hover{
      background-color: transparent;
    }
    &.disabled{
      cursor: not-allowed;
      color: var(--el-disabled-text-color);
    }
  }
  .total-info{
    font-size: 13px;
    font-size: 13px;
    height: 100%;
    @include flex-center(row, normal, center);
    .label-text{
      color: #666;
    }
    .value-text{
      color: #333;
      margin-left: 8px;
    }
  }
}
</style>