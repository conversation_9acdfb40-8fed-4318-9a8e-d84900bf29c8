import { isNullOrUndefOrEmpty } from '@/utils/is'
import { reactive } from 'vue'
export function useRules (formData: any) {
  const twoNumberReg = /^(([1-9]{1}\d{0,4})|(0{1}))(\.\d{1,2})?$/
  // const intervalReg = /^(([1-9]{1}\d{0,4})|(0{1}))(\.\d{1,2})?$/
  const validateNumber = (
    rule: any,
    value: any,
    callback: any,
    tips: any,
    isFirst = false
  ) => {
    const interval1type1Val = formData.settlementConfigs?.configs?.[0]?.type1Val
    // eslint-disable-next-line eqeqeq
    if (value != '0' && !value) {
      callback(new Error(`请输入${tips}`))
    // eslint-disable-next-line eqeqeq
    } else if (!twoNumberReg.test(value) || value == '0') {
      callback(new Error(`${tips}为正数且整数位不能超过5位，小数点不能超过2位`))
    } else if (
      isFirst &&
      interval1type1Val &&
      Number(interval1type1Val) < Number(value)
    ) {
      callback(new Error('第一区间左端点值需≥第一区间结算价格'))
    } else {
      callback()
    }
  }

  // 第一区间价格
  const validateDY = (rule: any, value: any, callback: any) => {
    const interval1type1Val = formData.settlementConfigs.configs[0]['type1Val']
    const interval1type2Val = formData.settlementConfigs.configs[0]['type2Val']
    const intervalBalance1 = formData.settlementConfigs.configs[0]['price']
    const interval2type1Val = formData.settlementConfigs.configs[1]['type1Val']
    if (!interval1type1Val || !interval1type2Val) {
      callback(new Error('请完善第一区间价格'))
    } else if (
      !twoNumberReg.test(interval1type1Val) ||
      interval1type1Val === '0' ||
      !twoNumberReg.test(interval1type2Val) ||
      interval1type2Val === '0'
    ) {
      callback(
        new Error('第一区间价格为正数且整数位不能超过5位，小数点不能超过2位')
      )
    } else if (Number(interval1type2Val) < Number(interval1type1Val)) {
      callback(new Error('第一区间价格大小关系错误'))
    } else if (interval2type1Val && interval1type2Val !== interval2type1Val) {
      callback(new Error('区间价格不连贯'))
    } else if (
      intervalBalance1 &&
      Number(interval1type1Val) < Number(intervalBalance1)
    ) {
      callback(new Error('第一区间左端点值需≥第一区间结算价格'))
    } else {
      callback()
    }
  }

  // 第二区间价格
  const validateDE = (rule: any, value: any, callback: any) => {
    const interval1type2Val = formData.settlementConfigs.configs[0]['type2Val']
    const interval2type1Val = formData.settlementConfigs.configs[1]['type1Val']
    const interval2type2Val = formData.settlementConfigs.configs[1]['type2Val']
    const interval3type1Val = formData.settlementConfigs.configs[2]['type1Val']
    // const { interval2type1Val, interval2type2Val, interval3type1Val, interval1type2Val } = formData
    if (!interval2type1Val || !interval2type2Val) {
      callback(new Error('请完善第二区间价格'))
    } else if (
      !twoNumberReg.test(interval2type1Val) ||
      interval2type1Val === '0' ||
      !twoNumberReg.test(interval2type2Val) ||
      interval2type2Val === '0'
    ) {
      callback(
        new Error('第二区间价格为正数且整数位不能超过5位，小数点不能超过2位')
      )
    } else if (
      Number(interval2type2Val) < Number(interval2type1Val) ||
      interval2type2Val === interval2type1Val
    ) {
      callback(new Error('第二区间价格大小关系错误'))
    } else if (
      (interval3type1Val && interval2type2Val !== interval3type1Val) ||
      (interval1type2Val && interval1type2Val !== interval2type1Val)
    ) {
      callback(new Error('区间价格不连贯'))
    } else {
      callback()
    }
  }

  // 第三区间价格
  const validateDS = (rule: any, value: any, callback: any) => {
    const interval2type2Val = formData.settlementConfigs.configs[1]['type2Val']
    const interval3type1Val = formData.settlementConfigs.configs[2]['type1Val']
    const interval3type2Val = formData.settlementConfigs.configs[2]['type2Val']
    // const { interval3type1Val, interval3type2Val, interval2type2Val } = formData
    if (!interval3type1Val || !interval3type2Val) {
      callback(new Error('请完善第三区间价格'))
    } else if (
      !twoNumberReg.test(interval3type1Val) ||
      interval3type1Val === '0' ||
      !twoNumberReg.test(interval3type2Val) ||
      interval3type2Val === '0'
    ) {
      callback(
        new Error('第三区间价格为正数且整数位不能超过5位，小数点不能超过2位')
      )
    } else if (
      Number(interval3type2Val) < Number(interval3type1Val) ||
      interval3type2Val === interval3type1Val
    ) {
      callback(new Error('第三区间价格大小关系错误'))
    } else if (interval2type2Val && interval3type1Val !== interval2type2Val) {
      callback(new Error('区间价格不连贯'))
    } else {
      callback()
    }
  }

  const validateInterval = (
    rule: any,
    value: any,
    callback: any,
    index: number
  ) => {
    switch (index) {
      case 0:
        validateDY(rule, value, callback)
        break
      case 1:
        validateDE(rule, value, callback)
        break
      case 2:
        validateDS(rule, value, callback)
        break
    }
  }

  // 校验正整数
  const isValidPositiveNumber = (value: string): boolean => {
    const positiveNumberReg = /^[+]?([1-9][0-9]*(?:[\.][0-9]*)?|\.[0-9]+)$/
    return positiveNumberReg.test(value)
  }

  const commonDrawerRules = reactive({
    name: [{ required: true, message: '请输入渠道名称', trigger: ['blur', 'change'] }],
    type: [{ required: true, message: '请选择渠道类型', trigger: ['blur', 'change'] }],
    otherSubject: [{ required: true, message: '请选择渠道公司主体', trigger: ['blur', 'change'] }],
    domainSubject: [{ required: true, message: '请选择域名主体', trigger: ['blur', 'change'] }],
    docker: [{ required: true, message: '请选择渠道对接人', trigger: ['blur', 'change'] }],
    status: [{ required: true, message: '请选择状态', trigger: ['blur', 'change'] }],
    settleType: [{ required: true, message: '请选择结算类型', trigger: ['blur', 'change'] }],
    h5Type: [{ required: true, message: '请选择H5类型', trigger: ['blur', 'change'] }],
    settlePrice: [
      {
        required: true,
        message: '请输入结算单价(正数)',
        validator: (rule: any, value: any, callback: any) => {
          if (!isValidPositiveNumber(value)) {
            callback(new Error('请输入结算单价(正数)'))
          } else {
            callback()
          }
        },
        trigger: ['blur', 'change']
      }
    ],
    creditProfitRatio: [
      {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          const reg = /^(\d+.\d{1,2}|\d+)$/
          if (isNullOrUndefOrEmpty(value)) {
            callback(new Error('请输入结算比例'))
          } else if (!reg.test(value) || Number(value) > 100 || Number(value) <= 0) {
            callback(new Error('取值范围(0-100]，保留2位小数'))
          } else {
            callback()
          }
        },
        trigger: ['blur', 'change']
      }
    ],
    settleFactor: [
      {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          const reg = /^(\d+.\d{1,2}|\d+)$/
          if (isNullOrUndefOrEmpty(value)) {
            callback(new Error('请输入结算系数'))
          } else if (!reg.test(value) || Number(value) > 100 || Number(value) <= 0) {
            callback(new Error('取值范围(0-100]，保留2位小数'))
          } else {
            callback()
          }
        },
        trigger: ['blur', 'change']
      }
    ]
  })
  return {
    commonDrawerRules,
    validateNumber,
    validateInterval
  }
}
