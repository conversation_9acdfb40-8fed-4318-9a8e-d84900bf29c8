<template>
  <div class="channel_group_list">
    <SearchFilter showCollapse @register="registerSetting" @search="search" />
    <div class="btn-box">
      <RxkButton
        v-if="getAuthByMenuCode('ChannelManagementBlacklist', 'BATCH_ADD')"
        @click="handleBatchOrientation"
        type="primary"
      >批量定向</RxkButton
      >
    </div>
    <div class="table-box">
      <RxkVTable @register="registerTable">
        <template #operateSlot="{ row }">
          <RxkButton
            class="table-action-btn"
            v-if="getAuthByMenuCode('ChannelManagementBlacklist', 'DETAIL')"
            text
            @click="handleDetails(row)"
          >详情</RxkButton
          >
          <el-button
            class="table-action-btn"
            :type="row.status === 1 ? 'primary' : 'danger'"
            text
            v-if="getAuthByMenuCode('ChannelManagementBlacklist', 'ENABLE')"
            @click="handleAble(row)"
          >{{ row.status === 1 ? '启用' : '禁用' }}</el-button
          >
        </template>
      </RxkVTable>
    </div>
    <!-- 批量定向 -->
    <BatchOrientationDrawer ref="refBatchOrientationDrawer" :enumChannelOptions="channelEnum" @refreshTable="reload()" />
    <!-- 详情 -->
    <BlackOrganDetail v-model:visible="detailVisible"
                      :id="curId"
                      :enumChannelOptions="channelEnum"
                      @refreshTable="reload()" />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref, withDefaults, onMounted } from 'vue'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import BatchOrientationDrawer from './batchOrientationDrawer.vue'
import BlackOrganDetail from './blackOrganDetail.vue'
import {
  getChannelListForBlack,
  blackListEnable
} from '@/apis/channelManage'
import { useResource } from '@/hooks/useResource'
import { useHandleData } from '@/hooks/useHandleData'
const { getAuthByMenuCode } = useResource()
const detailVisible = ref(false)
const props = withDefaults(
  defineProps<{
      listPro: Recordable;
    }>(),
  {
    listPro: () => ({})
  }
)
const channelEnum = ref<Array<{ channelId: string; channelName: string }>>([])
const searchInfo: { model: Record<string, any> } = reactive({
  model: {}
})

const [registerSetting] = useSearch({
  schemas: props.listPro.config.searchFormData
})

const [registerTable, { reload, setSearchInfo }] = useTable({
  api: props.listPro.api,
  columns: props.listPro.config.columns,
  searchInfo: searchInfo,
  immediate: true // 是否立刻请求
})

const search = (val: { [k: string]: any }) => {
  searchInfo.model = {
    ...searchInfo.model,
    ...val
  }
  if (searchInfo.model.tenantId2) {
    searchInfo.model.tenantId = searchInfo.model.tenantId2
  }
  setSearchInfo(searchInfo)
  reload()
}

const refBatchOrientationDrawer = ref()

const handleBatchOrientation = () => {
  refBatchOrientationDrawer.value.init()
}

const curId = ref('')

const handleDetails = (row: any) => {
  curId.value = row.id
  detailVisible.value = true
}

const handleAble = async (row: Recordable) => {
  const text = row.status === 1 ? '启用' : '禁用'
  await useHandleData(
    blackListEnable,
    { id: row.id, status: Number(!row.status) },
    text,
    'warning',
    '提示'
  )
  reload()
}

onMounted(() => {
  getChannelListForBlack().then(res => {
    channelEnum.value = res
  })
})

</script>

<style lang="scss" scoped>
.channel_group_list {
  height: 100%;
  display: flex;
  flex-direction: column;
  .btn-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    margin-bottom: 16px;
  }
  .table-box {
    flex: 1;
    overflow: auto;
  }
}
</style>
