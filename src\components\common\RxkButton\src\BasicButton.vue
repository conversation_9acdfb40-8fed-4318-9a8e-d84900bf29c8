<template>
  <el-button v-bind="getBindValue" @click="handleClick">
    <slot />
  </el-button>
</template>
<script lang="ts" setup>
import { computed, type PropType, ref, useAttrs } from 'vue'
defineOptions({
  name: 'Rxk<PERSON><PERSON><PERSON>'
})

const props = defineProps({
  type: {
    // 目前UI只设计了默认和primary，所以展示只支持2种type，新增type需要符合UI设计样式
    type: String as PropType<'primary' | 'default'>,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  text: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  size: {
    type: String as PropType<'large' | 'default' | 'small'>,
    default: 'default'
  }
})

// 通过v-bind绑定一个对象传递给按钮组件
const getBindValue = computed(() => ({ ...props, loading: loading.value }))
console.log('[ attrs ] >', useAttrs())
const loading = ref(false)
const emit = defineEmits(['click'])
function handleClick () {
  if (props.disabled) return
  // 优化：如果是涉及到提交等于后端的交互点击，请添加loading
  console.log(props.isLoading, 'props.isLoading')
  if (props.isLoading) {
    loading.value = true
    emit('click', () => (loading.value = false))
  } else {
    emit('click')
  }
}
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
