<template>
  <el-select
    style="width: 100%"
    v-model="selectVal"
    :placeholder="placeholder"
    placement="bottom"
    :multiple="multiple"
    :disabled="disabled"
    @change="handleChange"
    :clearable="clearable"
    size="default">
    <slot name="search"/>
    <template v-if="list.length">
      <el-option
        v-for="item in list"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        :disabled="item.disabled"
      />
    </template>
    <el-option
      v-else
      style="pointer-events: none;"
      class="nodata"
      value=""
    >
      {{  emptyText  }}
    </el-option>
  </el-select>
</template>
<script lang="ts" setup>
import { computed, type PropType, watch, onMounted, nextTick } from 'vue'
import type { List } from '@/types/common'

const props = defineProps({
  list: {
    type: Array as PropType<List[]>,
    required: true
  },
  modelValue: {
    type: [Number, String, Array] as any,
    default: ''
  },
  multiple: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  emptyText: {
    type: String,
    default: '无数据'
  }
})
const emit = defineEmits(['update:modelValue', 'change'])
const selectVal = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})
function handleChange (val: any) {
  emit('change', val)
}

// list可能异步
watch(() => props.list, () => {
  checkValInList()
})

// 如果值不存在list中，需要将值干掉
function checkValInList () {
  if (props.list.length) {
    if (props.multiple) {
      props.modelValue.forEach((val, i) => {
        const cur = props.list.find(item => item.value === val)
        !cur && selectVal.value.splice(i, 1)
      })
    } else {
      const cur = props.list.find(item => item.value === props.modelValue)
      !cur && (selectVal.value = '')
    }
  }
}

onMounted(() => {
  nextTick(() => {
    checkValInList()
  })  
})

</script>