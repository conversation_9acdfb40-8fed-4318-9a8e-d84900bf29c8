<template>
  <div class="menu">
    <RxkTree
      ref="menuTree"
      :name="`permission-${permissionManageStore.roleId}`"
      :data="data"
      v-if="data.length"
      show-checkbox
      node-key="id"
      :default-checked-keys="checkedList"
      :expand-on-click-node="false"
      :props="defaultProps"
      @check="checkChange"
      @is-check-data-change="handleIsDataChange"
    >
      <template #default="{ node, data }">
        <span>{{ node.label }}</span>
        <rxk-button v-if="data.dataPermission && !permissionManageStore.isSuper" text @click="handlePermission(data)">
          配置数据权限
        </rxk-button>
        <div class="tw-w-full tw-p-2 tw-ml-[40px]" v-if="data.resourceVOList?.length">
          <div class="tw-flex tw-items-start">
            <span v-if="getDetailRes(data)?.length" class="tw-flex-shrink-0 tw-w-[70px] tw-pt-[5px]" style="color: #333">列表页</span>
            <div class="resource-list tw-flex-1">
              <template v-for="item in data.resourceVOList" :key="item.id">
                <RxkCheckbox
                  v-if="item.sceneType !== 30"
                  v-model="item.checkFlag"
                  :disabled="permissionManageStore.isSuper"
                  @change="(val)=>{resourceChange(val,data)}"
                  :label="item.name">{{item.name}}</RxkCheckbox>
              </template>
            </div>
          </div>
          <div class="tw-flex tw-items-start" v-if="getDetailRes(data)?.length">
            <span class="tw-flex-shrink-0 tw-w-[70px] tw-pt-[5px]" style="color: #333">详情页</span>
            <div class="resource-list tw-flex-1">
              <template v-for="item in data.resourceVOList" :key="item.id">
                <RxkCheckbox
                  v-if="item.sceneType === 30"
                  v-model="item.checkFlag"
                  :disabled="permissionManageStore.isSuper"
                  @change="(val)=>{resourceChange(val,data)}"
                  :label="item.name">{{item.name}}</RxkCheckbox>
              </template>
            </div>
          </div>
        </div>
      </template>
    </RxkTree>
    <RxkDialog v-model="visible" title="数据权限" width="480px">
      <global-dialog v-if="visible"
                     :data="currentScopeList"
                     :department-id-list="departmentIdList"
                     ref="globalDialogDom" />
      <template #footer>
        <div class="footer">
          <RxkButton :disabled="loading" @click="visible=false">取消</RxkButton>
          <RxkButton type="primary" :loading="loading" @click="handleDefine">确定</RxkButton>
        </div>
      </template>
    </RxkDialog>
  </div>
</template>

<script setup lang="ts">
import GlobalDialog from '@/views/systemManage/permissionManage/globalDialog/globalDialog.vue'
import { ref, unref } from 'vue'
import { EDataScope } from '@/apis/interface/permissionManage'
import { ElMessage } from 'element-plus'
import { RxkTree } from '@/components/common/RxkTree'
import { setDataScopeApi, setRoleMenuPermissionApi } from '@/apis/permissionManage'
import { usePermissionManageStore } from '@/stores/modules/permission'
import type { Permission } from '@/apis/interface/permissionManage'
import type { PropType } from 'vue'
import { RxkCheckbox } from '@/components/common'
import { RxkDialog } from '@/components/common/RxkDialog'

const props = defineProps({
  data: {
    type: Array as PropType<Permission.ReqRoleMenuReturn[]>,
    required: true,
    default: () => []
  },
  expanded: {
    type: Array as PropType<string[]>,
    required: true,
    default: () => []
  },
  checkedList: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  handleSaveData: {
    type: Function,
    default: new Promise(resolve => resolve(true))
  }
})

const emits = defineEmits(['change', 'changeMenu'])

const permissionManageStore = usePermissionManageStore()

const defaultProps = {
  children: 'children',
  label: 'name',
  disabled: 'disabled',
  class: (data:any) => customNodeClass(data)
}

const customNodeClass = (data: any) => {
  if (data?.resourceVOList?.length) {
    return 'resource-node'
  }
  return null
}

const globalDialogDom = ref()
const menuTree = ref()
const visible = ref<boolean>(false)
const loading = ref<boolean>(false)

const currentScopeList = ref<Permission.ReqRoleMenuReturn['dataScopeList']>([])
const departmentIdList = ref<string[]>([])

const handleIsDataChange = (off: boolean) => {
  permissionManageStore.setSaveChange(off)
}

const handlePermission = (data: Permission.ReqRoleMenuReturn) => {
  permissionManageStore.setMenuId(data.id)
  currentScopeList.value = data.dataScopeList || []
  departmentIdList.value = data.departmentIdList || []
  visible.value = true
}

const checkChange = async (data:any, selectList:any) => {
  const { checkedKeys } = selectList
  setResourceCheck(data, checkedKeys)
}

function getDetailRes (data:Recordable){
  return data.resourceVOList?.filter((item:Recordable) => item.sceneType === 30) || []
}

function setResourceCheck (data:any, checkedKeys:string[]){
  if(data.children?.length){
    data.children.forEach((child:any) => setResourceCheck(child, checkedKeys ))
  }
  if(data.resourceVOList?.length){
    if(checkedKeys.includes(data.id)){
      data.resourceVOList.forEach((item:any) => item.checkFlag = true)
    } else {
      data.resourceVOList.forEach((item:any) => item.checkFlag = false)
    }
  }
}

function resourceChange (val:boolean, node: any){
  // 选中资源就默认选中菜单 未选中任何资源不去取消勾选菜单
  const res = node.resourceVOList.find((el:any) => el.checkFlag)
  const checkedKeys = unref(menuTree).getCheckedKeys()
  if(res) {
    if(!checkedKeys.includes(node.id)){
      checkedKeys.push(node.id)
      unref(menuTree).setCheckedKeys(checkedKeys)
      node.checkFlag = true
    }
  }
}

const handleDefine = async () => {
  const data: { department: string[]; dataScopeList: string[] } = globalDialogDom.value.getData()
  if (data.dataScopeList.includes(EDataScope.DEPARTMENT_CUSTOM)) {
    if (!data.department.length) return ElMessage.warning('请选择自定义部门')
  } else {
    data.department = []
  }
  try {
    loading.value = true
    await props.handleSaveData()
    await setDataScopeApi({
      dataScopeList: data.dataScopeList,
      departmentIdList: data.department,
      resourceIdList: [],
      roleId: permissionManageStore.roleId,
      menuId: permissionManageStore.menuId
    })
    emits('changeMenu', permissionManageStore.roleId)
    visible.value = false
  } finally {
    loading.value = false
  }
}
function formatPermissionList (){
  let list = []
  const checkedList = menuTree.value.getCheckedNodes()
  const halfList = menuTree.value.getHalfCheckedNodes()

  list = checkedList.map((item:any) => ({
    menuId: item.id,
    resourceIds: item.resourceVOList?.filter((child:any) => child.checkFlag)?.map((child:any) => child.id) || []
  }))
  // 半勾的菜单
  let half = halfList.map((el:any) => {
    // 半勾的菜单如果有资源列表，判断勾选了哪些资源
    return {
      menuId: el.id,
      isHalf: true
    }
  })
  return list.concat(half)
}

const handleSavePermission = async () => {
  await setRoleMenuPermissionApi({
    menuResourceList: formatPermissionList(),
    roleId: permissionManageStore.roleId,
    refreshFlag: permissionManageStore.refreshFlag
  })
  permissionManageStore.setRefreshFlag(false)
}

defineExpose({
  handleSavePermission
})
</script>

<style scoped lang="scss">
.menu{
  :deep(.el-tree){
    background: transparent;
    .el-tree-node__content{
      height: 32px;
    }
  }
  :deep(.resource-node) {
    .el-tree-node__children{
      display: flex;
      flex-wrap: wrap;
      width: 90%;
      justify-content: flex-start;
    }
    .el-tree-node__content{
      flex-wrap: wrap;
      height: 100%;
        .resource-list{
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          .el-checkbox{
            width: 20%;
            margin-right: 0;
          }
        }
    }
  }
}
.footer {
  height: 64px;
  line-height: 64px;
  padding: 0 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>