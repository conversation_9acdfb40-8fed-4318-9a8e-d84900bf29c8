<template>
  <div class="members">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索人员"
        clearable
        :prefix-icon="Search"/>
      <div class="memberList">
        <el-checkbox-group v-model="checkedIds">
          <div 
            class="childNodeItem"
            v-for="item in memberList"
            :key="item.id">
            <el-checkbox
              v-show="item.show"
              @change="(checked: boolean) => selectChange(checked, item)"
              :label="item.id"
              :disabled="item?.jobStatus?.code === 2"
              v-model="item.checked"
            >{{ item.realName }}<template v-if="item?.jobStatus?.code === 2">（离职）</template></el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, unref, watch, type PropType } from 'vue'
import { Search } from '@element-plus/icons-vue'
import type { UserVOList } from '../type'
import { cloneDeep } from '@/utils/tools'

const emit = defineEmits(['update:data', 'updateValue'])

const personnelSelectionInput = ref<string>('')
const memberList = ref<UserVOList[]>([])
const checkedIds = ref<string[]>([])

const props = defineProps({
  data: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  },
  checkedData: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  }
})

watch(() => props.data, (newValue) => {
  memberList.value = cloneDeep(newValue).map((item:Recordable) => {
    return {
      ...item,
      show: true
    }
  })
}, {
  immediate: true,
  deep: true
})

function setDefaultData (list:any[] = []){
  // 对list做兼容
  const ids:string[] = list?.map(el => {
    if(el instanceof Object){
      return el?.value
    }
    if(typeof el === 'string'){
      return el
    }
    return undefined
  })
  unref(memberList).forEach(item => {
    if(ids.includes(item.id)) item.checked = true
  })
  checkedIds.value = [...ids]
  emit('updateValue', getCheckList())
}

function selectChange (checked:boolean, item: any) {
  item.checked = checked
  console.log(checkedIds, item)
  emit('updateValue', getCheckList())
}

// 获取已选中的项
function getCheckList (){
  let list:Recordable[] = []
  unref(memberList).forEach(item => {
    item.checked && list.push({ value: item.id, label: item.realName })
  })
  return list
}

watch(personnelSelectionInput, (val) => {
  memberList.value.forEach(item => {
    if(val){
      item.show = item.realName && item.realName.includes(val)
    } else {
      item.show = true
    }
  })
})

function unChecked (id:string){
  unref(memberList).forEach(item => {
    if(item.id === id) item.checked = false
  })
  const index = checkedIds.value.findIndex((value) => value === id)
  index > -1 && checkedIds.value.splice(index, 1)
  emit('updateValue', getCheckList())
}

defineExpose({
  unChecked,
  setDefaultData
})

</script>

<style lang="scss" scoped>
.members {
  @include flex-center(row, normal, normal);
  height: 100%;
  .left {
    flex: 1;
    border-right: 1px solid #EBEEF5;
    padding: 16px 16px 16px 0;
    .memberList {
      overflow-y: auto;
      height: calc(100% - 38px);
      .childNodeItem {
        display: block;
        width: 100%;
      }
    }
    .personnelSelectionInput {
      border-radius: 4px;
      width: 268px;
      :deep(.el-input__wrapper) {
        box-shadow: none;
        background: #F4F4F5;
      }
      margin-bottom: 13px;
    }
  }
}
</style>