import type { PropType } from 'vue'

import { defineComponent, Transition, TransitionGroup } from 'vue'
import { getSlot } from '@/utils/helper/tsxHelper'

export function createSimpleTransition (name: string, origin = 'top center 0') {
  return defineComponent({
    name,
    props: {
      group: {
        type: Boolean as PropType<boolean>,
        default: false
      },
      origin: {
        type: String as PropType<string>,
        default: origin
      }
    },
    setup (props, { slots, attrs }) {
      return () => {
        const Tag = !props.group ? Transition : TransitionGroup
        return (
          <Tag name={name} {...attrs} >
            {() => getSlot(slots)}
          </Tag>
        )
      }
    }
  })
}