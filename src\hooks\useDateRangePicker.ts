import { ref } from 'vue'

export function useDateRangePicker (){

  const selectedValue = ref()
  // 选中日历日期后会执行的回调!!只选择一个日期就会执行,官网写的不清楚
  // 主要用这个方法获取到用户选择的初始时间,然后在禁用方法里通过这个时间设置结束时间的禁用时间
  const calendarChange = (date: Array<Date>) => {
    selectedValue.value = date[0]
  }
  const visibleChange = (e:boolean) => {
    if (e) {
      selectedValue.value = null
    }
  }
  const disabledDate = (time: Date, range:number) => {
    if (selectedValue.value) {
      const diff = Math.abs(selectedValue.value.valueOf() - time.valueOf())
      if (diff > 1000 * 3600 * 24 * range) {
        return true
      }
    }
  }

  return {
    calendarChange,
    visibleChange,
    disabledDate
  }
}