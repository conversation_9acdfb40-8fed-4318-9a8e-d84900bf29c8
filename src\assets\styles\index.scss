@import './variables';
@import './iconfonts/iconfont.css';
@import './common';

body {
  height: 100%;
  line-height: 1;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: MicrosoftYaHei, SourceHanSansCN-Regular, SourceHanSansCN;

  overflow: hidden;
}

html {
  height: 100%;
  box-sizing: border-box;
  line-height: 1;
}

#app {
  height: 100%;
  // min-width: 1440px;
  -webkit-tap-highlight-color: transparent;
}

* {
  -webkit-overflow-scrolling: touch;
}
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-track-piece {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  border-style: dashed;
  background-color: #d8d8d8;
  border-color: transparent;
  border-width: 2px;
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background: #d8d8d8;
}

.primary-color {
  color: $primary-color;
}
* {
  box-sizing: border-box;
}
ul,
li {
  list-style: none;
  padding: 0;
  margin: 0;
}

html:root {
  --el-color-primary: #5687ff;
  --el-form-label-font-size: 14px;
  --vxe-table-header-font-color: #666666;
}
.pointer {
  cursor: pointer;
}

.btnBlock {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  color: #5687ff;
  text-align: center;
  cursor: pointer;
  width: 100%;
}

.noAllowed {
  cursor: not-allowed !important;
}
.slide-x-reverse-transition {
  &-enter-active,
  &-leave-active {
    transition: 0.4s cubic-bezier(0.25, 0.8, 0.5, 1) !important;
  }
  &-enter-from,
  &-leave-to {
    opacity: 0%;
    transform: translateX(20px);
  }
}

/* 全局遮罩-s */
.ba-layout-shade {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999990;
}
// @media screen and (min-width: 1024px) {
//   #app {
//     min-width: 1440px;
//   }
// }
.el-table {
  --el-table-header-text-color: var(--vxe-table-header-font-color) !important;
}
.custorm-hasTable-footer {
  .vxe-table--body-wrapper {
    height: calc(100% - 88px) !important;
    min-height: 200px;
  }
  .vxe-table--body-wrapper.fixed-left--wrapper,
  .vxe-table--footer-wrapper.body--wrapper {
    overflow-x: hidden !important;
  }
}
