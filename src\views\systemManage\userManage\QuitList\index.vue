<template>
  <RxkDrawer
    size="1000"
    title="离职用户"
    :btn-loading="false"
    v-model="show"
    @close="handleClose"
    @confirm="handleClose"
  >
    <template #content>
      <div class="content-box">
        <SearchFilter is-dialog @register="registerSetting" @search="search"/>
        <div class="table-row">
          <RxkVTable @register="registerTable" />
        </div>
      </div>
    </template>
  </RxkDrawer>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { RxkVTable } from '@/components/common/RxkVTable'
import { SearchFilter } from '@/components/business/searchFilter'
import { ref, defineProps, unref } from 'vue'
import { getUserList } from '@/apis/userManage'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import type { ColumnType } from '@/types/table'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { RxkDrawer } from '@/components/common/RxkDrawer'
import { TableAppCodeEnum } from '@/enums/table'

const props = defineProps({
  selectTreeId: Number,
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue'])

watch(() => props.selectTreeId, (val) => {
  searchInfo.model.departmentId = val
  reload()
})

const show = computed({
  get: () => {return props.modelValue},
  set: (val) => {
    emit('update:modelValue', val)
  }
})

const searchInfo: {model: Recordable} = {
  model: {
    realName: undefined,
    departmentId: undefined,
    jobStatus: 'INACTIVE'
  }
}

const searchFormData = ref<FormSchema[]>([
  {
    key: 'realName',
    component: 'Input',
    val: '',
    fieldName: '用户姓名'
  }
])

const columns = ref<ColumnType[]>([
  {
    key: 'id',
    title: '用户ID'
  },
  {
    key: 'realName',
    title: '用户姓名'
  },
  {
    key: 'mobile',
    title: '联系电话'
  },
  {
    key: 'departmentVO',
    title: '所属部门',
    render: ({ cellData }) => cellData?.departmentVO?.name
  },
  {
    key: 'resignTime',
    title: '离职时间'
  }
])

const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})

const [registerTable, { reload, setSearchInfo }] = useTable({
  api: getUserList,
  columns: getBasicColumns(),
  searchInfo: searchInfo,
  immediate: true, // 是否立刻请求
  showSelection: false,
  showPagination: true,
  tableCode: TableAppCodeEnum.leaveUser
})

function search (data: Recordable) {
  searchInfo.model.realName = data.realName || undefined
  setSearchInfo(searchInfo)
  reload()
}

function getBasicColumns (): ColumnType[] {
  return unref(columns)
}

function handleClose (){
  emit('update:modelValue', false)
}

</script>

<style scoped lang="scss">
.drawer-title{
  color: #333;
}
.content-box{
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.table-row{
  flex: 1;
  overflow-y: hidden;
}
</style>
