<template>
  <div class="isTxt" v-if="isTxt">
    {{isTextValue || '-'}}
  </div>
  <template v-else>
    <div>
      <AddressType
        style="margin-top: 8px;"
        :showCity="showCity"
        :showArea="showArea"
        v-model="addressData"/>
      <RxkInput
        v-if="data.columnInfo.precisions === 1"
        style="margin-top: 8px"
        type="textarea"
        v-model="addressData.detail"/>
    </div>
  </template>

</template>
<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import AddressType from './addressType.vue'
import { RxkInput } from '@/components/common'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})
const addressData = ref<Recordable>({})
const isTextValue = ref('-')
onMounted(() => {
  const defaultData = innerValue.value ? JSON.parse(innerValue.value) : {}
  addressData.value.province = defaultData.province || ''
  addressData.value.city = defaultData.city || ''
  addressData.value.area = defaultData.area || ''
  addressData.value.pName = defaultData.pName || ''
  addressData.value.cName = defaultData.cName || ''
  addressData.value.aName = defaultData.aName || ''
  addressData.value.detail = defaultData.detail || ''
  isTextValue.value = getTextValue()
})

// 展示到市
const showCity = computed(() => {
  const precisions = props.data.columnInfo.precisions || 0
  return Number(precisions) < 4
})
// 展示到区
const showArea = computed(() => {
  const precisions = props.data.columnInfo.precisions || 0
  return Number(precisions) < 3
})

function getTextValue () {
  const {
    pName = '', cName = '', aName = '', detail
  } = addressData.value
  if (props.data.columnInfo.precisions === 1) {
    return (pName + cName + aName + detail) || '-'
  } else if (props.data.columnInfo.precisions === 2) {
    return (pName + cName + aName) || '-'
  } else if (props.data.columnInfo.precisions === 3) {
    return (pName + cName) || '-'
  } else if (props.data.columnInfo.precisions === 4) {
    return pName || '-'
  } else {
    return '-'
  }
}

watch(() => addressData.value, () => {
  console.log('变了吗', addressData)
  innerValue.value = JSON.stringify(addressData.value)
}, {
  deep: true
} )

</script>