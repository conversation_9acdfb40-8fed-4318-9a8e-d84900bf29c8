// 将数据转换成formData数据形式
export const turnDataToFormData = (data) => {
  const formData = new window.FormData()
  Object.keys(data).forEach((key) => {
    const value = data[key]
    if (Array.isArray(value)) {
      value.forEach((item) => {
        formData.append(`${key}[]`, item)
      })
      return
    }
    formData.append(key, data[key])
  })
  return formData
}
export function getFileName (str: string) {
  if (!str) return false
  const nameStr = str.split(';').find(item => item.includes('filename'))
  if (!nameStr) return false
  const name = nameStr.split('=')[1]
  return decodeURIComponent(name)
}