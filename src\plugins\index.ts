import type { App } from 'vue'
import { VXETable, Table, Column, Tooltip, VxeTableFilterModule } from 'vxe-table'
import { reduce } from 'lodash-es'
import { renders } from './render'
/**
 * @description vxeTable 配置
 * */
export function setupGlobVxeTable(app: App) {
  // 初始配置
  VXETable.config({
    tooltip: {
      zIndex: 9999
    }
  })
  // 按需引入
  reduce(
    [Table, Column, Tooltip, VxeTableFilterModule],
    (app: App, com: any) => (0, app.use)(com),
    app
  )
  // 格式化

  // 指令

  // 渲染器
  renders.forEach(render => {
    VXETable.renderer.add(render.name, render.options)
  })
}
