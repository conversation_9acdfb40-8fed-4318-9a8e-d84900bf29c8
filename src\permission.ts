/**
 * 路由权限控制模块
 * 负责处理路由导航守卫、权限验证和页面加载进度条
 */
import { router } from './router'
import { getToken } from '@/utils/auth' // 从本地存储获取token的工具函数
import { filterCurrentRoute } from '@/utils/util' // 过滤当前路由的工具函数
import NProgress from 'nprogress' // 页面加载进度条
import { useAccountStore } from '@/stores/modules/account' // 账户状态管理
import { usePermissionStore } from '@/stores/modules/permission' // 权限状态管理
import { checkPath } from '@/utils/route' // 检查路由路径的工具函数
import { unref } from 'vue' // Vue 3 工具函数，用于获取 ref 的值

/** 
 * 白名单路由列表
 * 这些路由不需要登录验证即可访问
 */
export const whiteList: string[] = ['/login', '/404', '/502', '/link']

/**
 * 全局前置守卫
 * 在路由跳转前执行，用于权限控制和页面加载状态管理
 */
router.beforeEach((to, from, next) => {
  // 开始页面加载进度条
  NProgress.start() 
  // 获取账户和权限的状态管理实例
  const useAccount = useAccountStore()
  const usePermission = usePermissionStore()

  // 判断是否有token（是否已登录）
  if (getToken()) {
    // 已经登录状态
    if (whiteList.includes(to.path)) {
      // 如果是访问白名单页面，直接放行
      next()
      NProgress.done() // 结束进度条
    } else {
      if (useAccount.hasLogin) {
        // 如果已经获取过用户信息
        // 检查路由路径：如果没有这个菜单就跳404，如果有菜单但是没有访问权限就跳503
        checkPath(to, next)
        NProgress.done() // 结束进度条
      } else {
        // 如果还没有获取用户信息，先获取用户信息和权限菜单
        useAccount.getUser() // 获取用户信息
        usePermission.getAuthMenuList().then(() => {
          // 获取权限菜单后，检查路由路径
          checkPath(to, next, true)
          NProgress.done() // 结束进度条
        })
      }
    }
  } else {
    // 未登录状态
    if (whiteList.includes(to.path)) {
      // 如果是访问白名单页面，直接放行
      next()
      NProgress.done() // 结束进度条
    } else {
      // 非白名单页面，重定向到登录页
      next('/login')
      NProgress.done() // 结束进度条
    }
  }
})

/**
 * 全局后置钩子
 * 在路由跳转完成后执行，确保进度条完成
 */
router.afterEach(() => {
  NProgress.done() // 结束进度条
})

/**
 * 全局解析守卫
 * 在导航被确认之前，同时在所有组件内守卫和异步路由组件被解析之后调用
 * 用于设置当前活动菜单
 */
router.beforeResolve((to) => {
  const permissionStore = usePermissionStore()
  // 根据当前路径从菜单列表中过滤出当前菜单
  const menu: any = filterCurrentRoute(
    unref(permissionStore.menuList) as any,
    unref(to.path)
  ) 
  // 更新当前活动菜单状态
  permissionStore.setCurrentMenu(menu)
})