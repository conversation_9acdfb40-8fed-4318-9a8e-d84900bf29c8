<template>
  <RxkRadio
    :list="list"
    v-model="innerValue"
    @focus="remoteMethod"
    :vertical="data.precisions === 1"/>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, unref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkRadio } from '@/components/common/RxkRadio'
import { getPageComponentData } from '@/apis/customerManage'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue', 'refreshDataList'])

const innerValue = computed({
  get () {
    console.log(props.modelValue, 'props.modelValue222')
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})
const flag = ref(true)
const list = ref([])
onMounted(() => {
  if (props.data.dataMark !== 2) {
    list.value = props.renderConfig.dataList || []
  }
})
function remoteMethod () {
  console.log(props.data)
  if (unref(flag) && props.data.dataMark === 2) {
    const postData = {
      columnId: props.data.id,
      columnUseScene: props.data.columnUseScene,
      tableCode: props.data.tableCode
    }
    getPageComponentData(postData).then(res => {
      list.value = res || []
      flag.value = false
      emit('refreshDataList', res)
    })
  }
}

</script>