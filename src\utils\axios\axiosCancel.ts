/**
 * Axios请求取消控制模块
 * 提供请求取消的能力，防止重复请求和页面切换时取消未完成的请求
 * 通过维护pendingMap记录待处理的请求，在需要时进行取消操作
 */
import type { AxiosRequestConfig, Canceler } from 'axios'
import axios from 'axios'
import { isFunc } from '@/utils/is'

/** 待处理请求Map，存储请求URL和对应的取消函数 */
let pendingMap = new Map<string, Canceler>()

/**
 * 获取请求的唯一标识
 * 基于请求方法、URL和请求数据生成唯一键
 * @param {AxiosRequestConfig} config - axios请求配置
 * @returns {string} 请求唯一标识
 */
export const getPendingUrl = (config: AxiosRequestConfig) => [config.method, config.url, JSON.stringify(config.data)].join('&')

/**
 * Axios请求取消控制类
 * 用于管理请求的取消操作
 */
export class AxiosCanceler {
  /**
   * 添加请求到待处理Map
   * 如果已存在相同请求，先移除旧请求，再添加新请求
   * @param {AxiosRequestConfig} config - axios请求配置
   */
  addPending (config: AxiosRequestConfig) {
    this.removePending(config)
    const url = getPendingUrl(config)
    config.cancelToken =
        config.cancelToken ||
        new axios.CancelToken((cancel) => {
          if (!pendingMap.has(url)) {
            pendingMap.set(url, cancel)
          }
        })
  }

  /**
   * 移除所有待处理的请求
   * 通常在路由切换时调用，避免页面切换后仍有请求响应导致的状态错误
   */
  removeAllPending () {
    pendingMap.forEach((cancel) => {
      cancel && isFunc(cancel) && cancel()
    })
    pendingMap.clear()
  }

  /**
   * 移除指定的待处理请求
   * 通常在请求完成或需要取消特定请求时调用
   * @param {AxiosRequestConfig} config - axios请求配置
   */
  removePending (config: AxiosRequestConfig) {
    const url = getPendingUrl(config)

    if (pendingMap.has(url)) {
      const cancel = pendingMap.get(url)
      cancel && cancel(url)
      pendingMap.delete(url)
    }
  }

  /**
   * 重置待处理请求Map
   * 清空所有待处理的请求记录
   */
  reset (): void {
    pendingMap = new Map<string, Canceler>()
  }
}
