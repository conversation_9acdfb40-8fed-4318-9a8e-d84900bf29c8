import type { Column } from 'element-plus'

export interface ColumnExt extends Partial<Column> {
  key: string
}

// 表头类型
export interface ColumnType {
  key: string
  title: string
  width?: string | number
  type?: 'selection' | 'index' | 'expand'
  slot?: string
  render?: Fn
  /** 同步数据 */
  syncData?: {
    /** 请求api */  
    api: (data?: any) => Promise<any>
    /** 值的key */
    value: string
    /** 标签的key */
    label: string
    /** 请求参数 */
    params: Recordable
    /** 数据 */
    data?: any[]
  }
  fixed?: true | 'left' | 'right'
  headerTooltipText?: string // 表头字段解释文案
  showed?: boolean
  showOverflow?: boolean
  sortable?: boolean
  field?: string
  desc?: string
  headerSlot?: string
  children?: ColumnType[]
  [key: string]: any
}
