<!-- 表格批量删选 -->
<template>
  <div class="tableBatchChoose">
    <el-popover
      placement="bottom"
      width="226"
      popper-class="tableBatchChoose-poper"
      trigger="click"
      :visible="visible"
    >
      <div class="poper-content">
        <div class="title">
          批量选择
        </div>
        <div class="content">
          <div class="main">
            <div class="item">
              <div class="num">序号</div>
              <el-input
                v-model="innterBatchData.startLimit"
                class="inputClass"
                @blur="startLimitBlur"
              />
            </div>
            <div class="item">
              <div class="separate"/>
            </div>
            <div class="item">
              <div class="num">序号</div>
              <el-input
                v-model="innterBatchData.endLimit"
                class="inputClass"
                @blur="endLimitBlur"
              />
            </div>
          </div>
          <div
            class="error"
            v-if="showError"
          >*{{errorMsg}}</div>
        </div>

        <div class="btn-block">
          <el-button
            type="default"
            size="small"
            @click="visible = false"
          >取消</el-button>
          <el-button
            type="primary"
            size="small"
            @click="sure"
          >确定</el-button>
        </div>
      </div>

      <template #reference>
        <i class="iconfont icon-piliangcaozuo" @click="visible = true"/>
      </template>
    </el-popover>
  </div>
</template>

<script lang="ts" setup>
import { isNullOrUndefOrEmpty } from '@/utils/is'
import { ref, unref } from 'vue'
import type { BatchData, BatchSelectPageInfo, TableBatchSelectActionType, Props } from './hooks/useTableBatchSelect'
// import { batchSelectOrderIdList } from '@/apis/common'

const visible = ref<boolean>(false)
const showError = ref<boolean>(false)
const errorMsg = ref<string>('')
const innterBatchData = ref<BatchData>({
  startLimit: 1,
  endLimit: 10000
})
const batchSelectIds = ref<Recordable[]>([])
const batchSelectPageInfo = ref<BatchSelectPageInfo>()

const props = withDefaults(defineProps<Props>(), {
  tableTotal: 0
})

const emit = defineEmits(['registerTableBatchSelect', 'tableBatchSelectSure'])

function inputCheck (startLimit:any, endLimit:any) {
  if (isNullOrUndefOrEmpty(startLimit) || isNullOrUndefOrEmpty(endLimit)) {
    showError.value = true
    errorMsg.value = '输入序号不能为空'
    return false
  }
  const reg = /^[1-9]\d*$/
  if (!reg.test(startLimit) || !reg.test(endLimit)) {
    showError.value = true
    errorMsg.value = '仅支持输入正整数'
    return false
  }
  if (Number(startLimit) > props.tableTotal || Number(endLimit) > props.tableTotal) {
    showError.value = true
    errorMsg.value = '输入序号超出当前最大值'
    return false
  }
  if (Number(startLimit) > Number(endLimit)) {
    showError.value = true
    errorMsg.value = '开始值应小于结束值'
    return false
  }
  if (Number(endLimit) - Number(startLimit) + 1 > 10000) {
    showError.value = true
    errorMsg.value = '单次批量操作上限为10000'
    return false
  }

  if (Number(endLimit - startLimit) < 0) {
    showError.value = true
    errorMsg.value = '单次批量操作下限为1'
    return false
  }
  return true
}

function startLimitBlur () {
  showError.value = !inputCheck(innterBatchData.value?.endLimit, innterBatchData.value?.endLimit)
}

function endLimitBlur () {
  showError.value = !inputCheck(innterBatchData.value?.startLimit, innterBatchData.value?.endLimit)
}

function initBatchData (batchData: BatchData) {
  console.log(props, 'initBatchData')
  innterBatchData.value = unref(batchData)
}
const tableBatchSelectActionType: TableBatchSelectActionType = {
  initBatchData,
  getBatchSelectIds,
  getBatchSelectPageInfo,
  getBatchSelectIdsList
}
emit('registerTableBatchSelect', tableBatchSelectActionType)

// 获取批量筛选idbatchSelectOrderIdList
// async function getBatchSelectIds (params: Recordable) {
//   batchSelectIds.value = await batchSelectOrderIdList({
//     ...params
//   })
// }

// 计算批量选择起始页数据
function computeBatchSelect (batchData:BatchData, pageSize: number) {
  // 计算开始页码
  const tableBatchStartPage = Math.floor((Number(batchData.startLimit) - 1) / pageSize) + 1
  // 计算开始页面的开始序号
  const tableBatchStartIndex = (Number(batchData.startLimit) - 1) % pageSize + 1
  console.log('开始页码:' + tableBatchStartPage, '开始序号:' + tableBatchStartIndex, '当前分页数：' + pageSize)
  // 计算结束页码
  const tableBatchEndPage = Math.floor((Number(batchData.endLimit) - 1) / pageSize) + 1
  // 计算结束的序号
  const tableBatchEndIndex = (Number(batchData.endLimit) - 1) % pageSize + 1
  console.log('结束页码:' + tableBatchEndPage, '结束序号:' + tableBatchEndIndex, '当前分页数：' + pageSize)
  batchSelectPageInfo.value = {
    tableBatchStartPage,
    tableBatchStartIndex,
    tableBatchEndPage,
    tableBatchEndIndex
  }
  return unref(batchSelectPageInfo.value)
}

function getBatchSelectPageInfo (batchData:BatchData, pageSize: number) {
  return computeBatchSelect(batchData, pageSize)
}

function getBatchSelectIdsList () {
  return unref(batchSelectIds.value)
}

async function sure () {
  if (!showError.value && inputCheck(innterBatchData.value?.startLimit, innterBatchData.value?.endLimit)) {
    emit('tableBatchSelectSure', {
      startLimit: innterBatchData.value?.startLimit,
      endLimit: innterBatchData.value?.endLimit
    })
    visible.value = false     
  }
}

</script>

<style lang="scss" scoped>
.tableBatchChoose{
  .batchChoose{
    font-size: 12px;
    color: #5687FF;
    text-decoration: underline;
    cursor: pointer;
  }
  .icon-piliangcaozuo {
    color: $secondary-text-color;
    font-size: $saas-font-size-small;
    cursor: pointer;
    &:hover {
      color: $primary-color;
    }
  }
}
.tableBatchChoose-poper{
  padding: 0;
  .poper-content{
    .title {
      font-size: 14px;
      font-weight: 700;
      color: $gray;
      margin-bottom: 16px;
    }
    .content{
      .inputClass{
        width: 92px;
      }
      .main{
        display: flex;
        align-items: center;
        .item {
          .num{
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
          }
          .separate {
            &::after{
              content: "";
              width: 8px;
              border-top: 1px solid #999;
              margin: 0 4px;
              display: inline-block;
            }
          }
        }
      }
      .error {
        font-size: 12px;
        color: #E05D58;
      }
    }
    .btn-block{
      padding: 28px 0 0 0;
      text-align: right;
    }
    
  }
}
</style>