<template>
  <el-date-picker
    v-model="innerValue"
    type="daterange"
    unlink-panels
    range-separator="至"
    start-placeholder="开始时间"
    end-placeholder="结束时间"
    :picker-options="state.pickerOptions"
    value-format="YYYY-MM-DD"
    @change="timeChange"
    style="width: 280px;"
    class="timeSelector"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
const props = defineProps({
  value: {
    default: [],
    type: Array
  }
})
const state = reactive({
  pickerOptions: {
    shortcuts: [{
      text: '本周',
      onClick (picker) {
        const today = new Date()
        const dayOfWeek = today.getDay() // 星期天为0，星期六为6
        let thursdayDiff = 0
        // 计算当前日期与周四差
        if (dayOfWeek < 4) {
          thursdayDiff = dayOfWeek + 3
        }
        if (dayOfWeek > 4) {
          thursdayDiff = dayOfWeek - 4
        }
        const thisThursday = new Date(today.getFullYear(), today.getMonth(), today.getDate() - thursdayDiff, 0, 0, 0) // 获取周四的日期对象
        const thisWednesday = new Date(thisThursday.getFullYear(), thisThursday.getMonth(), thisThursday.getDate() + 6, 23, 59, 59) // 获取周三的日期对象
        picker.$emit('pick', [thisThursday, thisWednesday])
      }
    }, {
      text: '近1月',
      onClick (picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        picker.$emit('pick', [start, end])
      }
    }, {
      text: '近3月',
      onClick (picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        picker.$emit('pick', [start, end])
      }
    }]
  }
})
const emit = defineEmits(['timeChange', 'update:value'])
const innerValue = computed({
  get () {
    return props.value
  },
  set (val) {
    emit('update:value', val)
  }
})
function timeChange (val: string) {
  emit('timeChange', val)
}
</script>
<style scoped lang="scss">
.timeSelector {
  :deep(.el-range-input){
    width: 99px;
  }
  :deep(.el-range-separator){
    font-size: 12px;
    width: auto;
  }
}
</style>
