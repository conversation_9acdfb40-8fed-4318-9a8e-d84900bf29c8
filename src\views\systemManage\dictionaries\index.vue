<template>
  <!-- 搜索表单 -->
  <SearchFilter @register="registerSetting" @search="search" />
  <div class="tw-ml-[16px] tw-mb-[16px]">
    <RxkButton @click="handleAdd" type="primary">新增</RxkButton>
  </div>
  <!-- 表格 -->
  <RxkVTable @register="registerTable">
    <template #modifiableSlot="{ row }">
      <div>
        <span
          :style="{
            backgroundColor: row.edit ? '#5DC144' : '#F44D4D'
          }"
        />
        <span>{{ row.edit ? '是' : '否' }}</span>
      </div>
    </template>
    <template #operateSlot="{ row }">
      <div class="tw-flex">
        <RxkButton class="table-action-btn"
                   text
                   @click="handleView(row)"
        >查看</RxkButton
        >
        <RxkButton class="table-action-btn"
                   text
                   @click="handleEdit(row)"
        >修改</RxkButton
        >
      </div>
    </template>
  </RxkVTable>
  <!-- 编辑对话框 -->
  <RxkDialog
    v-model="addDialogVisible"
    :title="isAdd ? '新增' : '编辑'"
    width="800px"
    @sure="addSure"
  >
    <div class="tw-m-[16px]">
      <el-form
        ref="formDataDom"
        require-asterisk-position="right"
        :model="formData"
        :rules="addRules"
      >
        <el-form-item label="字典名称" prop="name">
          <RxkInput placeholder="请输入字典名称" v-model="formData.name" />
        </el-form-item>
        <el-form-item label="字典编码" prop="code">
          <RxkInput
            :disabled="!isAdd"
            placeholder="请输入字典编码"
            v-model="formData.code"
          />
        </el-form-item>
        <el-form-item label="字段类型" prop="type">
          <RxkSelect
            :disabled="!isAdd"
            placeholder="请输入字段类型"
            v-model="formData.type"
            :list="dictionariesTypeList"
          />
        </el-form-item>
        <el-form-item label="字段描述" prop="summary">
          <RxkInput
            type="textarea"
            placeholder="请输入字段描述"
            :autosize="{ minRows: 8, maxRows: 10 }"
            v-model="formData.summary"
          />
        </el-form-item>
      </el-form>
    </div>
  </RxkDialog>
  <!-- 查看对话框 -->
  <DictionariesDetail
    v-if="viewDialogVisible"
    :groupCode="rowData.code"
    :id="rowData.id"
    :type="rowData.type"
    v-model:visible="viewDialogVisible"
  />
</template>

<script setup lang="tsx">
import { ref, unref } from 'vue'
import type { ColumnType } from '@/types/table'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { SearchFilter } from '@/components/business/searchFilter'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import {
  dataGroupPageList,
  dataGroupInsert,
  dataGroupUpdate
} from '@/apis/system'
import { RxkDialog } from '@/components/common/RxkDialog'
import { RxkInput } from '@/components/common/RxkInput'
import { dictionariesTypeList } from '@/enums/settlemenManage'
import { RxkSelect } from '@/components/common'
import DictionariesDetail from '@/views/systemManage/dictionaries/DictionariesDetail.vue'
import { cloneDeep } from '@/utils/tools'

defineOptions({
  name: 'DictionariesPage'
})

const formDataDom = ref()
const addDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isAdd = ref(false)

interface FormData {
  /** 主键 */
  id?: string
  /** 字典名称 */
  name: string
  /** 字典编码 */
  code: string
  /** 字段类型 */
  type: string
  /** 字段描述 */
  summary: string
}

const formDataInit = {
  name: '',
  code: '',
  type: '',
  summary: ''
}

const formData = ref<FormData>(cloneDeep(formDataInit))

const addRules = ref({
  name: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
  type: [{ required: true, message: '请输入字段类型', trigger: 'blur' }],
  summary: [{ required: true, message: '请输入字段描述', trigger: 'blur' }]
})

interface SearchCacheType {
  name?: string
  code?: string
  summary?: string
}

const searchCache: SearchCacheType = {}

const searchInfo: SearchCacheType = {
  name: searchCache?.name || undefined,
  code: searchCache?.code || undefined,
  summary: searchCache?.summary || undefined
}

function search (data: Recordable) {
  searchInfo.name = data.name || undefined
  searchInfo.code = data.code || undefined
  searchInfo.summary = data.summary || undefined
  reload()
}

const searchFormData = ref<FormSchema[]>([
  {
    key: 'name',
    component: 'Input',
    val: searchCache?.name || '',
    fieldName: '字典名称'
  },
  {
    key: 'code',
    component: 'Input',
    val: searchCache?.code || '',
    fieldName: '字典编码'
  },
  {
    key: 'summary',
    component: 'Input',
    val: searchCache?.summary || '',
    fieldName: '字段描述'
  }
])

// 表格列配置
const columns = ref<ColumnType[]>([
  {
    key: 'name',
    title: '字典名称'
  },
  {
    key: 'code',
    title: '字典编码'
  },
  {
    key: 'type',
    title: '字段类型',
    render: ({ cellData }) => {
      return (
        dictionariesTypeList.find((item) => item.value === cellData.type)
        ?.label || '-'
      )
    }
  },
  {
    key: 'summary',
    title: '字段描述'
  },
  {
    key: 'operate',
    title: '操作',
    fixed: 'right',
    slot: 'operateSlot',
    width: 120
  }
])

const rowData = ref<any>(undefined)

const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})

// 表格配置
const [registerTable, { reload }] = useTable({
  api: dataGroupPageList,
  columns: getBasicColumns(),
  searchInfo: { model: searchInfo },
  immediate: true
})

function getBasicColumns (): ColumnType[] {
  return unref(columns)
}

/** 查看 */
function handleView (scope: any) {
  rowData.value = scope
  viewDialogVisible.value = true
}

/** 修改 */
function handleEdit (scope: any) {
  rowData.value = scope
  formData.value = cloneDeep(scope)
  addDialogVisible.value = true
  isAdd.value = false
}

function handleAdd () {
  formData.value = cloneDeep(formDataInit)
  addDialogVisible.value = true
  isAdd.value = true
}

function addSure () {
  formDataDom.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const request = isAdd.value ? dataGroupInsert : dataGroupUpdate
        await request(formData.value)
        await reload()
        ElMessage.success(isAdd.value ? '新增成功' : '编辑成功')
        addDialogVisible.value = false
      } catch (error) {
        console.log(error)
      }
    }
  })
}
</script>
