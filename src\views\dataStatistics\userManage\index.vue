<template>
  <div class="sys-param-page">
    <SearchFilter @register="registerSetting" @search="search" />

    <div class="table-box">
      <RxkVTable @register="registerTable">
        <template #operateSlot="{ row }">
          <RxkButton
            class="table-action-btn"
            @click="openDetail(row)"
            text
          >详情</RxkButton>
        </template>
      </RxkVTable>
    </div>

    <!-- 详情 -->
    <UserDetailPopUp
      v-if="state.editVisible"
      :rowData="state.rowData"
      @close="closeDetail"
    />
  </div>
</template>

<script setup lang="tsx">
import { unref, reactive } from 'vue'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { userManage } from './data'
import UserDetailPopUp from './components/userDetailPopUp.vue'
import { fetchGetUserList } from '@/apis/carlife'

const state = reactive({
  rowData: {},
  editVisible: false
})
// 搜索表单数据
const [registerSetting] = useSearch({
  schemas: unref(userManage.searchFormData)
})
const getBasicColumns = () => unref(userManage.columns)
const searchInfo: Record<string, any> = {
  model: {
    channelId: '',
    device: '',
    phone: '',
    userName: '',
    visitTimeEnd: '',
    visitTimeStart: ''
  }
}

const [registerTable, { reload, setSearchInfo }] = useTable({
  api: fetchGetUserList,
  columns: getBasicColumns(),
  searchInfo: searchInfo,
  immediate: true // 是否立刻请求
})
const search = (val: { [k: string]: any }) => {
  Object.assign(searchInfo.model, val)

  searchInfo.model.visitTimeStart = val?.visitTime?.[0] ?? ''
  searchInfo.model.visitTimeEnd = val?.visitTime?.[1] ?? ''
  delete searchInfo.model.visitTime

  setSearchInfo(searchInfo)
  reload()
}

/** 详情 */
const openDetail = (rowData: any) => {
  state.rowData = rowData
  state.editVisible = true
}
/** 关闭弹窗 */
const closeDetail = (refresh: boolean) => {
  state.editVisible = false
  if (refresh) {
    reload()
  }
}

</script>

<style lang="scss" scoped>
.sys-param-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-form--inline .el-form-item {
    margin-right: 40px;
  }

  .table-box {
    width: 100%;
    flex: 1;
    overflow-y: hidden;
  }
  .btn-box {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 16px;
    justify-content: flex-end;
  }
}
</style>
