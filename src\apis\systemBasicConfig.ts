import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'

/**
 * @name 角色管理
 */

const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

/** 枚举映射 */

// 查询字段映射模板列表-a端
export const getTemplateConfigList = (data: any) =>
  request.post(
    '/crm/api_field_reflect/getTemplateConfigList',
    data,
    jsonHeaderConfig
  )
// 查询基础字段
export const getBaseConfigFieldApi = (data: any) =>
  request.post(
    '/crm/api_field_reflect/getBaseConfigField',
    data,
    jsonHeaderConfig
  )
// 更新字段映射值
export const updateFieldApi = (data: any) =>
  request.post(
    '/crm/api_field_reflect/updateApiFieldReflectTemplateConfig',
    data,
    jsonHeaderConfig
  )

// 查询配置id指定渠道的字段映射模板
export const getFieldReflectByConfigId = (id: string) =>
  request.post(`/crm/api_field_reflect/getFieldReflectByConfigId/${id}`, {})

// 删除字段映射
export const deleteFieldApi = (id: string) =>
  request.post(`/crm/api_field_reflect/deleteTemplate/${id}`, {})

// 新增自定义渠道
export const addCustomChannel = (data: Recordable) => request.post('/crm/api_field_reflect/saveApiChannel', data, jsonHeaderConfig)

// 更新api配置示例-a端
export const updateApiFieldReflectTemplateConfigDemo = (data: any) =>
  request.post(
    '/crm/api_field_reflect/updateApiFieldReflectTemplateConfigDemo',
    data,
    jsonHeaderConfig
  )

// 获取A端登录时限配置信息
export const getLoginTimeoutConfig = () =>
  request.get('/admin/user/loginConfig', {})

// 保存A端登录时限配置信息
export const saveLoginTimeoutConfig = (data: any) =>
  request.post('/admin/user/saveLoginConfig', data, jsonHeaderConfig)
