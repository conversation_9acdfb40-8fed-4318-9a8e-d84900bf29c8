<template>
  <div class="tw-pl-[16px] tw-pt-[16px]">
    <el-tabs v-model="searchInfo.type">
      <el-tab-pane label="IOS" name="1" />
      <el-tab-pane label="Andriod" name="2" />
    </el-tabs>
  </div>
  <div class="tw-ml-[16px] tw-mb-[16px]">
    <RxkButton @click="handleAdd" type="primary">新增</RxkButton>
  </div>
  <!-- 表格 -->
  <RxkVTable @register="registerTable">
    <template #operateSlot="{ row }">
      <div class="tw-flex tw-gap-[8px]">
        <RxkButton
          class="table-action-btn"
          v-if="row.status === 0"
          text
          @click="handlePublish(row)"
        >发布</RxkButton
        >
        <RxkButton class="table-action-btn"
                   text
                   @click="handleEdit(row)"
        >修改</RxkButton
        >
      </div>
    </template>
  </RxkVTable>
  <!-- 编辑对话框 -->
  <RxkDialog
    v-model="addDialogVisible"
    :title="isAdd ? '新增' : '编辑'"
    width="800px"
    @sure="addSure"
  >
    <div class="tw-m-[16px]">
      <el-form
        ref="formDataDom"
        label-width="auto"
        require-asterisk-position="right"
        :model="formData"
        :rules="addRules"
      >
        <el-form-item label="版本号" prop="version">
          <RxkInput placeholder="请输入版本号" v-model="formData.version" />
        </el-form-item>
        <el-form-item label="版本名称" prop="versionName">
          <RxkInput
            placeholder="请输入版本名称"
            v-model="formData.versionName"
          />
        </el-form-item>
        <el-form-item label="版本描述" prop="remarks">
          <RxkInput
            type="textarea"
            placeholder="请输入版本描述"
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="formData.remarks"
          />
        </el-form-item>
        <el-form-item label="是否强制更新" prop="forceUpdate">
          <RxkRadio
            placeholder="请选择是否强制更新"
            v-model="formData.forceUpdate"
            :list="versionUpdateStatusEnum"
          />
        </el-form-item>
        <el-form-item label="是否开启AB面" prop="goInto">
          <RxkRadio
            placeholder="请选择是否开启AB面"
            v-model="formData.goInto"
            :list="iosABEnum"
          />
        </el-form-item>
        <el-form-item label="下载地址" prop="downloadUrl">
          <RxkInput
            placeholder="请输入下载地址"
            v-model="formData.downloadUrl"
          />
        </el-form-item>
        <el-form-item label="包名" prop="platform">
          <RxkInput placeholder="请输入包名" v-model="formData.platform" />
        </el-form-item>
        <el-form-item label="版本补丁" prop="patchUuid">
          <RxkInput placeholder="请输入版本补丁" v-model="formData.patchUuid" />
        </el-form-item>
      </el-form>
    </div>
  </RxkDialog>
</template>

<script setup lang="tsx">
import { ref, unref, watch, reactive } from 'vue'
import type { ColumnType } from '@/types/table'
import { RxkVTable } from '@/components/common/RxkVTable'
import { RxkButton } from '@/components/common/RxkButton'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { RxkDialog } from '@/components/common/RxkDialog'
import { RxkInput } from '@/components/common/RxkInput'
import { RxkRadio } from '@/components/common'
import { cloneDeep } from '@/utils/tools'
import {
  versionUpdateStatusEnum,
  iosABEnum,
  versionStatusEnum
} from '@/enums/carlife'
import {
  appVersionAdd,
  appVersionUpdate,
  appVersionSelectByPage
} from '@/apis/appManage'

defineOptions({
  name: 'AppManageVersions'
})

const formDataDom = ref()
const addDialogVisible = ref(false)
const isAdd = ref(false)
const searchInfo = reactive({
  type: '1'
})

interface FormData {
  /** 主键 */
  id?: string
  /** 版本号 */
  version: string
  /** 版本名称 */
  versionName: string
  /** 状态 */
  status?: string
  /** 版本描述 */
  remarks: string
  /** 是否强制更新 */
  forceUpdate: number
  /** 是否开启AB面 */
  goInto: number
  /** 下载地址 */
  downloadUrl: string
  /** 包名 */
  platform: string
  /** 版本补丁 */
  patchUuid: string
  /** 发布时间 */
  createTime?: string
}

const formDataInit = {
  version: '',
  versionName: '',
  remarks: '',
  forceUpdate: 0,
  goInto: 0,
  downloadUrl: '',
  platform: '',
  patchUuid: ''
}

const formData = ref<FormData>(cloneDeep(formDataInit))

const addRules = ref({
  downloadUrl: [{ required: true, message: '请输入下载地址', trigger: 'blur' }],
  platform: [{ required: true, message: '请输入包名', trigger: 'blur' }]
})

// 表格列配置
const columns = ref<ColumnType[]>([
  {
    key: 'version',
    title: '版本号'
  },
  {
    key: 'versionName',
    title: '版本名称'
  },
  {
    key: 'status',
    title: '状态',
    render: ({ cellData }) => {
      return (
        versionStatusEnum.find((item) => item.value === cellData.status)
        ?.label || '-'
      )
    }
  },
  {
    key: 'remarks',
    title: '版本描述'
  },
  {
    key: 'forceUpdate',
    title: '是否强制更新',
    render: ({ cellData }) => {
      return (
        versionUpdateStatusEnum.find(
          (item) => item.value === cellData.forceUpdate
        )?.label || '-'
      )
    }
  },
  {
    key: 'goInto',
    title: '是否开启AB面',
    render: ({ cellData }) => {
      return (
        iosABEnum.find((item) => item.value === cellData.goInto)?.label || '-'
      )
    }
  },
  {
    key: 'downloadUrl',
    title: '下载地址'
  },
  {
    key: 'platform',
    title: '包名'
  },
  {
    key: 'patchUuid',
    title: '版本补丁'
  },
  {
    key: 'createTime',
    title: '发布时间'
  },
  {
    key: 'operate',
    title: '操作',
    fixed: 'right',
    slot: 'operateSlot',
    width: 120
  }
])

// 表格配置
const [registerTable, { reload }] = useTable({
  api: appVersionSelectByPage,
  columns: getBasicColumns(),
  searchInfo: {
    model: searchInfo
  },
  immediate: true
})

function getBasicColumns (): ColumnType[] {
  return unref(columns)
}

/** 发布 */
async function handlePublish (scope: any) {
  const data = cloneDeep(scope)
  data.status = 1
  try {
    await appVersionUpdate(data)
    ElMessage.success('发布成功')
    await reload()
  } catch (error) {
    console.log(error)
  }
}

/** 编辑参数 */
function handleEdit (scope: any) {
  formData.value = cloneDeep(scope)
  addDialogVisible.value = true
  isAdd.value = false
}

function handleAdd () {
  formData.value = cloneDeep(formDataInit)
  addDialogVisible.value = true
  isAdd.value = true
}

watch(searchInfo, () => {
  reload()
})

function addSure () {
  formDataDom.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const api = isAdd.value ? appVersionAdd : appVersionUpdate
        await api({ ...formData.value, type: searchInfo.type })
        await reload()
        ElMessage.success(isAdd.value ? '新增成功' : '编辑成功')
        addDialogVisible.value = false
      } catch (error) {
        console.log(error)
      }
    }
  })
}
</script>
