<template>
  <div class="log-page">
    <SearchFilter
      ref="searchFilterRef"
      @register="registerSetting"
      @search="search"
    />
    <div class="table-box">
      <RxkVTable @register="registerTable" />
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { SearchFilter } from '@/components/business/searchFilter'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import type { ColumnType } from '@/types/table'
import { ref, unref, h } from 'vue'
import dayjs from 'dayjs'
import { getLog } from '@/apis/operationLog'

enum OperationType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE'
}

const columns = ref<ColumnType[]>([
  { key: 'realName', title: '用户姓名' },
  { key: 'mobile', title: '手机号' },
  { key: 'role', title: '角色' },
  {
    key: 'operateType',
    title: '操作类型',
    render: ({ cellData }) => {
      return h(
        'span',
        {},
        {
          INSERT: '添加',
          UPDATE: '编辑',
          DELETE: '删除'
        }[cellData?.operateType]
      )
    }
  },
  { key: 'content', title: '操作内容' },
  { key: 'createTime', title: '时间' }
])

const searchInfo: { model: Recordable } = {
  model: {
    operateType: '',
    realName: '',
    timeStart: dayjs().startOf('date').format('YYYY-MM-DD HH:mm:ss'),
    timeEnd: dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss')
  }
}

const [registerTable, { reload, setSearchInfo }] = useTable({
  api: getLog,
  columns: getBasicColumns(),
  searchInfo: searchInfo,
  immediate: true,
  showRowIndex: false,
  pagination: {
    pageSize: 10,
    pageNum: 1
  }
})
function getBasicColumns (): ColumnType[] {
  return unref(columns)
}

const searchFilterRef = ref()
const searchFormData = ref<FormSchema[]>([
  {
    fieldName: '用户姓名',
    component: 'Input',
    key: 'realName',
    componentProps: { placeholder: '请输入用户姓名' },
    val: ''
  },
  {
    fieldName: '操作类型',
    key: 'operateType',
    component: 'Select',
    options: [
      { label: '添加', value: 'INSERT' },
      { label: '编辑', value: 'UPDATE' },
      { label: '删除', value: 'DELETE' }
    ],
    componentProps: {
      clearable: true,
      placeholder: '请选择操作类型'
    },
    val: ''
  },
  {
    fieldName: '时间范围',
    component: 'Datetimerange',
    key: 'createTime',
    val: [
      dayjs().startOf('date').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss')
    ],
    componentProps: {
      clearable: false,
      style: { width: '380px' },
      onChange: (val: any) => {
        const sevenDay = 24 * 60 * 60 * 1000 * 7
        const start = new Date(val[0]).getTime()
        const end = new Date(val[1]).getTime()
        if (end - start > sevenDay) {
          ElMessage.error('时间范围不能超过7天')
          const formData = searchFilterRef?.value?.formData
          searchFormData.value[0].val = formData.realName
          searchFormData.value[1].val = formData.operateType
          searchFormData.value[2].val = [
            dayjs().startOf('date').format('YYYY-MM-DD HH:mm:ss'),
            dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss')
          ]
        }
      }
    }
  }
])

const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})

function search (data: Recordable) {
  searchInfo.model.realName = data.realName
  searchInfo.model.operateType = data.operateType

  if (data.createTime) {
    searchInfo.model.timeStart = data.createTime[0]
    searchInfo.model.timeEnd = data.createTime[1]
  } else {
    searchInfo.model.timeStart = ''
    searchInfo.model.timeEnd = ''
  }
  setSearchInfo(searchInfo)
  reload()
}
</script>

<style lang="scss" scoped>
.log-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .table-box {
    flex: 1;
    overflow-y: hidden;
  }
}
</style>
