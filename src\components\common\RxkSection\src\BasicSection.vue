<template>
  <div class="rxk-section">
    <el-input :min="0"
              v-model="min"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              :placeholder="minPlaceholder"/>
    <span>-</span>
    <el-input :min="0"
              v-model="max"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              :placeholder="maxPlaceholder"/>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  minLength: {
    type: String,
    default: ''
  },
  maxLength: {
    type: String,
    default: ''
  },
  minPlaceholder: {
    type: String,
    default: ''
  },
  maxPlaceholder: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['update:minLength', 'update:maxLength'])
const min = computed({
  get () {
    return props.minLength
  },
  set (val) {
    emit('update:minLength', val)
  }
})
const max = computed({
  get () {
    return props.maxLength
  },
  set (val) {
    emit('update:maxLength', val)
  }
})
</script>
<style lang="scss" scoped>
.rxk-section {
  display: flex;
  border: 1px solid #DCDFE6;
  border-radius: 3px;
  align-items: center;
  :deep(.el-input__wrapper) {
    box-shadow: none;
  }
}
</style>