<!-- 该组件主要用于列表顶部的搜索栏 可根据需求扩展 -->
<!-- TODO  扩展高级搜索 -->
<template>
  <div class="common-search-bar">
    <div class="search-form-box">
      <FormContent :config="config" :formInline="formInline" :inline="true"/>
    </div>
    <div class="action-button">
      <el-button type="primary" @click="onSearch">搜索</el-button>
      <el-button @click="resetSearch">重置</el-button>
      <el-button link
                 type="primary"
                 v-if="showHighSearch"
                 @click="openDrawer">
        <Filter style="width:16px;margin-right:2px;"/>
        高级筛选
      </el-button>
    </div>
    <el-drawer
      v-model="showDrawer"
      direction="rtl"
    >
      <template #header>
        <h4 class="search-drawer-title">高级筛选</h4>
      </template>
      <div class="drawer-container">
        <FormContent :config="config"
                     :formInline="formInline"
                     :inline="false"
                     labelWidth="100px"/>
      </div>
      <template #footer>
        <div style="display: flex;">
          <el-button type="primary" @click="confirmClick">确定</el-button>
          <el-button @click="cancelClick">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { Filter } from '@element-plus/icons-vue'
import FormContent from './formContent.vue'
import { reactive, onMounted, ref } from 'vue'
import { type SearchConfig } from './type'

const props = defineProps<{
  config: SearchConfig[];
  showHighSearch?: boolean;
}>()
const formInline = reactive<{ [k: string]: any }>({})
const showDrawer = ref(false)

onMounted(() => {
  props.config.forEach((item) => {
    formInline[item.code] = item.value
  })
})

const emit = defineEmits(['searchChange'])
const onSearch = () => {
  emit('searchChange', formInline)
}

const resetSearch = () => {
  props.config.forEach((item) => {
    formInline[item.code] = undefined
  })
  emit('searchChange', formInline)
}

const openDrawer = () => {
  showDrawer.value = true
}

const cancelClick = () => {
  showDrawer.value = false
}

const confirmClick = () => {
  showDrawer.value = false
  emit('searchChange', formInline)
}

</script>

<style scoped lang="scss">
.common-search-bar {
  display: flex;

  .search-form-box {
    flex: 1;
    height: 32px;
    overflow: hidden;
  }

  .action-button {
    flex: 0 280px;
    height: 32px;
    display: flex;
    justify-content: flex-end;
  }

  .search-drawer-title {
    color: #333;
    font-size: 14px;
    line-height: 22px;
  }

  :global(.el-drawer__body) {
    border-top: 1px solid #EBEEF5;
  }

  :global(.el-drawer__header) {
    margin-bottom: 16px;
  }

  :global(.el-drawer__body) {
    padding: 24px;
  }
}
</style>
