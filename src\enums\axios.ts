/**
 * axios请求头枚举
 * */

export enum ContentTypeEnum {
    // json
    JSON = 'application/json;charset=UTF-8',
    // form-data qs
    FORM_URLENCODED = 'application/x-www-form-urlencoded;charset=UTF-8',
    // form-data  upload
    FORM_DATA = 'multipart/form-data;charset=UTF-8'
}

/**
 * 接口返回code字段，代表不同返回类型
 * */
export enum ResponseCodeEnum {
    SUCCESS= 200, // 操作成功
}

export enum ResponseStatusEnum {
    SUCCESS= 200, // 操作成功
    SERVER_ERROR = 500, // 服务器报错
    UNAUTHORIZED = 401, // 服务器报错
    FORBIDDEN = 403, // 未授权的操作
    NO_FUND = 404 // 404
}