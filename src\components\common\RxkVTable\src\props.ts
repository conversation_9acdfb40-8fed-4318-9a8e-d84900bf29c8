import type { BasicColumnType } from './type'
import { type PropType } from 'vue'
export const basicProps = {
  columns: {
    type: Array as PropType<BasicColumnType[]>,
    default: () => ([])
  },
  data: {
    type: Array as PropType<Recordable[]>,
    default: () => ([])
  },
  api: {
    type: Function as PropType<(...arg: any[]) => Promise<any>>,
    default: null
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  showRowIndex: {
    type: Boolean,
    default: true
  },
  showbatchSelect: {
    type: Boolean,
    default: false
  },
  showFooter: {
    type: Boolean,
    default: false
  }
}