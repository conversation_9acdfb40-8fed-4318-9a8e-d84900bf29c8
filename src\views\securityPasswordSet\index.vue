<template>
  <div class="safe-page">
    <div class="title-box">
      <span class="title">安全密码设置</span>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
    <el-form
      :model="formParams"
      class="tw-mt-3"
      ref="reLoginRef"
      @submit.prevent
    >
      <el-form-item
        label="安全密码"
        prop="pass"
        :rules="[
          {
            required: true,
            trigger: 'blur',
            validator: validatePass
          }
        ]"
      >
        <el-input
          v-model.trim="formParams.pass"
          placeholder="请输入"
          style="width: 280px"
          maxlength="16"
          show-password
          type="password"
          autocomplete="new-password"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getSafetyCode, setSafetyCode } from '@/apis/user'

const formParams = ref({
  pass: ''
})

function validatePass (rule: any, value: any, callback: any) {
  const reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])(?![\u4e00-\u9fa5])[a-zA-Z0-9~!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,16}$/
  if (!value) {
    callback(new Error('请输入安全密码'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入8~16位字母,数字,特殊字符'))
  } else {
    callback()
  }
}

getSafetyCode().then((res) => {
  if (res) formParams.value.pass = res
})

const reLoginRef = ref()
function handleSave () {
  reLoginRef.value.validate((valid: boolean) => {
    if (valid) {
      setSafetyCode({ safetyCode: formParams.value.pass }).then(() => {
        ElMessage.success('保存成功')
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.safe-page {
  height: 100%;
  padding: 24px;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 38px;
    margin-bottom: 16px;
    color: #333333;
    font-size: 14px;
    font-weight: 700;
  }
}
</style>
