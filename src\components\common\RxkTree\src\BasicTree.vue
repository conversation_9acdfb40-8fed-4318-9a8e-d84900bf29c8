<template>
  <el-tree v-bind="$attrs"
           ref="treeDom"
           :data="data"
           :default-expanded-keys="defaultExpanded"
           @check-change="handleCheckChange"
           @node-expand="handleNodeExpand"
           @node-collapse="handleNodeCollapse">
    <template #default="{ node, data }">
      <slot :node="node" :data="data" />
    </template>
  </el-tree>
</template>

<script setup lang="ts">
import { useRxkTreeStore } from '@/stores/modules/rxkTree'
import { nextTick, onBeforeUnmount, ref, watch } from 'vue'
import type { PropType } from 'vue'
import { usePageCache } from '@/hooks/usePageCache'

const props = defineProps({
  data: {
    type: Array as PropType<any[]>,
    required: true
  },
  name: {
    type: String as PropType<string>,
    required: true
  },
  defaultExpandedKeys: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  isStorage: {
    type: Boolean as PropType<boolean>,
    default: true
  }
})

defineSlots<{
  default: { node: any; data: any }
}>()

const emits = defineEmits(['node-expand', 'node-collapse', 'check-change', 'is-check-data-change'])

const rxkTreeStore = useRxkTreeStore()

const treeDom = ref()
const defaultExpanded = ref<string[]>([])
const defaultChecked = ref<string[]>([])

const handleNodeExpand = (node: { id: string }, data, event) => {
  addKey(node.id)
  if (!props.isStorage) return
  props.name && rxkTreeStore.setExpands(props.name, node.id)
  emits('node-expand', node, data, event)
}

const handleNodeCollapse = (node: { id: string }, data, event) => {
  removeKey(node.id)
  if (!props.isStorage) return
  props.name && rxkTreeStore.setExpands(props.name, node.id)
  emits('node-collapse', node, data, event)
}

const handleCheckChange = (...args: any[]) => {
  emits('check-change', ...args)
  const currentChecked: string[] = treeDom.value.getCheckedKeys()
  if (currentChecked.length !== defaultChecked.value.length) {
    emits('is-check-data-change', true)
    return
  }
  const off = defaultChecked.value.some(item => !currentChecked.includes(item))
  emits('is-check-data-change', off)
}

const initTreeDefaultExpand = () => {
  if (props.defaultExpandedKeys?.length) {
    rxkTreeStore.setExpands(props.name, props.defaultExpandedKeys)
  }
  console.log(props.defaultExpandedKeys)
  defaultExpanded.value = rxkTreeStore.getExpands(props.name) || []
}

const removeDefaultExpand = () => {
  props.name && rxkTreeStore.removeExpands(props.name)
}

const getCheckedNodes = () => {
  return treeDom.value?.getCheckedNodes()
}

const getCheckedKeys = () => {
  return treeDom.value?.getCheckedKeys()
}

const setCheckedKeys = (keys: any[]) => {
  return treeDom.value?.setCheckedKeys(keys)
}

const getHalfCheckedNodes = () => {
  return treeDom.value?.getHalfCheckedNodes()
}

const getHalfCheckedKeys = () => {
  return treeDom.value?.getHalfCheckedKeys()
}

watch(() => props.data, () => {
  initTreeDefaultExpand()
  nextTick(() => {
    if (treeDom.value && !defaultChecked.value.length) {
      defaultChecked.value = treeDom.value.getCheckedKeys()
    }
  })
}, { immediate: true })

onBeforeUnmount(() => {
  removeDefaultExpand()
})

// 菜单里面缓存tree的ExpendKeys
const { getCache, updateCache } = usePageCache()
function addKey (id: string|number){
  let arr = getCache()?.treeExpendKeys || []
  arr.push(id)
  arr = new Set(arr)
  updateCache({ treeExpendKeys: [...arr] })
}

function removeKey (id: string|number){
  let arr = getCache()?.treeExpendKeys || []
  const index = arr.findIndex((el: string) => el === id)
  index > -1 && arr.splice(index, 1)
  updateCache({ treeExpendKeys: [...arr] })
}

defineExpose({
  getCheckedNodes,
  getCheckedKeys,
  getHalfCheckedNodes,
  getHalfCheckedKeys,
  setCheckedKeys
})
</script>

<style scoped lang="scss">

</style>