{"name": "rxk_saas_web_new", "version": "0.0.0", "private": true, "scripts": {"serve": "vite serve --mode development", "build": "run-p type-check build-only", "build:test": "cross-env vite build --mode test", "build:dev": "cross-env vite build --mode dev", "build:prod": "cross-env vite build --mode production", "build:pre": "cross-env vite build --mode pre", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "postinstall": "patch-package"}, "dependencies": {"@antv/x6": "^2.16.1", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.1", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-plugin-selection": "^2.2.1", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-react-shape": "^2.2.2", "@antv/x6-vue-shape": "^2.1.1", "@codemirror/autocomplete": "^6.15.0", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.26.0", "@element-plus/icons-vue": "~2.0.10", "@highlightjs/vue-plugin": "^2.1.0", "@vueuse/core": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "app-info-parser": "^1.1.6", "axios": "^1.5.1", "big.js": "^6.2.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "element-plus": "^2.4.3", "element-plus-china-area": "^1.0.4", "emoji": "^0.3.2", "file-saver": "^2.0.5", "highlight.js": "^11.9.0", "js-md5": "^0.8.3", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "qs": "^6.11.2", "vue": "^3.3.4", "vue-clipboard3": "^2.0.0", "vue-codemirror6": "^1.2.5", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0", "vxe-table": "4.6.25"}, "devDependencies": {"@rushstack/eslint-patch": "^1.5.1", "@tsconfig/node18": "^18.2.2", "@types/crypto-js": "^4.1.3", "@types/js-md5": "^0.7.1", "@types/lodash-es": "^4.17.10", "@types/node": "^18.18.5", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.9", "@typescript-eslint/eslint-plugin": "^6.8.0", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.52.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "patch-package": "^8.0.0", "pinia-plugin-persistedstate": "^3.2.0", "postcss": "^8.4.32", "province-city-china": "^8.5.7", "sass": "^1.69.4", "tailwindcss": "^3.3.6", "terser": "^5.22.0", "typescript": "4.8.4", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.11", "vite-plugin-html": "^3.2.2", "vue-tsc": "^1.8.19"}}