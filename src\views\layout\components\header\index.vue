<template>
  <div class="main-header">
    <span class="iconfont icon icon-menu-fold tw-cursor-pointer el-text--primary tw-px-4 tw-text-2xl"
          @click="onMenuCollapse"
          v-if="config.layout.shrink"
          style="color: var(--el-color-primary)"/>
    <ul class="account-itmes">
      <!-- <li>
        <span
          class="icon iconfont icon-search tw-text-[18px]"
          style="color: #666"
        />
      </li>
      <li>
        <span
          class="icon iconfont icon-xiaoxi tw-text-[18px]"
          style="color: #666"
        />
      </li> -->
      <li>
        <el-avatar
          class="tw-mr-2"
          :size="28"
          src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
        />
        <el-dropdown>
          <span class="el-dropdown-link">
            {{ userInfo.realName }}
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <!-- <el-dropdown-menu>
              <el-dropdown-item @click="handleUpdatePassword">修改密码</el-dropdown-item>
            </el-dropdown-menu> -->
            <el-dropdown-menu>
              <el-dropdown-item @click="handleAccount"
              >账户设置</el-dropdown-item
              >
            </el-dropdown-menu>
            <el-dropdown-menu>
              <el-dropdown-item @click="onLogout">退出</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </li>
    </ul>
    <RxkDialog
      v-model="visible"
      title="修改账号密码"
      width="480"
      confirmBtn="提交"
      @close="handleCancel"
      @sure="handleSubmit"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-width="auto"
        :rules="rules"
        style="padding: 24px"
      >
        <el-form-item label="手机号" prop="mobile">
          <RxkInput v-model="formData.mobile" disabled />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <RxkInput v-model="formData.code">
            <template v-slot:suffix>
              <span
                class="pointer"
                style="color: var(--el-primary-color)"
                @click="getCode"
              >
                {{ getCodeState ? '发送验证码' : timeout + 's' }}
              </span>
            </template>
          </RxkInput>
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <RxkInput type="password" v-model="formData.password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <RxkInput
            type="password"
            v-model="formData.confirmPassword"
            show-password
          />
        </el-form-item>
      </el-form>
    </RxkDialog>
  </div>
</template>

<script setup lang="ts">
import { Search, ArrowDown } from '@element-plus/icons-vue'
import { useAccountStore } from '@/stores/modules/account'
import { computed, ref, unref, reactive } from 'vue'
import { RxkDialog } from '@/components/common/RxkDialog'
import { RxkInput } from '@/components/common'
import type { FormRules } from 'element-plus'
import { useConfig } from '@/stores/modules/config'
import { getCodeApi, updatePassword } from '@/apis/user'
import { cloneDeep } from 'lodash-es'
import { useRouter } from 'vue-router'
import { showShade } from '@/utils/pageShade'
const router = useRouter()
const accountStore = useAccountStore()
const userInfo = computed(() => {
  return accountStore.userInfo
})
const config = useConfig()
const visible = ref(false)
const formRef = ref()
const formData = ref({
  mobile: '',
  code: '',
  confirmPassword: '',
  password: '',
  username: ''
})
const validatePass = (rule: any, value: any, callback: any) => {
  const regex =
    /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}\[\]:;"'<>,.?\/\\])[a-zA-Z\d~!@#$%^&*()_+`\-={}\[\]:;"'<>,.?\/\\]{8,16}$/
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (!regex.test(value)) {
    callback(new Error('请输入8-16位英文字母、数字和符号'))
  } else {
    callback()
  }
}
const validatePass1 = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('两次输入的密码不一致，请重新输入'))
  } else if (value !== formData.value.password) {
    callback(new Error('两次输入的密码不一致，请重新输入'))
  } else {
    callback()
  }
}
const rules = reactive<FormRules<any>>({
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  confirmPassword: [
    { required: true, validator: validatePass1, trigger: 'blur' }
  ],
  password: [{ required: true, validator: validatePass, trigger: 'blur' }]
})

const getCodeState = ref(true)
let clearInter: ReturnType<typeof setTimeout>
const timeout = ref(0)
const onMenuCollapse = () => {
  showShade('ba-aside-menu-shade', () => {
    config.setMenuCollapse(true)
  })
  config.setMenuCollapse(false)
}
const getCode = async () => {
  if (unref(getCodeState)) {
    try {
      getCodeState.value = false
      timeout.value = 60
      clearInter = setInterval(() => {
        timeout.value--
        if (timeout.value <= 0) {
          clearInterval(clearInter)
          getCodeState.value = true
        }
      }, 1000)
      await getCodeApi({}, { mobile: unref(formData).mobile })
    } catch (e) {
      clearInterval(clearInter)
      getCodeState.value = true
    }
  }
}
const handleUpdatePassword = () => {
  formData.value.mobile = userInfo.value?.mobile
  formData.value.username = userInfo.value?.realName
  visible.value = true
}
const handleSubmit = (fn: Function) => {
  console.log('submit')
  formRef.value.validate((valid) => {
    if (valid) {
      console.log('校验成功', formData.value)
      const data = cloneDeep(formData.value)
      delete data.mobile
      delete data.username
      updatePassword(data).then(() => {
        onLogout()
      })
    } else {
      fn && fn()
    }
  })
}
const handleCancel = () => {
  visible.value = false
  formRef.value?.resetFields()
}

function handleAccount () {
  router.push('/securityPasswordSet')
}

const onLogout = () => {
  accountStore.logout()
}
</script>

<style scoped lang="scss">
.main-header {
  width: 100%;
  height: 64px;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding-right: 16px;
}

.account-itmes {
  display: flex;
  margin-left: auto;

  > li {
    margin-left: 24px;
    display: flex;
    align-items: center;

    .avar {
      width: 28px;
      height: 28px;
      border-radius: 28px;
      background: #333;
      margin-right: 8px;
    }
  }
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  &:focus {
    outline: none;
  }
}
</style>
