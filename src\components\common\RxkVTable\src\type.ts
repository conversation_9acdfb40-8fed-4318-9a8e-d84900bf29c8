import type { Column } from 'element-plus'
import type { ColumnType } from '@/types/table'

export interface BasicColumnType extends MakeOptional<Column, 'width'> {
    key: string
}

export interface BasicTableProps {
    columns: ColumnType[];// 表头
    data?: Recordable[];// 表格数据
    api?: (...arg: any) => Promise<any>;// 表格获取接口
    searchInfo?: Recordable; // 搜索条件传递
    immediate?: boolean; // 是否立刻加载
    showSelection?: boolean; // 是否展示多选
    showPagination?: boolean; // 是否显示分页
    showRowIndex?: boolean; // 是否显示序号
    showSetting?: boolean;// 是否显示列表配置
    showFooter?: boolean;// 表尾合计
    pagination?: PaginationProps;
    showbatchSelect?: boolean; // 是否展示批量选择
    rowKey?: string;
    useCache?: boolean; // 是否开启缓存
    resizeAble?: boolean; // 是否开启表格列宽自适应
    moduleId?: string; // 模块id
    tableCode?: string; // 表格code [列宽使用]
    customTableConfig?:boolean; // 是否请求 custom_table_config/list 获取用户默认设置
    extraKey?: string; // 自定义表头列字段指定场景key值
    tableColumns?: ColumnType[]; // 列表可展示的字段数据
    isJoin?: boolean; // 是否拼接场景key值
    pageSizes?: number[]; // 表格分页条数
    isTreeNode?: boolean; // 是否开启treeNode模式
    isMultiLevelHeader?: boolean; // 是否属于多级表头
    total?: number;
}

export interface TableActionType {
    setProps: (props: Partial<BasicTableProps>) => void;
    setSearchInfo: (data: Recordable) => void;
    getSearchInfo: () => any;
    reload: () => Promise<void>;
    getSectionData: () => any[]; // 获取已选项数据
    getTableData: () => TableData; // 获取表格数据
    clearSelection: () => void; // 清空所有选择
    getSectionDataIds: () => any[]; // 获取已选数据ID
    setSortConfig: (data: Recordable) => void; // 设置排序配置
    getPaginationData:() => PaginationProps;
    setColumnFixed:(fixedItemArr: Recordable[], key?: string) => void; // 设置固定列(支持分组表头)
}

export interface FetchParams {

}

export interface PaginationProps {
    pageSize: number;
    pageNum: number
}

export interface TableData {
    extraMap: any,
    list: Recordable[],
    total: number
}

export interface ResizeBasicColumnType extends BasicColumnType {
    resizeWidth?: number
}

export interface ResizeColumnType extends ColumnType {
    resizeWidth?: number
}

// 列配置弹窗EmitType
export interface ColumnSettingEmitType {
    // 搜索项相关
    searchObj: Recordable, 
    customColorArr:string[]
    // 固定列相关
    fixedItemArr: Recordable[]
}