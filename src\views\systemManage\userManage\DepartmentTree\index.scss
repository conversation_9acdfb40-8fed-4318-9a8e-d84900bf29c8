.left-content {
    //flex: 0 250px;
    border-right: 1px solid #ebeef5;
    min-width: 220px;
    overflow: auto;
    height: 100%;
    padding-bottom: 50px;
  
    .tlt {
      color: #333333;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      padding: 13px 12px 5px;
    }
  
    .comp-name {
      color: #333333;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 40px;
      height: 40px;
      padding: 0 12px;
      margin-bottom: 9px;
      display: flex;
      align-items: center;
      background: var(--bg-10, #EEF3FF);
    }
  
    .add-btn {
      width: calc(100% - 32px);
      margin: 8px 12px 16px;
      border: 1px solid #5687FF;
      color: #5687FF;
    }

    :deep(.el-tree-node__content){
      height: 100%;
    }
  }

.custom-tree-node{
  color: #333333;
  font-size: 14px;
  padding: 8px 0;
  height: 20px;
  box-sizing: content-box;
}

  
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  
  .custom-drop {
    width: 20px;
    height: 20px;
    background: #E9E9EB;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
  }