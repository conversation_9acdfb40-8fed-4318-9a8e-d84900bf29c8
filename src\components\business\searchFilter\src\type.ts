export interface FormActionType {
  submit: () => void;
  setProps: (formProps: Partial<FormProps>) => void;
  resetFormData: () => void;
}

// 组件
export interface FormSchema {
  fieldName?: string;
  component: string;
  key: string;
  val?: any;
  options?: Array<any>;
  clearable?: boolean;
  componentProps?: any;
  getSelectApi?: () => Promise<any>;
  originalfieldName?: {
    label: string,
    value: string
  };
  render?:any;
  span?:number;
  showed?:boolean;
  renderLabel?:any;
  
}

export interface FormProps {
  schemas?: FormSchema[],
  defaultValue?:Record<string, any>
}