import { isEmpty } from 'lodash-es'
import { cloneDeep } from './tools'

/** 请求信息 */
interface StoreQueue {
  /** 队列 */
  queue: Array<[Function, Function]>
  /** 接口缓存 */
  store: any
  /** 请求状态 */
  state: boolean
  /** 请求时间 */
  requestTime: number
  /** 最大缓存时间 */
  storeMaxTime: number
  /** 请求接口 */
  api: (data: any) => Promise<any>
  /** 请求参数 */
  data: Record<string, any>
}

/** 请求队列 */
const queues: Record<string, StoreQueue> = {}

/**
 * 处理接口请求的核心函数
 * 该函数负责管理请求队列、缓存控制和请求状态
 */
const startRequest = (apiName: string) => {
  /**
   * 从队列中解构出当前接口的所有相关信息
   */
  const {
    state,
    queue,
    store,
    requestTime,
    api,
    storeMaxTime,
    data: apiData
  } = queues[apiName]

  // 如果当前请求正在进行中，则退出函数
  // 防止同一接口并发请求，确保请求的有序执行
  if (state) return false

  // 判断任务队列是否为空
  // 只有队列中有等待的Promise才会继续执行
  if (!isEmpty(queue)) {
    // 将请求状态设置为进行中
    queues[apiName].state = true

    // 从队列头部取出一对Promise的resolve和reject回调函数
    // 遵循先进先出(FIFO)原则处理请求队列
    const [resolve, reject] = queues[apiName].queue.shift()

    /**
     * 成功回调处理函数
     * 负责更新缓存、重置状态并递归处理队列中的下一个请求
     */
    const resolveCallback = (data) => {
      // 更新缓存数据
      queues[apiName].store = data
      // 将请求状态设置为已完成
      queues[apiName].state = false
      // 这里深拷贝 是怕有些使用地方修改了源数据 造成污染
      resolve(cloneDeep(data))
      // 递归调用以处理队列中的下一个请求
      startRequest(apiName)
    }

    /**
     * 获取当前时间戳，用于判断缓存是否过期
     */
    const currentDate = Date.now()

    // 判断缓存是否过期
    // storeMaxTime为0表示不启用缓存过期机制
    // 否则比较当前时间与上次请求时间的差值是否超过了最大缓存时间
    const isItExpired =
      storeMaxTime === 0 ? false : currentDate - requestTime >= storeMaxTime

    // 缓存策略判断：
    // 1. 如果没有缓存数据(首次请求)或缓存已过期，则发起新的API请求
    // 2. 如果有有效的缓存数据，则直接使用缓存数据响应
    if (isEmpty(store) || isItExpired) {
      // 发起实际的API请求
      api(apiData)
      .then((res) => {
        // 更新最后请求时间为当前时间
        queues[apiName].requestTime = Date.now()
        // 处理成功响应
        resolveCallback(res)
      })
      .catch((err) => {
        // 请求失败时，重置状态
        queues[apiName].state = false
        // 调用reject通知调用方请求失败
        reject(err)
        // 继续处理队列中的下一个请求
        startRequest(apiName)
      })
    } else {
      // 使用缓存数据直接响应
      resolveCallback(store)
    }
  }
}

export interface StoreRequestOptions {
  /** 接口 */
  api: (data: any) => Promise<any>
  /** 接口参数 */
  data?: Record<string, any>
  /** 接口缓存名称 */
  cacheName?: string
  /** 接口缓存时间 */
  storeMaxTime?: number
}

/**
 * ! api 是匿名函数时 cacheName一定要传！！！！
 * @return {Promise}
 */
export const storeRequest = ({
  api,
  data = {},
  cacheName = '',
  storeMaxTime = 0
}: StoreRequestOptions) => {
  let name = cacheName || api.name || 'default'
  // 这里处理同一个接口 不同的参数缓存
  name += JSON.stringify(data)
  return new Promise((resolve, reject) => {
    if (!queues[name]) {
      /** 请求信息 queue队列 api请求接口 data请求参数 store接口缓存 state请求状态 requestTime请求时间 */
      queues[name] = {
        queue: [],
        store: {},
        state: false,
        requestTime: 0,
        storeMaxTime,
        api,
        data
      }
    }

    queues[name].queue.push([resolve, reject])
    startRequest(name)
  })
}
