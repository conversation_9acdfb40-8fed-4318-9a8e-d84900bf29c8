<template>
  <RxkDialog
    v-model="payVisible"
    title="支付订单"
    width="480"
    :confirmBtn="'支付'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @sure="handleSure"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      class="form"
      :model="state.formData"
      :rules="formRules"
    >
      <el-form-item label="订单名称" prop="orderName" v-if="type === 'cart'">
        <RxkSelect
          v-model="state.formData.orderName"
          placeholder="请选择订单名称"
          :list="orderNameList"
        />
      </el-form-item>

      <el-form-item label="子订单是否可见" prop="subOrderVisible" v-if="type === 'cart'">
        <RxkSelect
          v-model="state.formData.subOrderVisible"
          placeholder="请选择子订单是否可见"
          :list="subOrderVisibleList"
        />
      </el-form-item>

      <el-form-item label="原价总金额">
        <span class="price">
          ¥{{ formatNumberToFixed(payOrderInfo?.originalPrice / 100, 2, 0) }}
        </span>
      </el-form-item>

      <el-form-item label="优惠信息" v-if="payOrderInfo?.discountAmount !== 0">
        <div class="coupon">
          优惠券抵扣：
          <span class="discount-price">
            {{ formatNumberToFixed(payOrderInfo?.discountAmount / 100, 2, 0) }}
          </span>
          元
        </div>
      </el-form-item>

      <div class="maypay">
        <span class="str">应付金额</span>
        <span class="totalprice">
          ¥<span>
            {{ formatNumberToFixed(payOrderInfo?.payAmount / 100, 2, 0) }}
          </span>
        </span>
      </div>

      <el-form-item label="订单备注">
        <el-input
          v-model="state.formData.remark"
          style="width: 400px"
          maxlength="300"
          show-word-limit
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
  </RxkDialog>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, unref, inject, onMounted } from 'vue'
import { RxkDialog } from '@/components/common/RxkDialog'
import { RxkSelect } from '@/components/common'
import { tradeBuyForApi, tradeShopCartBuyForApi } from '@/apis/mall'
import { formatNumberToFixed } from '@/utils/util'
import { orderNameList, subOrderVisibleList } from '@/enums/mall'

const emit = defineEmits(['update:modelValue', 'close'])
const props = defineProps({
  modelValue: Boolean,
  payOrderInfo: {
    type: Object,
    default: () => {}
  },
  type: {
    type: String,
    default: 'detail'
  },
  remark: {
    type: String,
    default: ''
  }
})
const payVisible = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})
onMounted(() => {
  state.formData.remark = props.remark
})
// 表单实例
const formRef = ref()
const tenantId = unref(inject('tenantId') as string)
const state = reactive({
  formData: {
    orderName: 'SYSTEM_OPEN_FEE',
    subOrderVisible: false,
    num: 0,
    couponId: null,
    itemId: null,
    specId: null,
    platform: 'SAAS',
    tenantId: tenantId,
    remark: ''
  } as Recordable
})
// 表单校验规则
const formRules: Recordable = {
  orderName: [{ required: true, message: '请选择', trigger: 'change' }],
  subOrderVisible: [{ required: true, message: '请选择', trigger: 'change' }]
}
function handleClose () {}

const handleSure = async () => {
  const noticeFunc = () => {
    ElMessageBox.confirm(
      '下单成功，审核通过后请联系机构在应用商城-订单管理-待支付订单处完成支付',
      '提示',
      {
        type: 'warning',
        showCancelButton: false,
        confirmButtonText: '关闭'
      }
    ).then(() => {
      emit('close')
    })
  }

  if (props.type === 'detail') {
    state.formData.num = props.payOrderInfo?.num
    state.formData.couponId = props.payOrderInfo?.couponId
    state.formData.itemId = props.payOrderInfo?.itemId
    state.formData.specId = props.payOrderInfo?.specId
    state.formData.buyExt = props.payOrderInfo?.buyExt

    delete state.formData.orderName
    delete state.formData.subOrderVisible
    
    await tradeBuyForApi({ 
      ...state.formData,
      actualPrice: parseInt(Math.round(props.payOrderInfo?.originalPrice))
    })
    
    noticeFunc()
  } else {
    formRef.value.validate(async (valid: boolean) => {
      if (valid) {
        const { remark, orderName, subOrderVisible } = state.formData
        await tradeShopCartBuyForApi({
          ids: props.payOrderInfo?.ids,
          remark,
          orderName,
          subOrderVisible
        })

        noticeFunc()
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.form {
  padding: 16px 24px;

  .price {
    color: #f44d4d;
    font-size: 14px;
    font-weight: 700;
  }

  .coupon {
    width: 350px;
    height: 34px;
    border-radius: 4px;
    background: #fff6ec;
    margin-left: 10px;
    color: #333333;
    font-size: 12px;
    padding-left: 16px;
    line-height: 34px;

    span {
      color: #ff8b20;
      font-weight: 700;
      font-size: 14px;
    }
  }

  .payform {
    margin-bottom: 10px;
  }
  .discount-price {
    margin-right: 4px;
  }
  .maypay {
    margin: 16px 0px;
    display: flex;
    align-items: center;
    padding: 16px;
    height: 60px;
    justify-content: space-between;
    border-radius: 4px;
    background: #f5f7fa;

    .str {
      color: #666666;
      font-size: 12px;
      font-weight: 700;
    }

    .totalprice {
      color: #f44d4d;
      font-size: 16px;
      font-weight: 700;

      span {
        font-size: 24px;
      }
    }
  }
}

/* :deep(.el-radio-group){
  display: block;
  .el-radio{
    display: block;
    margin-right: 0;
    margin-bottom: 10px;
    height: 40px;
    line-height: 40px;
  }
} */
.card-box {
  .item {
    width: 100%;
    height: 40px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    cursor: pointer;
    color: #333333;

    .left {
      display: flex;
      align-items: center;
    }

    .circle {
      width: 14px;
      height: 14px;
      border-radius: 100px;
      border: 1px solid #c7c9cc;
      margin-right: 16px;
      position: relative;
    }

    &.active {
      background: var(--bg-10, #eef3ff);
      border: 1px solid #aac3ff;
      .circle {
        background: #5687ff;
        border: 1px solid #5687ff;
      }
      .circle:after {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: var(--el-color-white);
        content: '';
        position: absolute;
        left: 4px;
        top: 4px;
      }
    }
  }
}
</style>
