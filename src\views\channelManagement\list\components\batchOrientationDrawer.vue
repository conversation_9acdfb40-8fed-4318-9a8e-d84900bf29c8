<template>
  <RxkDrawer
    v-model="showDrawer"
    size="700"
    title="渠道黑名单定向"
    @close="handleClose"
    @confirm="handleSubmit"
  >
    <template v-slot:content>
      <div class="tw-p-[24px]">
        <!-- :rules="drawerRules" -->
        <el-form
          ref="ruleFormRef"
          :model="formData"
          :validate-on-rule-change="false"
          label-position="top"
        >
          <el-row>
            <el-col :span="24" style="margin-bottom: 24px;">
              <div class="title">渠道信息</div>
            </el-col>
            <el-col :span="24">
              <el-form-item label="批量添加渠道" :rules="{required: true, message: '请选择渠道', trigger: 'change'}" prop="channelIds">
                <!-- <el-select-v2
                  v-model="formData.channelIds"
                  style="width: 100%"
                  :props="{
                    label: 'channelName',
                    value: 'channelId'
                  }"
                  multiple
                  filterable
                  clearable
                  :options="enumChannelOptions"
                  placeholder="请选择"
                /> -->
                <SelectAllV2 v-model="formData.channelIds"
                             :list="enumChannelOptions"
                             :collapseTags="false"
                             :propsObj="{ value: 'channelId', label: 'channelName' }"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin: 20px 0;" align="middle" >
            <div class="title">限制机构</div>
            <RxkButton @click="addTenant" style="border: 1px solid #5687ff;color: #5687ff;margin-left: 16px;">添加</RxkButton>
          </el-row>
          <el-form class="organ-item"
                   ref="tenantFormRef"
                   :model="item"
                   :rules="tenantRules"
                   v-for="(item, index) in tenantsList"
                   :key="item.id">
            <div class="del-btn" @click="delTenant(index)">
              <el-icon color="#fff" size="10"><SemiSelect /></el-icon>
            </div>
            <el-form-item class="organ-select" v-if="!item.tenantId" prop="tenantId">
              <el-select-v2
                v-model="item.tenantId"
                style="width: 100%"
                :props="{
                  label: 'tenantName',
                  value: 'tenantId'
                }"
                filterable
                clearable
                :options="showTenantOptions"
                @change="changeTenant($event, item)"
              />
            </el-form-item>
            <div class="organ-name" v-else>{{ getTenantName(item.tenantId) }}</div>
            <div class="product-switch">
              <span>全部产品生效</span>
              <el-switch v-model="item.type" :inactive-value="0" :active-value="1"  />
            </div>
            <el-form-item class="product-select"
                          :rules="{ validator: (rule: any, value: any, callback: Fn) => validatorProduct(rule, value, callback, item), trigger: 'change' }"
                          v-if="item.type === 0"
                          prop="productIds">
              <RxkSelect v-model="item.productIds"
                         multiple
                         clearable
                         filterable
                         :emptyText="item.tenantId ? '请确认产品信息' : '请先选择机构'"
                         :list="item.productOptions"/>
            </el-form-item>
          </el-form>
        </el-form>
      </div>
    </template>
  </RxkDrawer>
</template>
<script setup lang="ts">
import { RxkDrawer } from '@/components/common/RxkDrawer'
import { RxkSelect } from '@/components/common/RxkSelect'
import { defineExpose, ref, unref, computed } from 'vue'
import {
  getTenantsForBlack,
  getProductListByTenantId,
  batchChannelBlackList
} from '@/apis/channelManage'
import { SemiSelect } from '@element-plus/icons-vue'
import { generateUUID } from '@/utils/util'
import type { FormInstance, FormRules } from 'element-plus'
import SelectAllV2 from '@/components/business/selectAllV2/index.vue'
// import { useCommonEnum } from '../hooks/useCommonEnum'
// const { commonChannelField } = useCommonEnum()
withDefaults(
  defineProps<{
    enumChannelOptions: Array<{ channelId: string; channelName: string }>;
    }>(),
  {
    enumChannelOptions: () => []
  }
)
interface TenantItem {
  productIds: string[],
  tenantId: string | void,
  type: number, id: string,
  productOptions: Array<{label: string, value: string}>
}
const showDrawer = ref(false)
const formData = ref<Recordable>({})
const tenantsList = ref<TenantItem[]>([])
const ruleFormRef = ref<FormInstance>()
const tenantFormRef = ref<FormInstance[]>([])
const emit = defineEmits(['refreshTable'])
const enumTenantOptions = ref<Array<{ tenantId: string; tenantName: string }>>([])
const showTenantOptions = computed(() => {
  const list = tenantsList.value.map(i => i.tenantId)
  return enumTenantOptions.value.filter(item => !list.includes(item.tenantId))
})

const getTenantsOptions = () => {
  getTenantsForBlack().then(res => {
    enumTenantOptions.value = res
  })
}
function changeTenant (e: string, row: TenantItem) {
  getProductListByTenantId({ tenantId: e }).then(res => {
    row.productOptions = res.map(i => {
      return { label: i.productName, value: i.productId }
    })
  })
}

function addTenant () {
  tenantsList.value.push({
    id: generateUUID(),
    productIds: [],
    tenantId: undefined,
    type: 1,
    productOptions: []
  })
}
function getTenantName (tenantId: string) {
  const curTenant = enumTenantOptions.value.find(item => item.tenantId === tenantId)
  return curTenant ? curTenant.tenantName : '-'
}
function delTenant (idx: number) {
  tenantsList.value.splice(idx, 1)
}

const init = () => {
  showDrawer.value = true
  getTenantsOptions()
}
function reset () {
  formData.value = {}
  tenantsList.value = []
}
const handleClose = (done?: Fn) => {
  showDrawer.value = false
  reset()
  done?.()
}
const handleSubmit = async (done: Fn) => {
  if (!tenantsList.value.length) {
    done()
    return ElMessage.warning('请添加限制机构')
  }
  if (!ruleFormRef.value) return
  const valid = await validWrapper(ruleFormRef.value)
  const validTenant = await vlaidTenants()
  if (valid && validTenant) {
    const tenants = unref(tenantsList).map(item => {
      return {
        productIds: item?.productIds.length ? item.productIds.join(',') : '',
        tenantId: item.tenantId,
        type: item.type
      }
    })
    const { channelIds } = unref(formData)
    const params = {
      channelIds: channelIds?.length ? channelIds.join(',') : '',
      tenants
    }
    batchChannelBlackList(params).then(() => {
      handleClose()
      ElMessage.success('操作成功')
      emit('refreshTable')
    })
    done()
  } else {
    done()
  }
}

function validatorProduct (rule: any, value: any, callback: any, val: TenantItem) {
  console.log(val)
  if (val.type === 1) {
    callback()
    return
  }
  if (!val.productIds.length) {
    callback(new Error('请选择产品'))
  } else {
    callback()
  }
}

const tenantRules = ref<FormRules>({
  tenantId: [
    { required: true, message: '请选择机构', trigger: 'change' }
  ]
})

// 校验包装
function validWrapper (form: FormInstance): Promise<boolean> {
  return new Promise(resolve => {
    form.validate(valid => {
      resolve(valid)
    })
  })
}

async function vlaidTenants () {
  if (!tenantFormRef.value) return
  const validFnArr = tenantFormRef.value.map(item => validWrapper(item))
  const validArr = await Promise.all(validFnArr)
  console.log(validArr)
  return validArr.every(i => i)
}

defineExpose({
  init
})
</script>
<style lang="scss" scoped>
:global(.el-tree-node__content .el-select-dropdown__item) {
  padding: 0;
  background: none;
  &:hover {
    background: none !important;
  }
}
.title {
  color: #333333;
  font-size: 14px;
  font-weight: 700;
  position: relative;
  padding-left: 9px;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    width: 3px;
    height: 14px;
    border-radius: 3px;
    background: #5687ff;
  }
  .btn-text {
    color: #5687ff;
    font-family: 'Microsoft YaHei';
    font-size: 12px;
    line-height: 20px;
  }
}
.organ-item {
  min-height: 48px;
  border-radius: 4px;
  background-color: #F4F4F5;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 8px 16px;
  margin-bottom: 12px;
  :deep(.el-form-item) {
    margin-bottom: 0;
    &.is-error {
      margin-bottom: 14px;
    }
  }
  .del-btn {
    margin-top: 8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: $tip-color;
    cursor: pointer;
  }
  .organ-select {
    width: 424px;
    
  }
  .organ-name {
    width: 424px;
    height: 32px;
    line-height: 32px;
    color: #333;
    font-size: 14px;
  }
  .product-switch {
    display: flex;
    align-items: center;
    height: 32px;
    color: #333;
    & > span {
      font-size: 14px;
      margin-right: 8px;
    }
  }
  .product-select {
    width: 100%;
    padding-left: 40px;
    margin-top: 8px;
  }
}
</style>
