import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

// 新增参数
export const addParamsApi = (data?: any) => request.post('/admin/param/add', data, jsonHeaderConfig)

// 参数列表-分页
export const paramsPageApi = (data?: any) => request.post('/admin/param/page', data, jsonHeaderConfig)

// 新增参数
export const updateParamsApi = (data?: any) => request.post('/admin/param/update', data, jsonHeaderConfig)

// 新增参数分组
export const addParamsGroupApi = (data?: any) => request.post('/admin/paramGroup/add', data, jsonHeaderConfig)

// 查询分组列表-不分页
export const paramGroupListApi = (data?: any) => request.post('/admin/paramGroup/list', data, jsonHeaderConfig)

// 查询分组列表-分页
export const paramGroupPageApi = (data?: any) => request.post('/admin/paramGroup/page', data, jsonHeaderConfig)

// 更新分组
export const updateParamsGroupApi = (data?: any) => request.post('/admin/paramGroup/update', data, jsonHeaderConfig)
