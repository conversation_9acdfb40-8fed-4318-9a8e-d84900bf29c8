import FilterContent from './FilterBox.vue'
import { VxeGlobalRendererHandles } from 'vxe-table'
import { size } from 'lodash-es'
export default {
  showFilterFooter: false,
  renderFilter(renderOpts, params: VxeGlobalRendererHandles.RenderFilterParams) {
    return <FilterContent params={params} />
  },
  // 筛选数据方法
  filterMethod(params: any) {
    const { option, row, column } = params
    const cellValue = row[column.field]
    const { values } = option
    if (size(values)) {
      return values.includes(cellValue)
    }
    return true
  }
}
