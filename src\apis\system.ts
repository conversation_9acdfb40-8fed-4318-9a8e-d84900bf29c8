import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
import { storeRequest } from '@/utils/storeRequest'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

/** 数据字典字典类别 分页查询 */
export const dataGroupPageList = (data?: any) =>
  request.post('/admin/dataGroup/page', data, jsonHeaderConfig)

/** 数据字典字典类别 新增 */
export const dataGroupInsert = (data?: any) =>
  request.post('/admin/dataGroup/insert', data, jsonHeaderConfig)

/** 数据字典字典类别 删除 */
export const dataGroupDelete = (data?: any) =>
  request.post('/admin/dataGroup/delete/{id}', data, jsonHeaderConfig)

/** 数据字典字典类别 修改 */
export const dataGroupUpdate = (data?: any) =>
  request.post('/admin/dataGroup/update', data, jsonHeaderConfig)

/** 获取系统参数分页列表 */
export const systemSelectSysParamList = (data?: any) =>
  request.post('/admin/sysParam/selectSysParamList', data, jsonHeaderConfig)

/** 新增或者更新系统参数 */
export const systemInsertOrUpdate = (data?: any) =>
  request.post('/admin/sysParam/insertOrUpdate', data, jsonHeaderConfig)

/** 字典明细相关分页查询 */
export const dataDetailPageList = (data?: any) =>
  request.post('/admin/dataDetail/page', data, jsonHeaderConfig)

/** 字典明细相关修改 */
export const dataDetailUpdate = (data?: any) =>
  request.post('/admin/dataDetail/update', data, jsonHeaderConfig)

/** 字典明细相关新增 */
export const dataDetailInsert = (data?: any) =>
  request.post('/admin/dataDetail/insert', data, jsonHeaderConfig)

/** 数据字典明细查询 */
export function dataDetailList (data: { groupCode: string }) {
  return storeRequest({
    api: (info: any) => request.post('/admin/dataDetail/list', info, jsonHeaderConfig),
    data: data,
    cacheName: 'dataDetailList',
    storeMaxTime: 3000
  })
}

/** 数据字典明细查询 用于选择 */
export const dataDetailListFormat = (data: {
  groupCode: string,
  numberValue?: boolean
}): Promise<{ label: string; value: string }[]> => {
  return dataDetailList(data).then((res: any) => {
    return res.map((item: any) => {
      return {
        label: item.labelName,
        value: data.numberValue ? Number(item.value) : String(item.value)
      }
    })
  })
}
