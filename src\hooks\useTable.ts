/**
 * @desc：业务对象中使用表格的hooks
 * */
import { ref } from 'vue'
import { isFunc, isArray, isNullOrUndefOrEmpty } from '@/utils/is'
import { defaultPagination } from '@/enums/table'
import { getFieldColumnList, getPageComponentDataEchoAcquire } from '@/apis/customerManage'
interface BusinessData {
  tableType: Number
}
export function useTable (data:BusinessData) {
  // 分页
  const paginationConfig = ref({
    pageSize: defaultPagination.pageSize,
    pageNum: defaultPagination.currentPage,
    total: 0
  })
  // 表格数据
  const tableData = ref<any[]>([])
  // 表头数据
  const tableFieldList = ref<any[]>([])
  // 传递给表格组件的表头数据
  const echoTableFieldList = ref<any[]>([])
  // 表格列表接口函数
  const tableApi = ref<Fn>(() => {})
  // 是否已经请求了表头
  const filedVisible = ref(false)

  // 接收表格列表接口
  function setTableRequest (fn: Fn) {
    if (isFunc(fn)) {
      tableApi.value = fn
    } else {
      throw Error('fn is must be a function')
    }
  }

  // 请求表格数据
  function getTableData (data: Record<string, any>, fn:Fn) {
    const query = {
      pageNum: paginationConfig.value.pageNum,
      pageSize: paginationConfig.value.pageSize,
      ...data
    }
    tableApi.value(query).then((res: any) => {
      console.log(res)
      tableData.value = res.datas
      // 请求过表头之后，直接处理数据即可
      if (filedVisible.value) {
        echoTableData()
      }
      if (fn && isFunc(fn)) {
        fn()
      }
    })
  }

  // 请求表头字段
  function getTableFieldList () {
    getFieldColumnList({
      tableType: data.tableType
    }).then(res => {
      filedVisible.value = true
      if (isArray(res)) {
        tableFieldList.value = res
        echoTableData()
      } else {
        tableFieldList.value = []
      }
    })
  }

  // 处理表格数据和表头的回显操作
  function echoTableData () {
    if (tableFieldList.value.length) {
      // 过滤
      const arr1 = tableFieldList.value.filter(i => i.useFlag === 1)
      Promise.all(
        arr1.map(field => {
          return (async () => {
            const arr: any[] = []
            if (field.dataMark === 2) {
              tableData.value.forEach(item => {
                !isNullOrUndefOrEmpty(item[field.fieldCode]) && arr.push(item[field.fieldCode])
              })
              let dataList: any[] = []
              if (arr.length) {
                await getPageComponentDataEchoAcquire({ columnId: field.id, echoData: arr.join(',') }).then(res => {
                  dataList = res
                }).catch(() => {
                  dataList = []
                })
              }
              return { ...field, dataList }
            } else {
              return { ...field }
            }
          })()
        })).then(res => {
        echoTableFieldList.value = res
      })
    } else {
      echoTableFieldList.value = []
    }
  }

  return {
    setTableRequest,
    getTableData,
    getTableFieldList,
    tableData,
    tableFieldList,
    echoTableFieldList
  }
}