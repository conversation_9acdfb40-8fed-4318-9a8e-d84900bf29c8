<template>
  <div class="isTxt" v-if="isTxt">
    {{name || '-'}}
  </div>
  <RxkSelect
    v-else
    :multiple="true"
    :list="filterList"
    v-model="innerValue"
    :disabled="renderConfig.disabled"
    @focus="remoteMethod"
    @change="handleChange">
    <template #search>
      <div class="search-box-1">
        <RxkInput
          v-model="searchText"
          clearable
          ref="inputSearch"
          @input="handleInput"
          placeholder="搜索"
          :suffix-icon="Search"/>
      </div>
    </template>
  </RxkSelect>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, unref, watch } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkSelect } from '@/components/common/RxkSelect'
import { getPageComponentData, getPageComponentDataEchoAcquire } from '@/apis/customerManage'
import { isNullOrUndefOrEmpty } from '@/utils/is'
import { RxkInput } from '@/components/common/RxkInput'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue', 'refreshDataList', 'updadeViewData'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})

const flag = ref(true)
const list = ref([])
const name = ref('')
const searchText = ref('')
const filterList = ref([])
function handleInput (val: any) {
  console.log(list, 'l')
  if (val.trim()) {
    const arr = list.value.filter(item => item.label.indexOf(val) !== -1)
    filterList.value = JSON.parse(JSON.stringify(arr))
  } else {
    filterList.value = JSON.parse(JSON.stringify(list.value))
  }
}

onMounted(() => {
  if (props.data.dataMark !== 2) {
    list.value = props.renderConfig.dataList || []
    filterList.value = JSON.parse(JSON.stringify(list.value))
    getName(list.value)
  }
  if (props.data.dataMark === 2 && innerValue.value.length) {
    const postData = {
      columnId: props.data.id,
      echoData: innerValue.value.join(',')
    }
    getPageComponentDataEchoAcquire({ ...postData }).then(res => {
      console.log('好看些莫德凯撒000')
      console.log(res)
      list.value = res
      filterList.value = JSON.parse(JSON.stringify(list.value))
      getName(res)
      emit('refreshDataList', unref(list))
      emit('updadeViewData', unref(innerValue))
    })
  }
})

watch(() => props.modelValue, () => {
  if (props.data.dataMark !== 2 && props.isTxt) {
    getName(list.value)
  }
})

function remoteMethod () {
  console.log(props.data)
  if (unref(flag) && props.data.dataMark === 2) {
    const postData = {
      columnId: props.data.id,
      columnUseScene: props.data.columnUseScene,
      tableCode: props.data.tableCode
    }
    getPageComponentData(postData).then(res => {
      list.value = res || []
      filterList.value = JSON.parse(JSON.stringify(list.value))
      flag.value = false
      emit('refreshDataList', res)
    })
  }
}

function getName (list: Recordable[]) {
  if (isNullOrUndefOrEmpty(list) || isNullOrUndefOrEmpty(innerValue.value)) {
    name.value = '-'
    return
  }
  const current = list.filter(item => {
    return innerValue.value.includes(String(item.value))
  })
  console.log(current, 'current222')
  name.value = current.map(item => item.label).join(',')
  // console.log(name, 'name222')
}

function handleChange () {

}

</script>
<style lang="scss">
.search-box-1 {
  padding: 10px 16px;
}
</style>