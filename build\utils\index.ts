import { loadEnv } from 'vite'

/**
 * @desc: 包装环境变量
 * */
export function wrapperEnv (envConf: Record<string, any>): ViteEnv {
  const returnEnv: any = {}
  for(const envName of Object.keys(envConf)) {
    let realVal = envConf[envName].replace(/\\n/g, '\n')
    // 环境变量的字符串布尔值转换成真的布尔值
    realVal = realVal === 'true' || realVal === 'false' ? (/^true/).test(realVal) : realVal
    // 环境变量的字符串数字转换成真的数字
    if (envName === 'VITE_PORT') {
      realVal = Number(realVal)
    }
    returnEnv[envName] = realVal
  }

  return returnEnv
}

export function getAppEnvConfig (mode: string): ViteEnv {
  const envDir = process.cwd()
  const env = loadEnv(mode, envDir)
  const viteEnv = wrapperEnv(env)
  const { VITE_PORT, VITE_TITLE, VITE_DROP_CONSOLE,VITE_GLOB_APP_TITLE,VITE_SAAS_ICON } = viteEnv

  return {
    VITE_PORT,
    VITE_TITLE,
    VITE_DROP_CONSOLE,
    VITE_GLOB_APP_TITLE,
    VITE_SAAS_ICON
  }
}