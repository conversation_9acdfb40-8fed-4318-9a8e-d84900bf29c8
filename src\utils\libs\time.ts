import { isNullOrUndefOrEmpty, isNumber } from '@/utils/is'
const time: any = {
  /**
  * @description 格式化时间字符串
  * @param time - 时间 (Date 对象)
  * @param format - 时间格式 (例如 'yyyy-MM-dd HH:mm:ss')
  * @param useEndOfDay - 是否使用结束日期 23:59:59，默认为 `false`
  * @returns 格式化后的时间字符串
 */

  format (time: string | Date, format: string, useEndOfDay: boolean = false) { // 时间转换 将Thu Jun 14 2018 00:00:00 GMT+0800 (中国标准时间)转换成你所需要的格式 如：yyy-MM-dd-HH-mm-ss
    if (isNullOrUndefOrEmpty(time)) {
      return ''
    }
    if (new Date(time) === 'Invalid Date') {
      return time
    }
    const t = new Date(time)
    const tf = function (i) {
      return (i < 10 ? '0' : '') + i
    }
    return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
      switch (a) {
        case 'yyyy':
          return tf(t.getFullYear())
        case 'MM':
          return tf(t.getMonth() + 1)
        case 'mm':
          return useEndOfDay ? '59' : tf(t.getMinutes())
        case 'dd':
          return tf(t.getDate())
        case 'HH':
          return useEndOfDay ? '23' : tf(t.getHours())
        case 'ss':
          return useEndOfDay ? '59' : tf(t.getSeconds())
      }
    })
  },
  timeFormat (time: string, format: string) {
    if (isNullOrUndefOrEmpty(time)) {
      return ''
    }
    if (isNullOrUndefOrEmpty(format)) {
      format = ''
    }
    time = time.replace(/:/g, format)
    return time
  },
  timeStamp (time: string) {
    const date = new Date(time)
    const Y = date.getFullYear() + '-'
    const M =
      (date.getMonth() + 1 < 10
        ? '0' + (date.getMonth() + 1)
        : date.getMonth() + 1) + '-'
    const D = date.getDate() + ' '
    // var h = date.getHours() + ':';
    // var m = date.getMinutes() + ':';
    // var s = date.getSeconds();
    return Y + M + D
  },
  transTypeToTime (type: string, reg: RegExp, useEndOfDay: boolean = false) {
    const now = new Date()
    switch (type) {
      case '今日': // 今日
        const dayStart = new Date(new Date(now.toLocaleDateString()).getTime())
        return [this.format(dayStart, reg), this.format(now, reg, useEndOfDay)]
      case '本周': // 本周
        const day = now.getDay() || 7
        const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1 - day)
        const weekEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7 - day)
        return [this.format(weekStart, reg), this.format(weekEnd, reg, useEndOfDay)]
      case '本月': // 本月
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
        const monthEnd = new Date(now.getFullYear(), now.getMonth(), this.getMonthDays())
        return [this.format(monthStart, reg), this.format(monthEnd, reg, useEndOfDay)]
      case '上月': // 上月
        var monthStartDate: any = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        var monthEndDate: any = new Date(now.getFullYear(), now.getMonth(), 1)
        var days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24)
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth() - 1, days)
        return [this.format(lastMonthStart, reg), this.format(lastMonthEnd, reg, useEndOfDay)]
      case '全年': // 全年
        var yearStartDate = new Date(now.getFullYear(), 0, 1)
        var yearEndDate = new Date(now.getFullYear(), 11, 31)
        return [this.format(yearStartDate, reg), this.format(yearEndDate, reg, useEndOfDay)]
      case '近三天': // 近三天
        var endDay = new Date(new Date(now.toLocaleDateString()).getTime())
        var startDay = this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(new Date(new Date().toLocaleDateString()).getTime() - 3 * 24 * 60 * 60 * 1000))
        return [this.format(startDay, reg), this.format(endDay, reg, useEndOfDay)]
      case '本季度': // 本季度
        var startMonth = new Date(now.getFullYear(), this.getQuarterStartMonth(), 1)
        var endMonth = new Date(now.getFullYear(), this.getQuarterStartMonth() + 2, this.getMonthDays())
        return [this.format(startMonth, reg), this.format(endMonth, reg, useEndOfDay)]
      case '昨日':
        const lastDay = new Date(new Date(now.toLocaleDateString()).getTime() - 24 * 60 * 60)
        return [this.format(lastDay, reg), this.format(lastDay, reg, useEndOfDay)]
      case '近7天':
        var endDay = new Date(new Date(now.toLocaleDateString()).getTime())
        var startDay = this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 60 * 60 * 1000))
        return [this.format(startDay, reg), this.format(endDay, reg, useEndOfDay)]
      case '近30天':
        var endDay = new Date(new Date(now.toLocaleDateString()).getTime())
        var startDay = this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(new Date(new Date().toLocaleDateString()).getTime() - 30 * 24 * 60 * 60 * 1000))
        return [this.format(startDay, reg), this.format(endDay, reg, useEndOfDay)]
      default:
        // 灵活处理时间范围(3个月、半年、一年)
        if(isNumber(type)) {
          var endDay = new Date(new Date(now.toLocaleDateString()).getTime())
          var startDay = this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(new Date(new Date().toLocaleDateString()).getTime() - type * 24 * 60 * 60 * 1000))
          return [this.format(startDay, reg), this.format(endDay, reg, useEndOfDay)]
        }
        return ''
    }
  },
  getMonthDays () { // 获取本月天数
    const now: any = new Date()
    let nowYear = now.getYear() // 当前年
    nowYear += (nowYear < 2000) ? 1900 : 0
    const nowMonth = now.getMonth()
    const monthStartDate: any = new Date(nowYear, nowMonth, 1)
    const monthEndDate: any = new Date(nowYear, nowMonth + 1, 1)
    const days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24)
    return days
  },
  /**
   * @methods: keyGetDateTime 方法功能
   * @function: 根据type类型返回对应的日期
   * @params: 参数：
   * type 日期类型[1="今天"，2="昨天",3="本周",4="本月", 5="近3天", 6="近30天", 7="本季", 8="本年",9="前面一个月到今天（29、30或者31天）", 10="上周", 11="上月", 12="上季",13="上年", 14="近7天"]
   * fSuffix 开始日期后缀[0="没有后缀",1="00:00:00",2="23:59:59"]
   * eSuffix 结束日期后缀[0="没有后缀",1="00:00:00",2="23:59:59"]
   */
  keyGetDateTime: function (type: string, fSuffix: number, eSuffix: number) {
    const date = new Date()
    let fs = ''
    let es = ''
    switch (fSuffix) {
      case 1:
        fs = ' 00:00:00'
        break
      case 2:
        fs = ' 23:59:59'
        break
    }
    switch (eSuffix) {
      case 1:
        es = ' 00:00:00'
        break
      case 2:
        es = ' 23:59:59'
        break
    }
    switch (parseInt(type, 10)) {
      case 1:
        return {
          startTime: date.getFullYear() + '-' + (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-' + (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + fs,
          endTime: date.getFullYear() + '-' + (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-' + (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + es
        }
      case 2:
        date.setDate(date.getDate() - 1)
        return {
          startTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + fs,
          endTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + es
        }
      case 3:
        // getDate()获得当前月的天数如今天是22
        // getDay()当前周几0-6，代表周天到周六
        // 只有一种特殊情况需要处理，当getDay()为0的时候，不能再减去1
        date.setDate(date.getDate() - (date.getDay() === 0 ? 6 : (date.getDay() - 1)))
        const date3: any = { startTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + fs }
        date.setDate(date.getDate() + 6)
        date3.endTime = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + es
        return date3
      case 4:
        date.setDate(1)
        const date4: any = { startTime: date.getFullYear() + '-' + (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-' + (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + fs }
        date.setMonth(date.getMonth() + 1)
        date.setDate(date.getDate() - 1)
        date4.endTime = date.getFullYear() + '-' + (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-' + (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + es
        return date4
      case 5:
        const date5: any = { endTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + es }
        date.setDate(date.getDate() - 2)
        date5.startTime = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + fs
        return date5
      case 6:
        const date6: any = { endTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + es }
        date.setDate(date.getDate() - 29)
        date6.startTime = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + fs
        return date6
      case 7:
        const quarter7 = Math.ceil((date.getMonth() + 1) / 3)
        const list7: { [key: number]: string[] } = {
          1: ['1-1', '3-31'],
          2: ['4-1', '6-30'],
          3: ['7-1', '9-30'],
          4: ['10-1', '12-31']
        }
        return {
          startTime: date.getFullYear() + '-' + list7[quarter7][0] + fs,
          endTime: date.getFullYear() + '-' + list7[quarter7][1] + es
        }
      case 8:
        return {
          startTime: date.getFullYear() + '/1/1' + fs,
          endTime: date.getFullYear() + '/12/31' + es
        }
      case 9:
        const date9: any = { endTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + es }
        date.setMonth(date.getMonth() - 1)
        date.setDate(date.getDate() + 1)
        date9.startTime = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + fs
        return date9
      case 10:
        date.setDate(date.getDate() - date.getDay())
        const date10: any = { endTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + es }
        date.setDate(date.getDate() - 6)
        date10.startTime = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + fs
        return date10
      case 11:
        date.setDate(1)
        date.setDate(date.getDate() - 1)
        var date11: any = { endTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + es }
        date.setDate(1)
        date11.startTime = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + fs
        return date11
      case 12:
        var quarter12 = Math.floor((date.getMonth() + 1) / 3)
        var list12: any = {
          1: ['1-1', '3-31'],
          2: ['4-1', '6-30'],
          3: ['7-1', '9-30'],
          4: ['10-1', '12-31']
        }
        return {
          startTime: date.getFullYear() + '-' + list12[quarter12][0] + fs,
          endTime: date.getFullYear() + '-' + list12[quarter12][1] + es
        }
      case 13:
        const lastYear = date.getFullYear() - 1
        return {
          startTime: lastYear + '-1-1' + fs,
          endTime: lastYear + '-12-31' + es
        }
      // 近7天
      case 14:
        const date7: any = { endTime: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + es }
        date.setDate(date.getDate() - 6)
        date7.startTime = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + fs
        return date7
    }
  },
  /**
   * type:  20(近7天)，30(近30天)，60(近60天)
   * return {startTime: YYYY-MM-DD HH:SS:MM, endTime: YYYY-MM-DD HH:SS:MM }
   * */
  getDateInterval (type: number) {
    const time: any = {}
    time.endTime = this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(new Date().toLocaleDateString() + ' 23:59:59'))
    switch (type) {
      case 20:
        time.startTime = this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(new Date(new Date().toLocaleDateString()).getTime() - 7 * 24 * 60 * 60 * 1000))
        break
      case 30:
        time.startTime = this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(new Date(new Date().toLocaleDateString()).getTime() - 30 * 24 * 60 * 60 * 1000))
        break
      case 60:
        time.startTime = this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(new Date(new Date().toLocaleDateString()).getTime() - 60 * 24 * 60 * 60 * 1000))
        break
    }
    return time
  },
  dateFormat (fmt: string, date: Date) {
    let ret
    const opt: any = {
      'Y+': date.getFullYear().toString(), // 年
      'm+': (date.getMonth() + 1).toString(), // 月
      'd+': date.getDate().toString(), // 日
      'H+': date.getHours().toString(), // 时
      'M+': date.getMinutes().toString(), // 分
      'S+': date.getSeconds().toString() // 秒
      // 有其他格式化字符需求可以继续添加，必须转化成字符串
    }
    for (const k in opt) {
      ret = new RegExp('(' + k + ')').exec(fmt)
      if (ret) {
        fmt = fmt.replace(ret[1], (ret[1].length === 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, '0')))
      }
    }
    return fmt
  },
  getQuarterStartMonth () {
    let quarterStartMonth = 0
    const nowMonth = new Date().getMonth()
    if (nowMonth < 3) {
      quarterStartMonth = 0
    }
    if (nowMonth > 2 && nowMonth < 6) {
      quarterStartMonth = 3
    }
    if (nowMonth > 5 && nowMonth < 9) {
      quarterStartMonth = 6
    }
    if (nowMonth > 8) {
      quarterStartMonth = 9
    }
    return quarterStartMonth
  },
  // 将秒转换为时分秒
  transferSeconds (seconds: number, type: number) {
    // type-1,不带单位，type2-带单位时分秒
    seconds = seconds || 0
    const returnType = type || 1
    const duration = parseInt(seconds.toString())
    let minute: any = parseInt((duration / 60).toString())
    let sec: any = duration % 60
    let hourTime: any = 0
    // 如果分钟大于60，将分钟转换成小时
    if (minute > 60) {
      // 获取小时，获取分钟除以60，得到整数小时
      hourTime = parseInt((minute / 60).toString())
      // 获取小时后取佘的分，获取分钟除以60取佘的分
      minute = parseInt((minute % 60).toString())
    }
    hourTime = hourTime === 0 ? '00' : hourTime < 10 ? '0' + hourTime : hourTime
    minute = minute === 0 ? '00' : minute < 10 ? '0' + minute : minute
    sec = sec === 0 ? '00' : sec < 10 ? '0' + sec : sec
    if (returnType === 2) {
      return hourTime + '时' + minute + '分' + sec + '秒'
    } else {
      return hourTime + ':' + minute + ':' + sec
    }
  }
}
time.install = function (Vue) {
  Vue.prototype.Time = time
}

export default time
