import { computed, ref, unref, watch } from 'vue'
import { type ComputedRef } from 'vue'
import type { BasicTableProps, PaginationProps } from '../type'
import { usePageCache } from '@/hooks/usePageCache'
import { useRoute } from 'vue-router'
function utf8_to_b64 (str: string) {
  return window.btoa(encodeURIComponent(str))
}
const { getCache, updateCache } = usePageCache()
import { saveColumnWidthApi } from '@/apis/common'
import { useResource } from '@/hooks/useResource'

export function usePagination (props: ComputedRef<BasicTableProps>) {
  watch(
    () => unref(props).pagination,
    (pagination) => {
      paginationInfo.value = pagination ?? paginationInfo.value
    }
  )
  const pageNumCache = unref(props).useCache
    ? getCache()?.table?.pageNum
    : undefined
  const pageSizeCache = unref(props).useCache
    ? getCache()?.table?.pageSize
    : undefined
  const route = useRoute()
  let defaultPageSize = 20
  // 获取所有页面的页码信息
  const allPageNumbers = JSON.parse(
    localStorage.getItem('allPageNumbers') || '{}'
  )
  // 查找当前页面的页码并应用
  const savedPage = allPageNumbers[utf8_to_b64(route.fullPath)]
  if (savedPage) {
    defaultPageSize = parseInt(savedPage)
  }
  const paginationInfo = ref<PaginationProps>({
    pageSize: pageSizeCache || defaultPageSize,
    pageNum: pageNumCache || 1
  })
  const pageSizes = [10, 20, 50, 100, 200]
  const getPaginationInfo = computed((): PaginationProps => {
    return { ...unref(paginationInfo) }
  })
  // 修改分页
  async function handleSizeChange (val: number) {
    unref(props).useCache && updateCache({ table: { pageSize: val } })
    paginationInfo.value.pageSize = val
    // 标识
    const KEY = 'vPaginationKey'
    const { getMenuAppCode } = useResource()
    const APPCODE = getMenuAppCode()
    const tableCode = props.value?.tableCode || props.value?.moduleId || APPCODE
    if (props.value?.customTableConfig) {
      await saveColumnWidthApi({
        columnKey: KEY,
        value: val,
        businessKey: tableCode + KEY
      })
    }
  }
  function handleCurrentChange (val: number) {
    unref(props).useCache && updateCache({ table: { pageNum: val } })
    paginationInfo.value.pageNum = val
  }
  return {
    getPaginationInfo,
    handleSizeChange,
    handleCurrentChange,
    pageSizes
  }
}
