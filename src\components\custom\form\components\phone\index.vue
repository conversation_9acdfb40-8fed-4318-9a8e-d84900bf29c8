<template>
  <div class="isTxt" v-if="isTxt">
    {{innerValue || '-'}}
  </div>
  <div class="phone-box" v-else-if="phoneEmpty">
    <RxkInput
      style="width: 100%;"
      v-model="innerValue"
      :disabled="renderConfig.disabled"
      :placeholder="renderConfig.placeholder"
      :maxlength="11"
    />
    <RxkButton style="margin-left: 8px; color: #5687FF">空号校验</RxkButton>
  </div>
  <div class="phone-box" v-else-if="phoneCode">
    <div style="width: 100%">
      <RxkInput
        style="width: 100%;"
        v-model="innerValue"
        :disabled="renderConfig.disabled"
        :placeholder="renderConfig.placeholder"
        :maxlength="11"
      />
      <div style="display: flex;margin-top: 8px;">
        <RxkInput
          style="flex: 1"
          placeholder="短信验证码"
        />
        <RxkButton style="margin-left: 8px; color: #5687FF">发送验证码</RxkButton>
      </div>
    </div>

  </div>
  <div class="phone-box" v-else>
    <RxkInput
      style="width: 100%;"
      v-model="innerValue"
      :disabled="renderConfig.disabled"
      :placeholder="renderConfig.placeholder"
      :maxlength="11"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkInput } from '@/components/common/RxkInput'
import { operateListEnum } from '@/enums/preObjectManage'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})
const phoneEmpty = computed(() => {
  const columnOperateList = props.data.columnOperateList || []
  const current = columnOperateList.find((item: Recordable) => item.code === operateListEnum.PHONE_EMPTY && item.value)
  return !!current
})

const phoneCode = computed(() => {
  const columnOperateList = props.data.columnOperateList || []
  const current = columnOperateList.find((item: Recordable) => item.code === operateListEnum.PHONE_CODE && item.value)
  return !!current
})

</script>
<style lang="scss" scoped>
.phone-box {
  display: flex;
  width: 100%;
}
</style>