import filterRender from './components/FilterBox'


function fenToYuan(str:any) {
  // 清理非法字符（保留数字和负号）
  const cleaned = String(str).replace(/[^\d-]/g, "");
  
  // 转换为数字
  const numericValue = Number(cleaned);
  
  // 检查有效性
  if (isNaN(numericValue)) {
    throw new Error("Invalid input: must be a number or numeric string");
  }
  const number = (numericValue / 100).toFixed(2)
  // 分转元并保留两位小数
  return number.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

const millage = {
  renderDefault: (_renderOpts: Recordable, { row, column }: Recordable) => {
    const value: number = Number(row[column.field])
    if (isNaN(value)) return '-'

    return <span>{fenToYuan(value)}</span>
  }
}

export const renders = [
  { name: 'millage', options: millage },
  { name: 'filterRender', options: filterRender }
]
