import request from '@/utils/axios'
import type { ResPage } from '@/apis/interface'
import { ContentTypeEnum } from '@/enums/axios'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

// * 新增菜单
export const addMenu = (params: any) => {
  return request.post<ResPage<any>>('/admin/menu/add', params, jsonHeaderConfig)
}

// * 新增菜单
export const updateMenu = (params: any) => {
  return request.post<ResPage<any>>('/admin/menu/update', params, jsonHeaderConfig)
}

// * 展示菜单树
export const getMenuTree = () => {
  return request.get<any>('/admin/menu/tree', {})
}
