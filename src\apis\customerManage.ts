import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}
export const getFieldColumnList = (data?: any) => request.post('/yoc_lb/table_column_config/pageColumnList', data)

// 获取新增，编辑搜索，布局组件数据
export const getPageComponentData = (data?: any) => request.post('/crm/component/pageAcquire', data, jsonHeaderConfig, { ignoreCancelToken: true })

export const getPageComponentDataEchoAcquire = (data?: any) => request.post('/crm/component/echoAcquire', data, jsonHeaderConfig, { ignoreCancelToken: true })

export const getTagForEcho = (data?: any) => request.post('/crm/config/saas_customer_tag_group_config/getTagForEcho', data, jsonHeaderConfig, { ignoreCancelToken: true })
