import BaseData, { getOperate } from '@/utils/preObjectManage/common'
import type { ComponentType, OptionType } from '@/types/preObjectManage'
import { fieldTypeConfig, fieldTypeEnum, operateListEnum } from '@/enums/preObjectManage'
import type { componentOperateListType } from '@/types/preObjectManage'
import { isNullOrUndefOrEmpty } from '@/utils/is'

// 日期1
export class Dates1 extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '日期'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.date1,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        setting: data.columnInfo.setting || JSON.stringify({ web: { dateStyle: 1 } }),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        precisions: data.columnInfo.precisions || 2,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.date1
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
// 日期区间
export class Dates2 extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '日期区间'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.date2,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        setting: data.columnInfo.setting || JSON.stringify({ web: { dateStyle: 1 } }),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        precisions: data.columnInfo.precisions || 2,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.date2
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 时间
export class Dates3 extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '时间'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.date3,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        setting: data.columnInfo.setting || JSON.stringify({ web: { dateStyle: 1 } }),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        precisions: data.columnInfo.precisions || 1,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.date3
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}