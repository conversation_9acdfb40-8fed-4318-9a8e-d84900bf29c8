import type { ReqPage } from './index'
// * 用户管理模块
export namespace User {
	export interface UserSearchModel {
		realName?: string;
		username?: string;
		state?: string;
		departmentId?: number;
	}
	export interface ReqUserParams extends ReqPage {
		model?: UserSearchModel
	}
	export interface ResUserList {
		departmentVO: any;
		id: string;
		username: string;
		mobile: string;
		password: string;
		realName: string;
		state: {
			code: number;
			name: string;
			value: string;
		};
		createTime: string;
		updateTime: string;
		loginTime: string
	}
	export interface ReqAddUserParams extends ReqPage {
		departmentId: number
		departmentFlag: boolean
		mobile: number
		managerId: string
		password: string
		realName: string
		roleIdList: Array<string>
		state: string
		username: string
	}
	export interface ResStatus {
		userLabel: string;
		userValue: number;
	}
	export interface ResDepartment {
		id: string;
		name: string;
		children?: ResDepartment[];
	}
	export interface ResRole {
		id: string;
		name: string;
		children?: ResDepartment[];
	}
}