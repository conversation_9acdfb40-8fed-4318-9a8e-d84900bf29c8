<template>
  <div>
    <template v-for="column in columns" :key="column.key">
      <!-- 处理有子列的情况，使用vxe-colgroup递归渲染子列 -->
      <vxe-colgroup
        v-if="column.children && column.children.length"
        align="center"
        :field="column.key"
        :title="column.title"
      >
        <!-- 递归调用DynamicTableHeader组件处理子列 -->
        <DynamicTableHeader :columns="column.children">
          <!-- 透传所有插槽 -->
          <template v-for="slot in Object.keys($slots)" #[slot]="scope">
            <slot :name="slot" v-bind="scope || {}" />
          </template>
        </DynamicTableHeader>
      </vxe-colgroup>
      
      <!-- 处理使用cellRender自定义渲染的列 -->
      <vxe-column
        v-else-if="column.cellRender"
        v-bind="getColumnBindingValue(column)"
        :cell-render="column.cellRender"
      />
      
      <!-- 处理普通列的情况 -->
      <vxe-column
        v-else
        v-bind="getColumnBindingValue(column)"
        :showOverflow="column.showOverflow ?? true"
      >
        <!-- 自定义表头内容 -->
        <template #header>
          <!-- 使用命名插槽自定义表头 -->
          <template v-if="column.headerSlot">
            <slot :name="column.headerSlot" :column="column" />
          </template>
          <!-- 带有提示文本的表头 -->
          <template v-else-if="column.headerTooltipText">
            <div class="header-content">
              <!-- 标题文本带提示 -->
              <RxkSingeLineTooltip
                class="title-text"
                effect="dark"
                :tooltipContent="column.title"
                placement="top"
              />
              <!-- 辅助信息图标及提示 -->
              <el-tooltip
                effect="dark"
                :content="column.headerTooltipText"
                placement="top"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 14 14"
                  class="bc439ugl5__design-iconfont desc-icon"
                  width="14"
                  height="14"
                  fill="#999"
                >
                  <path
                    d="M7 0.875C8.73192 0.9205 10.1744 1.51973 11.3273 2.67269C12.4803 3.82565 13.0795 5.26808 13.125 7C13.0795 8.73192 12.4803 10.1744 11.3273 11.3273C10.1744 12.4803 8.73192 13.0795 7 13.125C5.26808 13.0795 3.82565 12.4803 2.67269 11.3273C1.51973 10.1744 0.9205 8.73192 0.875 7C0.9205 5.26808 1.51973 3.82565 2.67269 2.67269C3.82565 1.51973 5.26808 0.9205 7 0.875ZM7 3.5C6.76288 3.5 6.56687 3.58662 6.412 3.75987C6.25712 3.93313 6.18873 4.13817 6.20681 4.375L6.52138 7.875C6.53975 8.00275 6.5921 8.10527 6.67844 8.18256C6.76477 8.25985 6.87181 8.29865 6.99956 8.29894C7.12731 8.29923 7.23435 8.26044 7.32069 8.18256C7.40702 8.10469 7.45937 8.00217 7.47775 7.875L7.79231 4.375C7.81069 4.13788 7.74229 3.93283 7.58712 3.75987C7.43196 3.58692 7.23596 3.50029 6.99913 3.5H7ZM7 10.5C7.20067 10.491 7.36706 10.4226 7.49919 10.2948C7.63131 10.1671 7.69737 10.003 7.69737 9.80262C7.69737 9.60225 7.63131 9.43585 7.49919 9.30344C7.36706 9.17102 7.20067 9.10496 7 9.10525C6.79933 9.10554 6.63294 9.1716 6.50081 9.30344C6.36869 9.43527 6.30263 9.60167 6.30263 9.80262C6.30263 10.0036 6.36869 10.1676 6.50081 10.2948C6.63294 10.422 6.79933 10.4904 7 10.5Z"
                  />
                </svg>
              </el-tooltip>
            </div>
          </template>
          <!-- 默认表头显示 -->
          <template v-else>
            {{ column.title }}
          </template>
        </template>

        <!-- 单元格内容渲染 -->
        <!-- 使用自定义render函数 -->
        <template v-if="column.render" v-slot="scope">
          <TableColumnRender
            :data="scope.row"
            :column-config="column"
            :render-fn="column.render"
            :rowIndex="scope.rowIndex"
          />
        </template>
        <!-- 使用命名插槽自定义内容 -->
        <template v-else-if="column.slot" v-slot="{ row, $rowIndex }">
          <slot
            :name="column.slot"
            :row="row"
            :index="$rowIndex"
            :column="column"
          />
        </template>
        <!-- 使用同步数据处理显示内容 -->
        <template v-else-if="column.syncData" v-slot="{ row }">
          {{ dealShowVal(column.syncData.data.find((item) => String(item[column.syncData.value]) === String(row[column.key]))?.[column.syncData.label]) }}
        </template>
        <!-- 使用text函数处理显示内容 -->
        <template v-else-if="column.text" v-slot="{ row }">
          {{ dealShowVal(column.text(row)) }}
        </template>
        <!-- 默认显示方式，直接根据key获取显示值 -->
        <template v-else v-slot="scope">
          {{ dealShowVal(scope.row[column.key]) }}
        </template>
      </vxe-column>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { ColumnType } from '@/types/table'
import TableColumnRender from './TableColumnRender.vue'
import RxkSingeLineTooltip from '@/components/common/RxkSingeLineTooltip'

import { useConfig } from '@/stores/modules/config'

/** 获取配置store */
const config = useConfig()

/** 组件接收的props定义 */
defineProps<{
  /** 表格列配置数组 */
  columns: ColumnType[]
}>()

/**
 * 获取列的绑定属性值
 * @param {ColumnType} i 列配置对象
 * @returns {Recordable} 处理后的属性对象
 */
function getColumnBindingValue (i: ColumnType) {
  const { fixed, title, key, width, sortable } = i
  const data: Recordable = {
    title,
    prop: key,
    field: key,
    minWidth: 80,
    'show-header-overflow': true
  }
  // 移动端只固定right
  !config.layout.shrink && fixed && (data.fixed = fixed)
  width && (data.width = width)
  sortable && (data.sortable = sortable)
  return data
}

/**
 * 处理显示值，空值显示为'--'
 * @param {any} val 需要显示的值
 * @returns {string} 处理后的显示值
 */
const dealShowVal = (val: any): string => {
  return val ?? '--'
}
</script>

<style scoped lang="scss">
.header-content {
  display: flex;
  align-items: center;
  & > .el-tooltip__trigger {
    line-height: 1;
  }
}
.title-text {
  margin-right: 4px;
  line-height: 1;
}
.desc-icon {
  cursor: pointer;
}
</style>
