// 渠道类型
export const channelTypeOption = [{ label: 'H5渠道', value: 1 }]

// H5类型
export const h5TypeOption = [{ label: '引流版', value: 1 }, { label: '留资版', value: 2 }]

// 状态
export const statusOption = [
  { label: '下线', value: 0 },
  { label: '上线', value: 1 }
]

// 域名主体类型
export const doMainOption = [
  { label: '科融', value: '科融' }
]

// 设备类型
export const equipmentTypeOption = [
  { value: 1, label: 'Android' },
  { value: 2, label: 'IOS' },
  { value: 3, label: 'Web' },
  { value: 4, label: '其它' }
]

// 结算类型
export const settlementWayOption = [
  { label: '按CPA结算', value: 1 },
  { label: '按CPS结算', value: 2 },
  { label: '访问UV结算', value: 3 }
]

// 车辆抵押状态
export const carMortgageStatusOptions = [
  { label: '未抵押', value: 0 },
  { label: '抵押中', value: 1 },
  { label: '全款', value: 10 },
  { label: '车贷-已结清', value: 11 },
  { label: '车贷-未结清', value: 12 }
]

export type ActionEnumType = keyof typeof UseActionsEnum
// 用户行为枚举
export const UseActionsEnum = {
  110001: 'H5链接访问',
  110002: 'H5链接填写手机号',
  110003: 'H5下载弹框访问',
  110004: 'H5链接下载APP',
  110005: 'APP登录',
  110006: 'APP进入首页',
  110007: 'APP留资申请',
  110008: 'APP获取预审链接',
  110009: '预审申请结果',
  110010: '完成电核',
  110011: '完成授信',
  110012: '拿取放款结果',
  110013: 'H5发送验证码',
  110014: 'H5发送验证码验证成功',
  110015: 'APP电核通过',
  110016: 'APP电核数-失败',
  110017: 'APP授信成功',
  110018: 'APP授信-失败',
  110019: 'APP放款成功',
  110020: 'APP放款数-失败',
  110021: 'APP放款总金额',
  110022: 'H5链接登录',
  110023: 'APP预审通过',
  110024: '留资后登录',
  110025: 'APP初筛通过',
  110026: 'APP初筛拒绝',
  110027: 'APP申请预审链接',
  110028: '资金方初筛拒绝',
  110029: '资金方预审拒绝',
  110030: 'guide页访问',
  110031: 'guide页点击',
  110032: '三方请求量',
  110033: '三方请求成功量'
}

// 三方类型
export const productTypeEnum = [
  { label: '三方信贷', value: 1 },
  { label: '网贷', value: 2 },
  { label: 'api网贷', value: 3 }
]

// 结算类型
export const billingTypeEnum = [
  { label: '按CPA结算', value: 1 },
  { label: '按CPS结算', value: 2 },
  { label: '按CPA结算+按放款CPS结算', value: 3 }
]
// 结算类型
export const statusEnum = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }

]
// 推送状态
export const pushTypeEnum = [
  { label: '请求准入', value: 10 },
  { label: '准入成功', value: 11 },
  { label: '准入失败', value: 12 },
  { label: '发起产品申请', value: 20 },
  { label: '推送成功', value: 21 },
  { label: '推送失败', value: 22 },
  { label: '产品匹配成功', value: 30 },
  { label: '产品匹配成功', value: 31 }
]

/** 版本状态 */
export const versionStatusEnum = [
  {
    value: 0,
    label: '未发布'
  },
  {
    value: 1,
    label: '已发布'
  }
]

/** 是否强制更新 */
export const versionUpdateStatusEnum = [
  {
    value: 0,
    label: '否'
  },
  {
    value: 1,
    label: '是'
  }
]

/** 是否开启AB面 */
export const iosABEnum = [
  {
    value: 1,
    label: '开启'
  },
  {
    value: 0,
    label: '关闭'
  }
]

/** 账户状态 */
export const accountStatusEnum = [
  {
    value: 0,
    label: '全部'
  },
  {
    value: 1,
    label: '正常'
  },
  {
    value: 2,
    label: '禁用'
  }
]
