<template>
  <el-radio-group
    v-model="radio"
    class="rxk-radio-group"
    :class="{'verticalRadio': vertical}"
    @change="change"
    :fill="primaryColor"
  >
    <template v-if="radioButton">
      <el-radio-button
        v-for="item in list"
        :key="item.value"
        :label="item.value"
        :disabled="item.disabled"
      >
        {{item.label}}
      </el-radio-button>
    </template>
    <template v-else>
      <el-radio
        v-for="item in list"
        :key="item.value"
        :label="item.value"
        :disabled="item.disabled"
      >
        {{item.label}}
      </el-radio>
    </template>
  </el-radio-group>
</template>
<script setup lang="ts">
import type { PropType } from 'vue'
import type { List } from '@/types/common'
import { computed } from 'vue'
const primaryColor = '#5687FF'
defineOptions({
  name: 'RxkRadio'
})
const props = defineProps({
  list: {
    type: Array as PropType<List[]>,
    required: true
  },
  modelValue: {
    type: [Number, String],
    default: 0
  },
  // 开启按钮样式单选
  radioButton: {
    type: Boolean,
    default: false
  },
  vertical: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue', 'change'])
const radio = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})

function change (val: number) {
  emit('change', val)
}

</script>
<style lang="scss">
@import "./index.scss";
</style>