<template>
  <RxkDrawer
    size="700"
    title="渠道黑名单定向"
    @close="handleClose"
    @confirm="handleSubmit"
    v-model="show"
  >
    <template #content>
      <el-form
        class="tw-p-[24px]"
        :model="formData"
        ref="ruleFormRef"
        :rules="rules"
        label-position="top"
        v-loading="loading"
      >
        <div class="title">机构信息</div>
        <div class="tenant-wrapper">
          <div class="tenant-wrapper-item">
            <div class="label">机构ID</div>
            <div class="value">{{ formData.tenantId || '-' }}</div>
          </div>
          <div class="tenant-wrapper-item">
            <div class="label">机构简称</div>
            <div class="value">{{ formData.tenantShortName || '-' }}</div>
          </div>
          <div class="tenant-wrapper-item">
            <div class="label">机构名称</div>
            <div class="value">
              <el-tooltip :content="formData.tenantName" placement="top">
                <div class="fill-text">{{ formData.tenantName }}</div>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="title">定向信息</div>
        <div class="product-switch">
          <span>全部产品生效</span>
          <el-switch v-model="formData.type" :inactive-value="0" :active-value="1"  />
        </div>
        <el-form-item class="product-select" v-if="formData.type === 0 && !loading" prop="productIds">
          <RxkSelect v-model="formData.productIds"
                     multiple
                     filterable
                     :list="productOptions"
                     clearable />
        </el-form-item>
        <el-form-item style="margin-top: 22px;" label="渠道限制" prop="channelIds">
          <!-- <el-select-v2
            v-model="formData.channelIds"
            style="width: 100%"
            :props="{
              label: 'channelName',
              value: 'channelId'
            }"
            multiple
            filterable
            clearable
            :options="enumChannelOptions"
            placeholder="请选择"
          /> -->
          <SelectAllV2 v-model="formData.channelIds"
                       :list="enumChannelOptions"
                       :collapseTags="false"
                       :propsObj="{ value: 'channelId', label: 'channelName' }"/>
        </el-form-item>
        <el-form-item style="margin-top: 22px;" label="自动限制渠道" prop="channelBlockIds">
          <RxkSelect v-model="formData.channelBlockIds"
                     multiple
                     filterable
                     :list="autoLimitChannelOptions"
                     clearable/>
        </el-form-item>
      </el-form>
    </template>
  </RxkDrawer>
</template>

<script setup lang="ts">
import { RxkDrawer } from '@/components/common/RxkDrawer'
import { RxkSelect } from '@/components/common/RxkSelect'
import { ref, unref, computed, watch } from 'vue'
import {
  getChannelBlackDetail,
  getProductListByTenantId,
  updateChannelBlackDetail
} from '@/apis/channelManage'
import type { FormInstance, FormRules } from 'element-plus'
import SelectAllV2 from '@/components/business/selectAllV2/index.vue'

const props = withDefaults(
  defineProps<{
    visible: boolean,
    id: string,
    enumChannelOptions: Array<{ channelId: string; channelName: string }>;
    }>(),
  {
    visible: false,
    id: '',
    enumChannelOptions: () => []
  }
)
const emit = defineEmits(['update:visible', 'refreshTable'])
const productOptions = ref<Array<{ label: string; value: string }>>([])
const loading = ref(true)
const formData = ref<Recordable>({})
const ruleFormRef = ref<FormInstance>()
const autoLimitChannelOptions = ref<Array<{ label: string; value: string }>>([])
const rules = ref<FormRules>({
  channelIds: [
    { required: true, message: '请选择限制渠道', trigger: 'change' }
  ],
  productIds: [
    { required: true, message: '请选择限制产品', trigger: 'change' }
  ]
})

const show = computed({
  get () {
    return props.visible
  },
  set (val: boolean) {
    emit('update:visible', val)
  }
})
function init (id: string) {
  getChannelBlackDetail({ id: id }).then(res => {
    res.channelIds = res.channelIds ? res.channelIds.split(',') : []
    res.productIds = res.productIds ? res.productIds.split(',') : []
    getProductListByTenantId({ tenantId: res.tenantId }).then(productList => {
      productOptions.value = productList.map(i => {
        return { label: i.productName, value: i.productId }
      })
      if (ruleFormRef.value) {
        ruleFormRef.value.clearValidate()
      }
      loading.value = false
    })
    res.channelBlockIds = res.channelBlockIds ? res.channelBlockIds.split(',') : []
    if (res.channelBlockIds.length) {
      autoLimitChannelOptions.value = props.enumChannelOptions.filter(i => res.channelBlockIds.includes(i.channelId)).map(i => ({ label: i.channelName, value: i.channelId }))
    } else {
      autoLimitChannelOptions.value = []
    }
    formData.value = res
  })
}

function handleClose (fn?: Fn) {
  show.value = false
  fn?.()
}
function handleSubmit (fn?: Fn) {
  if (!ruleFormRef.value) return
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const { channelIds, productIds, channelBlockIds, id, type } = unref(formData)
      const params = {
        id,
        type,
        channelIds: channelIds?.length ? channelIds.join(',') : '',
        productIds: productIds?.length ? productIds.join(',') : '',
        channelBlockIds: channelBlockIds?.length ? channelBlockIds.join(',') : ''
      }
      updateChannelBlackDetail(params).then(() => {
        fn?.()
        ElMessage.success('操作成功')
        show.value = false
        emit('refreshTable')
      })
    } else {
      fn?.()
    }
  })
}

function reset () {
  loading.value = true
  formData.value = {}
}
watch(() => show.value, (val: boolean) => {
  if (val && props.id) {
    init(props.id)
  }
  if (!val) {
    reset()
  }
})
</script>

<style lang="scss" scoped>
.title {
  color: #333333;
  font-size: 14px;
  font-weight: 700;
  position: relative;
  padding-left: 9px;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    width: 3px;
    height: 14px;
    border-radius: 3px;
    background: #5687ff;
  }
}
.tenant-wrapper {
  display: flex;
  justify-content: space-between;
  margin-top: 26px;
  margin-bottom: 28px;
  .tenant-wrapper-item {
    width: 202px;
    font-size: 14px;
    .label {
      color: #999;
      margin-bottom: 14px;
    }
    .value {
      color: #333;
    }
  }
  .fill-text {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden; 
  }
}
.product-switch {
  margin-top: 16px;
  display: flex;
  align-items: center;
  height: 32px;
  color: #333;
  & > span {
    font-size: 14px;
    margin-right: 16px;
  }
}
.product-select {
  margin-top: 4px;
  margin-bottom: 0;
}

</style>
