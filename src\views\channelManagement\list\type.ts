import type { ColumnType } from '@/types/table'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
export interface Operation {
  isAdd: boolean
  id?: string | number
}

export interface AnyObj {
  [key: string]: any
}

export interface TablePro {
  searchFormData: FormSchema[]
  columns: ColumnType[]
}

export interface GroupFrom {
  channelGroupName: string
  channelIds: string
  channelTypeId: string
  id?: string;
  identification?: string;
  filedType?: number;
}
