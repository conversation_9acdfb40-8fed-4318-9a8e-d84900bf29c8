export default {
  mounted (el: any, binding: any) {
    if (el.tagName !== 'INPUT') el = el.querySelector('input')
    el.old = '' // 记录旧值
    el.handler = function () {
      const { max, digit, min, negative, isReplace } = binding.value || binding.arg || {}

      const digitReg = new RegExp(`^\\d*(\\.?\\d{0,${digit === undefined ? 2 : digit}})`, 'g')
      const is = el.value.includes('-')
      const val = el.value
      let newValue = el.value.replace(/[^\d.]/, '')
      .replace(/,/g, '')
      .replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      .replace(/^\./, '0.') // 如果输入的第一位为小数点，则替换成 0. 实现自动补全
      .match(digitReg)[0] || '' // 第五步：最终匹配得到结果 以数字开头，只有一个小数点，而且小数点后面只能有0到2位小数
      // if (newValue.match(/-/g)?.length === 2) {
      //   newValue = newValue.replace(/-/g, '')
      // }
      // 负数 并且 允许输入符号
      if (is && negative) {
        if (val.match(/-/g)?.length === 2) newValue = val.split('-').join('')
        newValue = '-' + newValue
      }
    
      if (newValue.slice(-1) === '.' && digit === 0) {
        newValue = Number(newValue)
      }
      if (max !== undefined && newValue > max) {
        newValue = isReplace ? max : String(newValue).slice(0, -1)
      } else if (min !== undefined && newValue < min) {
        newValue = isReplace ? min : String(newValue).slice(0, -1)
      } else {
        el.old = newValue
      }
      // 判断是否需要更新，避免进入死循环\
      if (newValue !== el.value) {
        el.value = newValue
        el.dispatchEvent(new Event('input')) // 通知v-model更新
      }
    }
    el.blurHander = function () {
      const { digit } = binding.value || {}
      const digitReg = new RegExp(`^\\d*(\\.?\\d{0,${digit === undefined ? 2 : digit}})`, 'g')
      if (el.value === '-') {
        el.value = ''
        el.dispatchEvent(new Event('input')) // 通知v-model更新
        return
      }
      let newValue = el.value.replace(/[^\d.]/, '')
      .replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      .replace(/^\./, '0.') // 如果输入的第一位为小数点，则替换成 0. 实现自动补全
      .match(digitReg)[0] || '' // 第五步：最终匹配得到结果 以数字开头，只有一个小数点，而且小数点后面只能有0到2位小数
      if (newValue.slice(-1) === '.' && digit !== 0 && digit !== undefined) {
        newValue = Number(newValue)
        el.value = newValue
        el.dispatchEvent(new Event('input')) // 通知v-model更新
      }
    }
    el.addEventListener('input', el.handler)
    el.addEventListener('blur', el.blurHander)
  },
  beforeUnmount (el: any) {
    el.removeEventListener('input', el.handler)
    el.removeEventListener('blur', el.blurHander)
  }
}
