<template>
  <div class="main-page">
    <router-view v-slot="{ Component }">
      <keep-alive ref="compomentRef">
        <component :is="Component"  :key="route.fullPath"/>
      </keep-alive>
    </router-view>
  </div>
</template>
<script lang="ts" setup>
import { useRoute } from 'vue-router'
import useRouteCache from '@/hooks/useRouteCache'
import { events } from '@/eventBus.js'
import { onMounted, ref, watch } from 'vue'
const route = useRoute()
const { caches } = useRouteCache()

const compomentRef = ref()
watch(
  caches,
  () => {
    console.log('[ caches23 ] >', caches.value)
  },
  {
    deep: true
  }
)
onMounted(() => {
  events.on('closeTabs', ({ fullPath, path }: any) => {
    // 通过fullPath精确删除keep-alive缓存
    const instance = compomentRef.value._
    const list = [fullPath, path]
    list.forEach((_path: any) => {
      const vnode = instance.__v_cache.get(_path)
      if (vnode) {
        vnode.shapeFlag &= ~256
        vnode.shapeFlag &= ~512
        instance.__v_cache.delete(_path)
      }
    })
  })
})
</script>
<style lang="scss">
.main-page {
  background: #fff;
  height: 100%;
}
</style>
