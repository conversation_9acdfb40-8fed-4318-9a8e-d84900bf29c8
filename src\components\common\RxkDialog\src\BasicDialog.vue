<template>
  <el-dialog class="dialog"
             v-model="show"
             :fullscreen="fullscreen"
             :width="width"
             :show-close="false"
             :append-to-body="config.layout.shrink"
             v-bind="$attrs">
    <template #header v-if="showHeader">
      <div class="title">
        <div class="title-text">{{title}}</div>
        <span @click="close" v-if="showClose" class="pointer icon iconfont icon-close"/>
      </div>
    </template>
    <slot/>
    <template #footer v-if="showFooter">
      <slot name="footer">
        <div class="footer">
          <slot name="footerLeft"/>
          <div style="flex:1">
            <RxkButton @click="close" v-if="showCancelBtn">{{ cancelBtn }}</RxkButton>
            <RxkButton :isLoading="isLoading"
                       :disabled="isDisabled"
                       type="primary"
                       @click="sure"
                       v-if="showConfirmBtn">{{ confirmBtn }}</RxkButton>
          </div>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
// @todo
import { computed, ref } from 'vue'
import { useConfig } from '@/stores/modules/config'
import { RxkButton } from '@/components/common/RxkButton/index'
const config = useConfig()
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  showFooter: {
    type: Boolean,
    default: true
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  fullscreen: {
    type: Boolean,
    default: false
  },
  width: {
    type: [String, Number],
    default: '200px'
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  isDisabled: {
    type: Boolean,
    default: false
  },
  showCancelBtn: {
    type: Boolean,
    default: true
  },
  cancelBtn: {
    type: String,
    default: '取消'
  },
  showConfirmBtn: {
    type: Boolean,
    default: true
  },
  confirmBtn: {
    type: String,
    default: '确定'
  },
  showClose: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'sure'])
const loading = ref(false)
const show = computed({
  get () {
    return props.modelValue
  },
  set (val) {
    emit('update:modelValue', val)
  }
})
function close () {
  emit('update:modelValue', false)
}
function sure (fn: Fn) {
  if (props.isLoading) {
    loading.value = true
    emit('sure', () => {
      loading.value = false
      fn?.()
    })
  } else {
    emit('sure')
  }

}
</script>
<style lang="scss">
.dialog {
  .el-dialog__header {
    padding: 0;
    margin-right: 0;
  }
  .el-dialog__footer {
    padding: 0;

  }
  .el-dialog__body {
    padding: 0;
    height: 100%;
    max-height: 600px;
    overflow-y: auto;
  }
}
</style>
<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56px;
  padding: 0 24px;
  border-bottom: 1px solid #EBEEF5;
  .title-text {
    font-size: 14px;
    font-weight: 700;
    color: #333;
  }
  span {
    font-size: 16px;
    color: #999999;
  }
}
.footer {
  height: 64px;
  line-height: 64px;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #EBEEF5;
}
</style>