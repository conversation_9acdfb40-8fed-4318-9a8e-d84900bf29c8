import type { Layout } from '@/types/config'
import { defineStore } from 'pinia'
import { reactive } from 'vue'

export const useConfig = defineStore(
  'config',
  () => {
    const layout: Layout = reactive({
      // 全局
      showDrawer: false,
      shrink: false,
      layoutMode: 'Default',
      mainAnimation: 'slide-right',
      isDark: false,

      // 侧边栏
      menuBackground: ['#ffffff', '#1d1e1f'],
      menuColor: ['#303133', '#CFD3DC'],
      menuActiveBackground: ['#ffffff', '#1d1e1f'],
      menuActiveColor: ['#409eff', '#3375b9'],
      menuTopBarBackground: ['#fcfcfc', '#1d1e1f'],
      menuWidth: 200,
      menuDefaultIcon: 'fa fa-circle-o',
      menuCollapse: false,
      menuUniqueOpened: false,
      menuShowTopBar: true,

      // 顶栏
      headerBarTabColor: ['#000000', '#CFD3DC'],
      headerBarTabActiveBackground: ['#ffffff', '#1d1e1f'],
      headerBarTabActiveColor: ['#000000', '#409EFF'],
      headerBarBackground: ['#ffffff', '#1d1e1f'],
      headerBarHoverBackground: ['#f5f5f5', '#18222c']
    })

    function menuWidth () {
      if (layout.shrink) {
        return layout.menuCollapse ? '0px' : layout.menuWidth + 'px'
      }
      // 菜单是否折叠
      return layout.menuCollapse ? '64px' : layout.menuWidth + 'px'
    }
    const setShrink = (shrink: boolean) => {
      layout.shrink = shrink
    }
    const setMenuCollapse = (value: boolean) => {
      layout.menuCollapse = value
    }
    const toggleMenuExpand = (expand = !layout.menuCollapse) => {
      layout.menuCollapse = expand
    }

    return { layout, menuWidth, setShrink, setMenuCollapse, toggleMenuExpand }
  }
)
