<template>
  <div class="isTxt" v-if="isTxt">
    {{name || '-'}}
  </div>
  <tagPopover
    v-else
    :renderConfig="renderConfig"
    @show="remoteMethod"
    :innerValue="innerValue"
    :list="dataList"
    @change="changeValue"
    ref="tagPopoverRef"
    :hidden="hidden"
  />
</template>
<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import tagPopover from './tagPopover.vue'
import { getPageComponentData, getPageComponentDataEchoAcquire, getTagForEcho } from '@/apis/customerManage'
import { isNullOrUndefOrEmpty } from '@/utils/is'
import { columnUseSceneEnums } from '@/utils/preObjectManage/utils'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue', 'refreshDataList'])

const list = ref<Recordable[]>([])
const name = ref('')

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    console.log(newVal, 'newVal77723')
    emit('update:modelValue', newVal)
  }
})
const flag = ref(true)
const dataList = ref<Recordable[]>([])
const tagPopoverRef = ref()
const hidden = ref(false)
onMounted(() => {
  // 根据使用场景判断是否需要隐藏已禁用的标签
  // columnUseScene: columnUseSceneEnums.pageSearch
  if (props.data.columnUseScene === columnUseSceneEnums.addPage ||
      props.data.columnUseScene === columnUseSceneEnums.editPage) {
    hidden.value = true
  }

  if (props.data.dataMark === 2 && innerValue.value.length) {
    const postData = {
      columnId: props.data.id,
      echoData: innerValue.value.join(',')
    }
    if (props.data.getByTagIdFlag) {
      getTagForEcho({ ...postData }).then(res => {
        if (res && res.length) {
          list.value = res.map(item => ({ id: item.value, tagName: item.label, checked: true })) || []
          getName(list.value)
          innerValue.value = list.value.map(item => item.id)
          console.log(innerValue, ' innerValu111e', list)
          tagPopoverRef.value.refreshSelectArr(list.value)
        }
      })
    } else {
      getPageComponentDataEchoAcquire({ ...postData }).then(res => {
        console.log(res)
        if (res && res.length) {
          list.value = res[0].label ? JSON.parse( res[0].label).map(item => ({ id: item.id, tagName: item.tagName, checked: true })) : []
          getName(list.value)
          innerValue.value = list.value.map(item => item.id)
          console.log(list.value, innerValue.value, '标签')
        }
      })
    }

  }
})
watch(() => props.isTxt, (val) => {
  if (!val) {
    nextTick(() => {
      tagPopoverRef.value.refreshSelectArr(list.value)
    })
  }
})
function changeValue (list: string[]) {
  innerValue.value = list
  emit('update:modelValue', list)
}

function remoteMethod () {
  console.log(flag, 'flag888')
  if (flag.value) {
    const postData = {
      tableCode: props.data.tableCode,
      columnId: props.data.id,
      columnUseScene: props.data.columnUseScene
    }
    getPageComponentData(postData).then(res => {
      flag.value = false
      console.log(res, 'res999')
      dataList.value = res.map((item: Recordable) => {
        if (item.tagList) {
          item.tagList = item.tagList.map((sub: Recordable) => {
            const i = innerValue.value.indexOf(sub.id)
            return {
              ...sub,
              checked: i !== -1
            }
          })
        } else {
          item.tagList = []
        }
        return {
          ...item
        }
      })
      console.log(dataList, 'dataList143')
      emit('refreshDataList', res)
    })
  }

}

function getName (list: Recordable[]) {
  console.log(list, 'list999')
  name.value = list.map(item => item.tagName).join(',')
}

watch(() => props.isTxt, (val) => {
  if (val) {
    getName(list.value)
  }
})
</script>