<template>
  <el-popover
    v-model:visible="visible"
    :width="400"
    trigger="click"
    @show="show"
    popper-class="custom-tag"
  >
    <template #reference>
      <div class="tag-box-input">
        <template v-if="selectedArr.length > 0">
          <ul class="tags">
            <li
              class="tag"
              v-for="i in selectedArr"
              :key="i.id"
            >{{i.tagName}}</li>
          </ul>
        </template>
        <template v-else>
          <span class="placeholder">{{renderConfig.placeholder}}</span>
        </template>
      </div>
    </template>
    <template #default>
      <div class="tag-box">
        <ul class="tag-ul">
          <li class="tag-li" v-for="item in list" :key="item.id">
            <div class="title">{{item.tagName}}</div>
            <div class="item-box">
              <div @click="handleClick(sub)"
                   class="tag "
                   :class="{'active': sub.checked}"
                   v-for="sub in item.tagList"
                   :key="sub.id">{{sub.tagName}}</div>
            </div>
          </li>
        </ul>
        <div class="btn-box">
          <RxkButton @click="handleCancel">取消</RxkButton>
          <RxkButton type="primary" @click="sure">确定</RxkButton>
        </div>
      </div>
    </template>
  </el-popover>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue'
import { ref, watch } from 'vue'
const props = defineProps({
  list: {
    type: Array as PropType<Recordable[]>,
    default: () => []
  },
  renderConfig: {
    type: Object as PropType<Recordable>,
    default: () => {}
  },
  innerValue: {
    type: Array,
    default: () => []
  }
})
const visible = ref(false)
const selectedArr = ref<Recordable[]>([])
const emit = defineEmits(['show', 'change'])
function handleClick (sub: Recordable) {
  sub.checked = !sub.checked
}
watch(() => props.innerValue, () => {
  if (!props.innerValue) {
    selectedArr.value = []
  }
})
function show () {
  emit('show')
}
function sure () {
  let list:any[] = []
  props.list?.forEach(item => {
    list.push(...item.tagList.filter((sub: Recordable) => sub.checked))
  })
  visible.value = false
  selectedArr.value = list
  emit('change', selectedArr.value.map(item => item.id))
}
function handleCancel () {
  visible.value = false
}
watch(() => visible.value, () => {
  props.list?.forEach(item => {
    item.tagList.forEach(sub => {
      const i = props.innerValue.indexOf(sub.id)
      sub.checked = i !== -1
    })
  })
})

// 刷新选中
function refreshSelectArr (arr: Recordable[]) {
  selectedArr.value = arr
}
defineExpose({
  refreshSelectArr
})

</script>
<style>
.custom-tag {
  padding: 0!important;
}
</style>
<style lang="scss" scoped>

.tag-box {
  .btn-box {
    text-align: right;
    border-top: 1px solid #EBEEF5;
    height: 54px;
    line-height: 54px;
    padding: 0 24px;
  }
  .tag-ul {
    height: 300px;
    overflow-y: auto;
    padding: 12px;
    .tag-li {
      .title {
        font-size: 12px;
        color: #333333;
        font-weight: 700;
        margin-bottom: 11px;
      }
      .item-box {
        display: flex;
        flex-wrap: wrap;
        .tag {
          padding: 0 10px;
          height: 24px;
          line-height: 24px;
          background-color: #F4F4F5;
          margin-bottom: 8px;
          margin-right: 8px;
          border-radius: 3px;
        }
        .active {
          background-color: #5687FF;
          color: #fff;
        }
      }
    }
  }
}
.tag-box-input {
  width: 100%;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  overflow: hidden;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  cursor: pointer;
  color: #333333;
  font-size: 12px;
  .icon {
    font-size: 12px;
    color: #C0C4CC;
  }
  .tags {
    width: auto;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    .tag {
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(86, 135, 255, .1);
      border-radius: 10px;
      margin-right: 5px;
      height: 20px;
      padding: 3px 8px;
    }
  }
  .placeholder {
    font-size: 12px;
    color: #cccccc;
  }
}
</style>