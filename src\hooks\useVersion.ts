/**
 * 版本管理 当新版本发布时 做提示刷新
 */
import { ref, h } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { router } from '@/router'
import request from '@/utils/axios'
import { whiteList } from '@/permission'
interface VersionOption {
  intervalTime?: number
}
export function useVersion (option:VersionOption) {
  const version = ref(localStorage.getItem('version') || '')
  const interval = ref<any>(null)
  const intervalTime = option.intervalTime || 60 * 1000

  const setTimeoutVal = ref<any>(null)

  function initVersion (){
    // 这里取请求版本号
    getVersion().then((res:any) => {
      if(version.value !== res?.versionTime) {
        if(version.value){
          ElMessage.success('已为您更新至最新版本！')
        }
      }
      version.value = res?.versionTime || ''
      localStorage.setItem('version', version.value)
    })
  }
  initVersion()

  function intervalVersion (){
    interval.value = setInterval(() => {
      computedVersion()
    }, intervalTime)
  }
  intervalVersion()
  // 请求版本号
  async function getVersion (){
    try {
      const path = window.location.protocol + '//' + window.location.host
      const { data } = await request.get(path + '/version.json', null, {
        headers: {
          'Cache-Control': 'no-cache'
        }
      },
      { isReturnNativeResponse: true, ignoreCancelToken: true })
      if(data?.versionTime) return data
      return null
    } catch(e) {
      console.log(e)
    }
  }

  // 对比版本号
  function computedVersion (){
    getVersion().then((res:any) => {
      if(version.value && res && version.value !== res?.versionTime){
        clearInterval(interval.value)
        handleUpdateVersion(res?.refresh)
      }
    })
  }

  // 版本更新
  function handleUpdateVersion (refreshType: string){
    console.log(refreshType)
    switch(refreshType) {
      case 'no':
        break
      case 'operation':
        operationVersion()
        break
      case 'forced':
        refresh()
    }
  }

  // 手动刷新
  function operationVersion (){
    let countdown = 60
    const interval = ref<any>()
    interval.value = setInterval(() => {
      countdown--
      const el = document.getElementById('changeDataContent')
      if(el){
        el.innerHTML = `系统将在${countdown}秒后自动刷新页面，如有操作请及时保存`
      }
      if(countdown === 0){
        refreshNow()
      }
    }, 1000)
    ElMessageBox.confirm(h('div', { id: 'changeDataContent' }, `系统将在${countdown}秒后自动刷新页面，如有操作请及时保存`), '发现新版本', {
      confirmButtonText: '立即刷新',
      cancelButtonText: '稍后刷新'
    }).then(() => {
      refreshNow()
    }).catch(() => {
      // 重新更新倒计时，等一分钟之后弹出弹窗提示
      clearInterval(interval.value)
      clearTimeout(setTimeoutVal.value)

      setTimeoutVal.value = setTimeout(() => {
        operationVersion()
      }, 60 * 1000)
    })
    function refreshNow (){
      clearInterval(interval.value)
      clearTimeout(setTimeoutVal.value)
      refresh()
    }
  }

  function refresh (){
    location.reload()
  }

  // 切换菜单，检查更新 强制刷新
  function hardRefreshVersion (){
    getVersion().then((res) => {
      if(version.value && version.value !== res.versionTime){
        clearInterval(interval.value)
        refresh()
      }
    })
  }

  router.beforeEach((to, from, next) => {
    if(!whiteList.includes(to.path)){
      hardRefreshVersion()
    }
    console.log('检测新版本')
    next()
  })
}
