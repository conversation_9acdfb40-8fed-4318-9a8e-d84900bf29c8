import { defineStore } from 'pinia'
import { getAuthMenuApi } from '@/apis/user'
import { ref } from 'vue'
import type { RoleMenuType } from '@/types/menu'
import { useAccountStore } from '@/stores/modules/account'
import { formatOriginMenu } from '@/router/utils'
import { cloneDeep } from 'lodash-es'

export const usePermissionStore = defineStore('usePermission', () => {
  const menuList = ref<RoleMenuType[]>([])
  const useAccount = useAccountStore()
  const currentMenu = ref<Recordable>({})

  function getAuthMenuList (): Promise<any> {
    return new Promise((resolve, reject) => {
      // 调用接口获取路由
      getAuthMenuApi().then((res) => {
        // 渲染菜单
        menuList.value = formatOriginMenu(cloneDeep(res))
        useAccount.setHasLogin(true)
        resolve({})
      }).catch((e) => {
        useAccount.logout()
        reject(e)
      })
    })
  }

  function setCurrentMenu (menu:Recordable){
    currentMenu.value = menu
  }

  return { getAuthMenuList, menuList, setCurrentMenu, currentMenu }
})

interface PermissionManageState {
  roleId: string;
  menuIndex: number;
  menuId: string;
  isSaveChange: boolean;
  isSuper: boolean;
  refreshFlag: boolean;
}

export const usePermissionManageStore = defineStore({
  id: 'PermissionManage',
  state: (): PermissionManageState => ({
    roleId: '',
    menuId: '',
    menuIndex: 0,
    isSaveChange: false,
    isSuper: false, // 超级管理员
    refreshFlag: false

  }),
  actions: {
    setRoleId (id: string) {
      this.roleId = id
    },
    setMenuIndex (index: number) {
      this.menuIndex = index
    },
    setMenuId (id: string) {
      this.menuId = id
    },
    setSaveChange (isChange: boolean) {
      this.isSaveChange = isChange
    },
    setIsSuper (role:any) {
      this.isSuper = role?.roleType?.name === 'SUPER'
    },
    setRefreshFlag (bool: any){
      this.refreshFlag = bool
    }
  },
  persist: true
})