/** 类型标识-渠道类型 */
enum EnumIdentification {
  /** 普通渠道 */
  OrdinaryList,
  /** 撞库匹配联登渠道 */
  CollisionLibrary,
  /** 信贷api半流程 */
  HalfCreditApi,
  /** 信息流渠道 */
  InformationList,
  /** 信贷api全流程 */
  FullCreditApi,
  /** 微信小程序 */
  WeChatList
}
enum EnumGroupType {
  add = '添加渠道组',
  detail = '渠道组详情',
  link = '渠道组链接',
  channelLink = '渠道链接'
}

export function useCommonEnum () {
  /**
   * 开关
   */
  const enumSwitch = [
    { label: '关闭', value: 0 },
    { label: '开启', value: 1 }
  ]
  /**
   * 状态
   */
  const enumStatus = [
    { label: '上线', value: 0 },
    { label: '下线', value: 1 }
  ]
  /**
   * 是否
   */
  const enumSwitchBoolean = [
    { label: '否', value: 0 },
    { label: '是', value: 1 }
  ]
  /**
   * 域名主体
   */
  const enumYxType = [
    { label: '科融', value: 1 }
  ]

  /**
   * 非API半流程的结算方式
   */
  const enumSettlementWay = [
    { label: '按注册数结算', value: 2 },
    { label: '按联登成功数结算', value: 3 },
    { label: '按访问UV结算', value: 4 }
  ]

  /**
   * API半流程的结算方式
   */
  const enumSettlementWayAPI = [
    { label: '按结算订单结算', value: 5 },
    { label: '按区间价格结算', value: 1 },
    { label: '按信贷收益CPS结算', value: 6 },
    { label: '按入库uv结算', value: 7 }
  ]

  /**
   * 信贷api半流程回跳方式  0:不回跳 1:静态回跳 2:动态回跳
   */
  const enumStaticCallbackType = [
    { label: '不回跳', value: 0 },
    { label: '静态回跳', value: 1 },
    { label: '动态回跳', value: 2 }
  ]
  const enumFieldType = [
    { label: '普通结算', value: 0 },
    { label: '信息流结算', value: 1 }
  ]
  /**
   * 公共的Field
   */
  const commonTypeField = {
    label: 'name',
    value: 'id'
  }

  /**
   * 渠道的Field
   */
  const commonChannelField = {
    label: 'channelName',
    value: 'id'
  }

  /**
   * 样式Field
   */
  const styleTypeField = {
    label: 'name',
    value: 'id',
    data: 'records'
  }

  /**
   * 样式Field
   */
  const userTypeField = {
    label: 'realName',
    value: 'id',
    data: 'records'
  }

  /**
   * 信贷api半流程回跳方式
   */
  const enumDispatchHalfProcessBackWay = [
    { label: '不回跳', value: 0 },
    { label: '静态回跳', value: 1 },
    { label: '动态回跳', value: 2 }
  ]

  return {
    enumSwitch,
    enumStatus,
    enumSwitchBoolean,
    enumYxType,
    commonTypeField,
    commonChannelField,
    styleTypeField,
    enumDispatchHalfProcessBackWay,
    enumSettlementWay,
    enumSettlementWayAPI,
    userTypeField,
    enumStaticCallbackType,
    EnumGroupType,
    EnumIdentification,
    enumFieldType
  }
}
