<template>
  <div class="inputGai">
    <RxkInput
      style="width: 100%;"
      v-model="innerValue[0]"
      :maxlength="renderConfig.maxlength"
      @change="handleChange"
    />
    <span class="separate">-</span>
    <RxkInput
      style="width: 100%;"
      v-model="innerValue[1]"
      :maxlength="renderConfig.maxlength"
      @change="handleChange"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { RxkInput } from '@/components/common/RxkInput'

const props = defineProps({
  ...basicProps
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) { // 监听不到数组的变化
    emit('update:modelValue', newVal)
  }
})
function handleChange () {
  emit('update:modelValue', innerValue.value)
}

</script>
<style lang="scss" scoped>
.inputGai {
  display: flex;
  align-items: center;
  .separate {
    padding: 0 4px;
  }
}
</style>