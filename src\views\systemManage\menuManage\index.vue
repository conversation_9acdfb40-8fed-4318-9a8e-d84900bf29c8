<template>
  <div class="main-user-manage">
    <el-button @click="handleAdd">增加菜单</el-button>
    <el-table  :data="tableData"
               style="width: 100%; margin-bottom: 20px"
               row-key="id"
               border
               default-expand-all>
      <el-table-column prop="name" label="菜单名称"/>
      <el-table-column prop="code" label="code" />
      <el-table-column prop="path" label="路由地址" />
      <el-table-column prop="systemCode" label="子系统编号"/>
      <el-table-column prop="action" label="操作">
        <template #default="scope">
          <el-button @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <RxkDrawer v-model="showAdd"
             title="新增菜单"
             width='700'
             @close="showAdd = false"
             @sure="submitMenu">
    <el-form>
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="formState.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="菜单code" prop="code">
        <el-input v-model="formState.code"  placeholder="请输入" />
      </el-form-item>
      <el-form-item label="父级菜单id" prop="parentId">
        <el-tree-select v-model="formState.parentId"
                        :data="tree"
                        check-strictly
                        :render-after-expand="true"
                        @currentChange="currentChange"
        />
      </el-form-item>
      <el-form-item label="路由地址" prop="path">
        <el-input v-model="formState.path"  placeholder="请输入" />
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <el-input v-model="formState.icon" placeholder="请输入" />
      </el-form-item>
    </el-form>
  </RxkDrawer>
</template>

<script setup lang="tsx">
import { reactive, ref, onMounted } from 'vue'
import { RxkDrawer } from '@/components/common/RxkDrawer'
import { addMenu, getMenuTree, updateMenu } from '@/apis/mnueManage'
import { ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'

const showAdd = ref(false)

const isEdit = ref(false)

const submitLoading = ref(false)

const tableData = ref<any[]>([])
const tree = ref<any[]>([])

function handleAdd (){
  isEdit.value = false
  showAdd.value = true
}

const formState = reactive<any>({
  name: '',
  parentId: undefined,
  path: '',
  icon: '',
  code: '',
  dataPermission: false,
  systemCode: 'BASE'
})

function currentChange (val){
  console.log(val)
  console.log(formState.parentId)
}

async function submitMenu (){
  try {
    submitLoading.value = true
    let api = addMenu
    let params = { ...formState, id: undefined }
    if(isEdit.value){
      api = updateMenu
      params.id = rowId.value
    }
    const res = await api({ ...params })
    showAdd.value = false
    ElMessage.success({ message: '操作成功！' })
    console.log(res)
  } finally {
    submitLoading.value = false
  }
}

onMounted(async () => {
  const res = await getMenuTree()
  tableData.value = formatResource(cloneDeep(res) )
  console.log(tableData.value)
  tree.value = formatTree(cloneDeep(res))
})

function formatTree (trees:any[]){
  return trees.map(el => {
    const tree = { ...el, value: el.id, label: el.name }
    if(tree?.children?.length){
      tree.children = formatTree(tree.children)
    }
    return tree
  })
}

function formatResource (trees:any[], type: string = 'menu'){
  return trees.map(el => {
    const tree = { ...el, type }
    if(tree?.children?.length){
      tree.children = formatResource(tree.children)
    } else if(tree?.resourceVOList?.length){
      tree.children = formatResource(tree.resourceVOList, 'resource')
    }
    return tree
  })
}

const rowId = ref('')

function handleEdit (row:any) {
  for(let key in formState){
    formState[key] = row[key] || undefined
  }
  rowId.value = row.id
  isEdit.value = true
  showAdd.value = true
}

</script>

<style scoped lang="scss">
.main-user-manage {
  width: 100%;
  height: 100%;
  padding: 20px 16px 8px 16px;
}
</style>
