<template>
  <div class="top-nav">
    <div class="action-box turn-left">
      <CaretLeft class="action-icon" @click="navTurnLeft" />
    </div>
    <div class="center-tabs" ref="centerRef">
      <div class="scroll-tab-box" ref="scrollRef">
        <div
          class="default-tag"
          :class="{ 'active-tag': activeKey === item.path }"
          v-for="item in history"
          :key="item.path"
          link
          :closable="!item.closeAble"
          @click="clickTag(item.path)"
          @mouseover="config.layout.shrink? null : handleHover(item)"
          @mouseleave="hoverTag = null"
        >
          <div class="tag-item" v-if="item.name">
            {{ item.name }}
            <el-icon
              style="margin-left: 6px; width: 12px"
              @click="(e: Event) => closeTag(e, item)"
              v-if="(activeKey === item.path || hoverTag === item.path) && item.path !== '/' && history.length > 1"
            >
              <Close />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
    <div class="action-box turn-right">
      <CaretRight class="action-icon" @click="navTurnRight" />
    </div>
    <div class="action-box drop-action">
      <el-dropdown
        trigger="click"
        size="small"
        popper-class="top-nav-drop-over-box"
        @command="handleClose"
      >
        <CaretBottom class="action-icon" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="closeOther">关闭其他</el-dropdown-item>
            <el-dropdown-item command="closeAll">关闭所有</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, computed, nextTick, unref, onMounted } from 'vue'
import {
  CaretLeft,
  CaretRight,
  CaretBottom,
  Close
} from '@element-plus/icons-vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { useNavHistory } from '@/stores/modules/routerNav'
import { events } from '@/eventBus'
import { ElMessageBox } from 'element-plus'
import { useConfig } from '@/stores/modules/config'
//  已点击导航列表全部管理在store中
const config = useConfig()
const navHistory = useNavHistory()
const router = useRouter()
const scrollRef = ref(null)
const centerRef = ref(null)
let history = computed(() => navHistory.history)
let activeKey = computed(() => navHistory.activeKey)
const hoverTag = ref('')
//  操控滚动条
const controlScroll = (direction: number) => {
  const scrollDom = unref(centerRef) as unknown as Element
  // 获取当前的滚动位置
  const currentScrollLeft = scrollDom.scrollLeft

  // 在原有基础上增加100px
  scrollDom.scrollTo({
    left: currentScrollLeft + direction * 160,
    behavior: 'smooth'
  })
}
// 重置滚动条
const resetScroll = () => {
  showActiveItem()
}

const navTurnLeft = () => {
  controlScroll(-1)
}

const navTurnRight = () => {
 
  controlScroll(1)
}

const clickTag = (path: string) => {
  navHistory.setActive(path)
}
const closeTag = (e: Event, item: Recordable) => {
  e.stopPropagation()
  navHistory.closeTag(item.path)
  events.emit('closeTabs', item)
}

const handleHover = (item:Recordable) => {
  hoverTag.value = item.path
}

const handleClose = (code: string) => {
  ElMessageBox({
    title: '关闭标签',
    message: '关闭列表页签后，将无法正常使用上一个下一个快速切换详情页，确认要关闭吗？',
    showCancelButton: true
  }).then(() => {
    switch (code) {
      case 'closeOther':
        navHistory.closeElse()
        break
      case 'closeAll':
        navHistory.closeAll()
        break
      default:
        break
    }
    resetScroll()
  })
}

//  确保激活项一直在可视窗内
const showActiveItem = () => {
  const scrollDom = unref(scrollRef) as unknown as Element
  const activeElement = scrollDom.querySelector('.active-tag')

  activeElement?.scrollIntoView({ behavior: 'smooth' })

}

onMounted(() => {
  showActiveItem()
})

watch(activeKey, (val) => {
  router.push(val)
  nextTick(() => {
    showActiveItem()
  })
})

onBeforeRouteUpdate((to) => {
  navHistory.addHistory({
    name: to.meta.title as string,
    path: to.fullPath,
    closeAble: false
  })
})
</script>
<style scoped lang="scss">
.top-nav {
  width: 100%;
  display: flex;
  align-items: center;
  background-color: #f4f4f5;
  padding: 8px 16px 8px 8px;

  .action-box {
    width: 24px;
    height: 24px;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .action-icon {
      color: #666666;
      width: 18px;
      height: 18px;
      outline: none;
    }
  }

  .turn-left {
    &:active {
      .action-icon {
        color: #5687ff;
      }
    }
  }

  .turn-right {
    &:active {
      .action-icon {
        color: #5687ff;
      }
    }
  }

  .center-tabs {
    flex: 1;
    overflow: hidden;
    .scroll-tab-box {
      display: flex;
      white-space: nowrap;
      align-items: center;
      transition: all ease-in 0.1s;
      position: relative;
      left: 0;
      top: 0;

      .default-tag {
        border: 0;
        color: #666666;
        border-radius: 2px;
        font-weight: 400;
        cursor: pointer;
        font-size: 12px;
        .tag-item {
          display: flex;
          align-items: center;
          padding: 6px 10px;
        }
      }

      .active-tag {
        font-weight: 700;
        color: #5687ff;
        background-color: #fff;
      }
    }
  }

  .drop-action {
    background-color: #fff;
    border-radius: 4px;
  }
}
</style>
<style lang="scss">
.top-nav-drop-over-box {
  padding: 6px !important;

  .el-dropdown-menu__item {
    line-height: 30px;
  }

  .el-popper__arrow {
    display: none;
  }
}

.top-nav {
  .default-tag {
    margin: 0 2px;
    .el-tag__close {
      color: #999999;

      &:hover {
        background-color: unset;
      }
    }
    &:hover {
      background-color: rgba(#5687ff, .1);
    }
  }

  .active-tag {
    .el-tag__close {
      color: #5687ff;
    }
  }
}
</style>
