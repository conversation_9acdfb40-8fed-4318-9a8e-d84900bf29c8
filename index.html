<!DOCTYPE html>
<html lang="zh-Cn">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%- title %></title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      // ios 10 禁止缩放
      window.onload = function () {
        document.documentElement.addEventListener(
          'touchstart',
          function (event) {
            if (event.touches.length > 1) {
              event.preventDefault()
            }
          },
          false
        )
        var lastTouchEnd = 0
        document.addEventListener(
          'touchend',
          function (event) {
            var now = Date.now()
            if (now - lastTouchEnd <= 300) {
              event.preventDefault()
            }
            lastTouchEnd = now
          },
          false
        )
        // 阻止双指放大
        document.addEventListener('gesturestart', function (event) {
          event.preventDefault()
        })
      }
      // 软键盘弹起挡住原来的视图
    window.addEventListener('resize', function() {
      if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
        window.setTimeout(function() {
          if ('scrollIntoView' in document.activeElement) {
            document.activeElement.scrollIntoView(false)
          } else {
            document.activeElement.scrollIntoViewIfNeeded(false)
          }
        }, 0)
      }
    })
    </script>
  </body>
</html>
