<template>
  <MemberSingleSelection 
    :paramsData="commonParams"
    :api="getPageComponentData"
    :showValueData="showValueData"
    :disabled="isTxt || renderConfig.disabled"
    @sure="getUser"
    @getUserList="getUserList"
    style="width: 100%"/>
</template>
  
<script lang="ts" setup>
import { useNavHistory } from '@/stores/modules/routerNav'
import { computed, ref } from 'vue'
import { basicProps } from '@/components/custom/form/components/props'
import { getPageComponentData } from '@/apis/customerManage'
import { isArray, isNullOrUndefOrEmpty } from '@/utils/is'

const emit = defineEmits(['update:modelValue', 'refreshDataList', 'updadeViewData'])
const props = defineProps({
  ...basicProps
})
const load = ref<boolean>(true)
const innerValue = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal)
  }
})

const showValueData = computed(() => {

  let value: Recordable[] = []
  if(innerValue.value) {
    if(!isArray(innerValue.value)) {
      value = [{
        id: innerValue.value
      }]
    } else {
      value = innerValue?.value?.map(item => ({ id: item }))
    }
  }
  return value
})
const navHistory = useNavHistory()
console.log(navHistory, 'navHistory')
const systemCode = navHistory.activeSysCode
  
const commonParams = {
  columnId: props.data.id,
  columnUseScene: props.data.columnUseScene,
  tableCode: props.data.tableCode,
  extraAgreements: {
    systemCode: systemCode,
    containChildren: 1,
    useSceneType: props.data.useSceneType,
    disableUserIds: [innerValue.value]
  },
  tenantId: props.data.tenantId || 0
}

function getUser (data:Recordable){
  console.log(data, '确定啦')
  emit('refreshDataList', data.userInfoList)
  innerValue.value = data.userInfoList.map((item: Recordable) => item.id) || []

}

function getUserList (data: Recordable) {
  if(!isNullOrUndefOrEmpty(data)) {
    emit('refreshDataList', data)
    emit('updadeViewData', innerValue.value)
  }
}

</script>
  
  <style lang="scss" scoped>
  </style>