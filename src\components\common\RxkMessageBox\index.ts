import type { App<PERSON>ontext, RendererElement, RendererNode, VNode } from 'vue';
import 'element-plus/es/components/message-box/style/css'
import './src/index.scss'

import { type ElMessageBoxOptions, ElMessageBox } from 'element-plus';

export function rxkMessageBox(
  message: string | VNode<RendererNode, RendererElement, {
    [key: string]: any;
  }> | (() => VNode<RendererNode, RendererElement, {
    [key: string]: any;
  }>) | undefined,
  title: string,
  options?: ElMessageBoxOptions,
  appContext?: AppContext
) {
  return new Promise<void>((resolve, reject) => {
    ElMessageBox.confirm(message, title, {
      ...options,
      customClass: 'rxkMessageBox'
    }, appContext).then(() => {
      resolve()
    }).catch(() => {
      reject()
    });
  })
}

