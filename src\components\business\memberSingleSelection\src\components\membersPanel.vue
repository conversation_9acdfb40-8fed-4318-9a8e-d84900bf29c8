<template>
  <div class="members">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索人员"
        clearable
        :prefix-icon="Search"/>
      <div 
        class="memberList"
      >
        <el-radio-group 
          v-model="checkUserId">
          <div 
            class="childNodeItem"
            v-for="(item, index) in memberList"
            
            :key="index">
            <el-radio 
              :label="item.id"
              :disabled="item.jobStatus && item.jobStatus.code === 2"
              @change="selectChange(item)">{{ item.realName }}<template v-if="item.jobStatus && item?.jobStatus.code === 2">（离职）</template></el-radio>
          </div>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, unref, watch, type PropType } from 'vue'
import { Search } from '@element-plus/icons-vue'
import type { UserVOList } from '../type'
import { cloneDeep } from '@/utils/tools'

const emit = defineEmits(['update:data', 'updateValue'])
const personnelSelectionInput = ref<string>('')
const checkUserId = ref<string>('')
const memberList = ref<UserVOList[]>([])

const props = defineProps({
  data: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  },
  checkedUser: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  }
})

const orginRoleData = computed(() => {
  return unref(props.data)
})

watch(() => props.data, (newValue) => {
  memberList.value = cloneDeep(newValue)
})

watch(() => props.checkedUser, (checkedUser) => {
  console.log(checkedUser, 'checkedUsercheckedUser')
  checkUserId.value = checkedUser?.[0]?.id
}, {
  immediate: true
})

function selectChange (item: UserVOList) {
  emit('updateValue', [item])
}

watch(personnelSelectionInput, (val) => {
  memberList.value = orginRoleData.value?.filter(item => item?.realName && item?.realName.includes(val))
})

</script>

<style lang="scss" scoped>
.members {
  @include flex-center(row, normal, normal);
  height: 100%;
  .left {
    flex: 1;
    padding-top: 16px;
    .memberList {
      overflow-y: auto;
      height: calc(100% - 50px);
      .childNodeItem {
        display: block;
        width: 100%;
      }
    }
    .personnelSelectionInput {
      border-radius: 4px;
      width: 268px;
      :deep(.el-input__wrapper) {
        box-shadow: none;
        background: #F4F4F5;
      }
      margin-bottom: 13px;
    }
  }
  
}
</style>