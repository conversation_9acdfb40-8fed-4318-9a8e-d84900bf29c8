<template>
  <div class="user-list">
    <SearchFilter @register="registerSetting" @search="search" />
    <div class="table-row">
      <RxkVTable @register="registerTable" />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { RxkButton } from '@/components/common/RxkButton'
import { RxkVTable } from '@/components/common/RxkVTable'
import { SearchFilter } from '@/components/business/searchFilter'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { ref, unref } from 'vue'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { getUserListNoAuth } from '@/apis/user'
import { usePermissionManageStore } from '@/stores/modules/permission'
import { ElMessage, ElMessageBox } from 'element-plus'
import { removeRoleUserApi } from '@/apis/permissionManage'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import type { ColumnType } from '@/types/table'

const permissionManageStore = usePermissionManageStore()

const searchInfo: { model: Recordable } = {
  model: {
    realName: undefined,
    mobile: undefined,
    roleId: permissionManageStore.roleId
  }
}
const searchFormData = ref<FormSchema[]>([
  {
    key: 'realName',
    component: 'Input',
    val: '',
    fieldName: '用户姓名'
  },
  {
    key: 'mobile',
    component: 'Input',
    fieldName: '联系电话',
    val: ''
  }
])
const [registerSetting] = useSearch({
  schemas: unref(searchFormData)
})

const search = (data: { [k: string]: any }) => {
  searchInfo.model.realName = data.realName || undefined
  searchInfo.model.mobile = data.mobile || undefined
  searchInfo.model.roleId = permissionManageStore.roleId || undefined
  setSearchInfo(searchInfo)
  reload()
}

const removeRoleUser = (data) => {
  ElMessageBox.confirm(`确定移除用户 - ${data.realName}吗？`, '警告', {
    type: 'error',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        try {
          await removeRoleUserApi(data.id, permissionManageStore.roleId)
          await reload()
          ElMessage.success('移除成功')
          done()
        } catch {
          ElMessage.error('移除失败')
        } finally {
          instance.confirmButtonLoading = false
        }
      } else {
        done()
      }
    }
  })
}

const columns = ref<ColumnType[]>([
  { key: 'id', title: '用户ID', width: 180 },
  { key: 'realName', title: '用户姓名' },
  { key: 'mobile', title: '联系电话' },
  { key: 'departmentVO', title: '所属部门', render: ({ cellData }) => cellData?.departmentVO?.name },
  { key: 'createTime', title: '添加时间', width: 180 },
  {
    key: 'operate',
    title: '操作',
    width: 100,
    render: ({ cellData }) => {
      return (
        <>
          {permissionManageStore.isSuper ? '' : <RxkButton text onClick={() => removeRoleUser(cellData)}>移除</RxkButton>}
        </>
      )
    }
  }
])
const getBasicColumns = () => unref(columns)
const [registerTable, { reload, setSearchInfo }] = useTable({
  api: getUserListNoAuth,
  columns: getBasicColumns(),
  searchInfo: searchInfo,
  immediate: true // 是否立刻请求
})

const handleSelectRole = (id: string) => {
  searchInfo.model.realName = ''
  searchInfo.model.mobile = ''
  searchInfo.model.roleId = id
  setSearchInfo(searchInfo)
  reload()
}

defineExpose({
  handleSelectRole
})
</script>

<style scoped lang="scss">
.user-list {
  font-size: 14px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .el-form--inline .el-form-item {
    margin-right: 40px;
  }
}
.table-row{
  flex: 1;
  overflow-y: hidden;
}
</style>
