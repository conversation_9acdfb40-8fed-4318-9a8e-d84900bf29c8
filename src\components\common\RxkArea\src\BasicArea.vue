<template>
  <el-cascader
    class="rxk_area"
    v-bind="getBindValue"
    :props="regionData"
    :options="province"
    v-model="input"
    @change="handleChange"
  />
  <RxkInput  type="textarea"
             placeholder="请输入详细地址"
             v-if="getBindValue.showArea"
             v-model.trim="areaInputValue"/>
</template>
<script lang="ts" setup>
import { RxkInput } from '@/components/common/RxkInput'
import { computed } from 'vue'
import province from 'province-city-china/dist/level.json'

defineOptions({
  name: 'RxkArea'
})

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请输入'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showArea: {
    type: Boolean,
    default: true
  },
  areaValue: {
    type: String,
    default: ''
  },
  regionData: {
    type: Object,
    default: () => ({
      value: 'code',
      label: 'name'
    })
  }

})
const getBindValue = computed(() => (props))
const emit = defineEmits(['update:modelValue', 'update:areaValue', 'change'])
const handleChange = (v) => {
  emit('change', v)
}
const input = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    console.log(value, 222222)
    
    emit('update:modelValue', value)
  }
})
const areaInputValue = computed({
  get () {
    return props.areaValue
  },
  set (value) {
    emit('update:areaValue', value)
  }
})

</script>
<style lang="scss">
@import "./index.scss";
</style>