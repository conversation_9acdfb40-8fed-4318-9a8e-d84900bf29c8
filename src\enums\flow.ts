// actions
export const actions = [
  {
    label: '通过',
    value: 'PASS',
    disabled: true
  },
  {
    label: '拒绝',
    value: 'REJECT',
    disabled: true
  },
  {
    label: '回退',
    value: 'ROLLBACK'
  },
  {
    label: '转交',
    value: 'TRANSFER'
  },
  {
    label: '加签',
    value: 'ADD_SIGN'
  },
  {
    label: '结束流程',
    value: 'CLOSE'
  }
]
// approvalRule
export const approvalRule = [
  {
    label: '任一负责人提交进入下一节点',
    value: 'ANY'
  },
  {
    label: '所有负责人提交后进入下一节点',
    value: 'ALL'
  }
]
export const missingApproverRule = [
  {
    label: '指定负责人',
    value: 'CANDIDATE'
  },
  {
    label: '自动跳转下一负责人',
    value: 'NEXT'
  },
  {
    label: '停止流转并报错',
    value: 'ERROR'
  }
]
export const timeoutConfig = [
  {
    label: '不限时',
    value: 'not'
  },
  {
    label: '12小时',
    value: '12'
  },
  {
    label: '24小时',
    value: '24'
  },
  {
    label: '48小时',
    value: '48'
  },
  {
    label: '自定义',
    value: 'customer'
  }
]
export const notifyConfig = [
  {
    label: '使用系统默认通知',
    value: 'SYSTEM'
  }
  // {
  //   label: '自定义通知',
  //   value: 'CUSTOM'
  // }
]

export const triggerRules = [
  { label: '总是触发', value: 'ALWAYS' },
  { label: '满足条件触发', value: 'CONDITION' }
]

export const withdrawRules = [
  { label: '随时可撤回', value: 'ANYTIME' },
  { label: '禁止撤回', value: 'FORBID' },
  { label: '审批前允许撤回', value: 'BEFORE_APPROVAL' }
]

export const operatorModes = [
  { label: '操作人已明确', value: 'NORMAL' },
  { label: '操作人需指派', value: 'ASSIGN' }
]

export enum NodeType {
  APPROVAL = 'APPROVAL',
  BUSINESS = 'BUSINESS',
  PEER = 'PEER',
  STRUCT = 'STRUCT',
  DIALOG = 'DIALOG',
  START = 'START',
  CHILD = 'SUB'
}
// CREATE,PASS,REJECT,ROLLBACK,TRANSFER,ADD_SIGN,CANCEL,CLOSE
export enum ApproveStatus {
  start = 'CREATE', // 发起流程
  reject = 'REJECT', // 审批拒绝
  resolve = 'PASS', // 审批通过
  pending = 'APPROVE_PENDING', //  等待审批
  revoke = 'CANCEL', //  撤回
  rollback = 'ROLLBACK', // 回退
  addSign = 'ADD_SIGN', // 加签
  transfer = 'TRANSFER', // 转交
  close = 'CLOSE' // 关闭
}

export const StatusOptions = {
  [ApproveStatus.start]: {
    color: '#5687FF',
    name: '发起流程',
    type: ''
  },
  [ApproveStatus.reject]: {
    icon: 'icon-circle-close',
    color: '#F44D4D',
    name: '拒绝',
    type: 'danger'
  },
  [ApproveStatus.resolve]: {
    icon: 'icon-qiyong',
    color: '#5DC144',
    name: '通过',
    type: 'success'
  },
  [ApproveStatus.pending]: {},
  [ApproveStatus.revoke]: {
    icon: 'icon-chehui',
    color: '#999999',
    type: 'info',
    name: '已撤回'
  },
  [ApproveStatus.rollback]: {
    color: '#FCAC33',
    text: '退',
    name: '回退',
    type: 'warning'
  },
  [ApproveStatus.addSign]: {
    color: '#5687ff',
    text: '加',
    name: '加签',
    type: ''
  },
  [ApproveStatus.transfer]: {
    color: '#5687ff',
    text: '转',
    name: '转交',
    type: ''
  },
  [ApproveStatus.close]: {
    icon: 'icon-circle-close',
    color: '#999999',
    name: '关闭',
    type: 'info'
  }
}
//  PROCESSING :处理中 PASS :完成 REJECT :驳回 CANCEL :取消
export enum ApproveStatusName {
  PROCESSING = '处理中',
  PASS = '通过',
  REJECT = '驳回',
  CANCEL = '取消'
}

export const notDelCell = ['STRUCT']

export const approvalTypes = [
  { label: '普通审批', value: 'NORMAL' },
  { label: '逐级审批', value: 'LEVEL' }
]

export const approvalRangeLevels = [
  { label: '直接部门主管', value: '1' },
  { label: '上级部门主管', value: '2' },
  { label: '3级部门主管', value: '3' },
  { label: '4级部门主管', value: '4' }
]

export const autoApprovalRules = [
  { label: '负责人与上一审批人一致时', value: 'PRE_NODE' },
  { label: '负责人处理过', value: 'APPROVED' },
  { label: '不启用', value: 'DISABLE' }
]

export const triggerActions = [
  { label: '数据新增', value: 'NEW' },
  // { label: '删除数据', value: 'DELETE' },
  { label: '修改数据', value: 'EDIT' }
]

export const conditionMap: any = {
  EQUAL: 'equal',
  NOT_EQUAL: 'unequal',
  CONTAINS: 'contain',
  NOT_CONTAINS: 'exclusive',
  GT: 'gt',
  GTE: 'gte',
  LT: 'lt',
  LTE: 'lte',
  NON_NULL: 'notnull',
  IS_NULL: 'isnull'
}

/**
 * "PASS": "通过","REJECT": "拒绝","ROLLBACK": "回滚","TRANSFER": "转交","ADD_SIGN": "加签","CLOSE": "结束流程", "CANCEL"：“撤回流程”，"CREATE": "重新发起"
 */
export enum AuthBtn {
  PASS = 'PASS',
  REJECT = 'REJECT',
  ROLLBACK = 'ROLLBACK',
  TRANSFER = 'TRANSFER',
  ADD_SIGN = 'ADD_SIGN',
  CANCEL = 'CANCEL',
  CLOSE = 'CLOSE',
  CREATE = 'RESUBMIT'
}

// 审批人 执行人 操作人范围选择
export enum FlowOperatorType {
  USER = 'USER',
  ROLE = 'ROLE',
  DEPARTMENT = 'DEPARTMENT',
  DYNAMIC = 'DYNAMIC'
}

export const FlowOperatorToOrz: Recordable = {
  USER: 'USER',
  ROLE: 'ROLE',
  DEPARTMENT: 'DEP',
  DYNAMIC: 'DYNAMIC',
  DEP: 'DEPARTMENT'
}
