<template>
  <div class="channel-statistics-page">
    <SearchFilter @register="registerSetting" @search="search"/>
    <div class="btn-box">
      <RxkButton @click="handleDown">导出</RxkButton>
    </div>

    <div class="table-box">
      <RxkVTable @register="registerTable"
                 :defalutLoading="true" 
                 class="custorm-hasTable-footer"
                 ref="dataTableRef"
                 show-footer
                 showHeaderSummary
                 :footer-method="footerMethod">
        <template #operateSlot="{row}">
          <RxkButton
            class="table-action-btn"
            @click="openDialog(row.channelId)"
            text
          >单日明细</RxkButton>
        </template>
      </RxkVTable>
    </div>

    <!-- 单日明细 -->
    <DayDetails 
      v-if="state.dayDetailsVisible"
      v-model="state.dayDetailsVisible"
      :channelId="state.channelId"
    />

  </div>
</template>

<script setup lang="tsx">
import { unref, reactive, onMounted, ref } from 'vue'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { channelStatistics } from '../data'
import DayDetails from './components/dayDetails.vue'
import { excelExport } from '@/utils/index'
import { fetchChannelStatisticsListExport, fetchGetChannelStatisList, fetchGetDataStatisticsTotal } from '@/apis/carlife'
import timeUtils from '@/utils/libs/time'
import type { ColumnType } from '@/types/table'
import { settlementWayOption } from '@/enums/carlife'

const sumTableData = ref<Recordable>({})

const dataTableRef = ref()
onMounted(() => {
  const [transStartTime, transEndTime] = timeUtils.transTypeToTime('今日', 'yyyy-MM-dd')
  searchInfo.model.dayStart = transStartTime
  searchInfo.model.dayEnd = transEndTime

  setSearchInfo(searchInfo)
  getSummaryData()
  reload()
})

const state = reactive({
  channelId: '',
  dayDetailsVisible: false
})

// 搜索表单数据
const [registerSetting] = useSearch({
  schemas: unref(channelStatistics.searchFormData)
})
const searchInfo:Record<string, any> = {
  model: {
    dayStart: '',
    dayEnd: '',
    isChannelGroup: true,
    isDayGroup: false
  }
}

const [registerTable, { reload, setSearchInfo, getSearchInfo, getPaginationData }] = useTable({
  api: fetchGetChannelStatisList,
  columns: unref(channelStatistics.columns),
  searchInfo: searchInfo,
  immediate: false // 是否立刻请求
})

const search = (val:{ [k: string]: any }) => {
  Object.assign(searchInfo.model, val)

  searchInfo.model.dayStart = val.timeRange?.[0] ?? ''
  searchInfo.model.dayEnd = val.timeRange?.[1] ?? ''
  delete searchInfo.model.timeRange

  setSearchInfo(searchInfo)
  getSummaryData()
  reload()
}
// 打开弹窗
const openDialog = (channelId: string) => {
  state.channelId = channelId
  state.dayDetailsVisible = true
}

const handleDown = () => {
  const params = { ...getSearchInfo(), ...getPaginationData() }
  excelExport(fetchChannelStatisticsListExport, params, 'POST')
}
function getSummaryData () {
  fetchGetDataStatisticsTotal(searchInfo).then((res) => {
    sumTableData.value = res
    unref(unref(dataTableRef).tableRef)?.updateFooter?.()
  })
}

//  总计
function footerMethod ({ columns }: { columns: ColumnType[] }) {
  return [
    columns.map((column, columnIndex) => {
      if (columnIndex === 0) {
        return '总计'
      }

      if(column.field === 'settleType'){
        return settlementWayOption.find((item) => item.value === sumTableData.value.settleType)?.label
      }

      const arr = column.field?.split(',') || []
      return arr.map((item) => {
        return unref(sumTableData)[item] ?? ''
      })?.filter(i => i !== '').join(' | ')
    })
  ]
}

</script>

<style lang="scss" scoped>
.channel-statistics-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-form--inline .el-form-item {
    margin-right: 40px;
  }
  .btn-box {
    padding: 0 16px 4px;
    display: flex;
    justify-content: flex-end;
  }
  .table-box {
    width: 100%;
    flex:1;
    overflow-y: hidden;
  }
}

.sort-btn {
  &:hover {
    color: $primary-color;
  }
  cursor: pointer;
  margin-left: 8px;
}
</style>
