{"include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/**/*.ts", "types/**/*", "build/**/*", "vite.config.ts", "tailwind.config.js", "postcss.config.js", "auto-imports.d.ts", "components.d.ts"], "exclude": ["src/**/__tests__/*", "node_modules", "dist"], "compilerOptions": {"composite": true, "baseUrl": ".", "strictPropertyInitialization": false, "paths": {"@/*": ["src/*"]}, "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "esModuleInterop": true, "allowJs": true, "module": "ESNext", "jsx": "preserve", "outDir": "./dist", "noEmit": true, "target": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"]}}