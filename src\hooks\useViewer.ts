import {
  reactive,
  render,
  h,
  createVNode,
  defineComponent,
  type VNode
} from 'vue'
import { ElImageViewer } from 'element-plus'
export const useViewer = (props: any) => {
  let vm: VNode | null = null
  const data = reactive({
    ...props
  })
  const Com = defineComponent({
    render () {
      return h(ElImageViewer, {
        ...data,
        
        zIndex: 9999,
        onClose: () => {
          
          close()
        }
      })
    }
  })

  vm = createVNode(Com)
  const div: HTMLDivElement = document.createElement('div')
  setTimeout(() => {
    render(vm, div)
  })
  function open (target = document.body) {
    if (!vm) return
    target.appendChild(div)
  }
  function close () {
    if (div && div.parentNode) {
      div.parentNode.removeChild(div)
    }
  }
  return {
    open,
    close,
    setUrl (urls: string[]) {
      data.urlList = urls
    }

  }
}
