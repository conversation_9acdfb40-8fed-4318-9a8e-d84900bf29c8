import BaseData, { getOperate } from '../common'
import type { componentOperateListType, ComponentType, OptionType } from '@/types/preObjectManage'
import { fieldTypeConfig, fieldTypeEnum, operateListEnum } from '@/enums/preObjectManage'
import { keysIn } from 'lodash-es'
import { isNullOrUndefOrEmpty } from '@/utils/is'

// 单行文本1
export class SingleLineText extends BaseData {
  public fieldProperty: string
  public limitNum: number
  constructor (data: ComponentType, options: OptionType) {
    console.log(data, 'data123')
    const fieldTypeName = '单行文本'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.LENGTH,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.COVER,
        value: false
      },
      {
        code: operateListEnum.GLOBAL_COVER,
        value: false
      },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData: ComponentType = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.singleLineText,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.singleLineText
    this.limitNum = 20
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 多行文本
export class MultilineText extends BaseData {
  public fieldProperty: string
  public limitNum: number
  constructor (data: ComponentType, options: OptionType) {
    const fieldTypeName = '多行文本'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.LENGTH,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.COVER,
        value: false
      },
      {
        code: operateListEnum.GLOBAL_COVER,
        value: false
      },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.multilineText,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.multilineText
    this.limitNum = 100
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 金额
export class Money extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '金额'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.RANGE,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.LENGTH,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.money,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.money
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   if (validateResult.success) {
  //     checkMinAndMax(validateResult)
  //   }
  //   return validateResult
  // }
}

// 分率
export class Percent extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '百分比'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.RANGE,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.LENGTH,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.percentage,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.percentage
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   if (validateResult.success) {
  //     checkMinAndMax(validateResult)
  //   }
  //   return validateResult
  // }
}

// 数字
export class Numbers extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '数字'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.RANGE,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.LENGTH,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.number,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.number
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   if (validateResult.success) {
  //     checkMinAndMax(validateResult)
  //   }
  //   return validateResult
  // }
}

// 手机
export class Phone extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '手机'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.PHONE_EMPTY,
        value: false
      },
      {
        code: operateListEnum.PHONE_CODE,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.COVER,
        value: false
      },
      {
        code: operateListEnum.GLOBAL_COVER,
        value: false
      },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.phone,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.phone
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 统一社会信用代码
export class CreditCode extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '统一社会信用代码'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.COVER,
        value: false
      },
      {
        code: operateListEnum.GLOBAL_COVER,
        value: false
      },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.creditCode,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.creditCode
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 身份证
export class IdCard extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '身份证号'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.COVER,
        value: false
      },
      {
        code: operateListEnum.GLOBAL_COVER,
        value: false
      },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.idCard,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.idCard
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
// 邮箱
export class Email extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '邮箱地址'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      {
        code: operateListEnum.COVER,
        value: false
      },
      {
        code: operateListEnum.GLOBAL_COVER,
        value: false
      },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.email,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.email
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 座机号
export class Landline extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '座机号'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.MUST,
        value: false
      },
      {
        code: operateListEnum.REPEAT,
        value: false
      },
      {
        code: operateListEnum.ADD_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.EDIT_PAGE_EDIT,
        value: true
      },
      {
        code: operateListEnum.DETAIL_EDIT,
        value: true
      },
      {
        code: operateListEnum.LIST_EDIT,
        value: true
      },
      // {
      //   code: operateListEnum.COVER,
      //   value: false
      // },
      // {
      //   code: operateListEnum.GLOBAL_COVER,
      //   value: false
      // },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.landline,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.landline
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}

// 流水号
export class Serial extends BaseData {
  public fieldProperty: string
  constructor (data :ComponentType, options :OptionType) {
    // 默认的字段配置名称
    const fieldTypeName = '流水号'
    const columnOperateList = [
      {
        code: operateListEnum.COLUMN_SHOW,
        value: true
      },
      {
        code: operateListEnum.CREATE_POSITION,
        value: true
      },
      {
        code: operateListEnum.DETAIL_POSITION,
        value: true
      },
      {
        code: operateListEnum.ENABLE,
        value: true
      },
      {
        code: operateListEnum.GLOBAL_ENABLE,
        value: true
      },
      {
        code: operateListEnum.COVER,
        value: false
      },
      {
        code: operateListEnum.GLOBAL_COVER,
        value: false
      },
      {
        code: operateListEnum.SHOW,
        value: true
      },
      {
        code: operateListEnum.EXPORT,
        value: false
      },
      {
        code: operateListEnum.IMPORT,
        value: false
      }

    ]
    // 字段默认数据
    const defaultData = {
      ...data,
      columnInfo: {
        ...data.columnInfo,
        name: data.columnInfo?.name || fieldTypeName,
        code: data.columnInfo?.code || fieldTypeConfig.serial,
        columnOperateList: data.id ? data.columnInfo?.columnOperateList : columnOperateList.map(item => getOperate(item.code, data, options, item.value)),
        fieldType: !isNullOrUndefOrEmpty(data.columnInfo.fieldType) ? data.columnInfo.fieldType : fieldTypeEnum.CUSTOM_EXTEND,
        dataMark: !isNullOrUndefOrEmpty(data.columnInfo?.dataMark) ? data.columnInfo?.dataMark : 0
      }

    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.serial
  }

  // 检验自身数据
  // validate () {
  //   const validateResult = new FieldValidateResult(this.fieldBaseData)
  //   validateName(validateResult)
  //   return validateResult
  // }
}
