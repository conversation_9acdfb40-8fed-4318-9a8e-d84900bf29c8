// src/hooks/useRouteCache.ts
import { ref, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'

const caches = ref<string[]>([])
const refreshPages = new Map<string, boolean>()
let collect = false

export default function useRouteCache () {
  const route = useRoute()
  
  // 收集当前路由相关的缓存
  function collectRouteCaches () {
    route.matched.forEach(routeMatch => {
      const componentDef: any = routeMatch.components?.default
      console.log('[ componentDef ] >', componentDef, routeMatch )
      const componentName = componentDef?.name || componentDef?.__name
      if (!componentName) {
        console.warn(`${routeMatch.path} 路由的组件名称name为空`)
        return
      }
      addCache(componentName)
    })
  }
  
  // 收集缓存（通过监听）
  function collectCaches () {
    if (collect) {
      console.warn('useRouteCache：不需要重复收集缓存')
      return
    }
    collect = true
    watch(() => route.path, collectRouteCaches, {
      immediate: true
    })
  }
  
  // 添加缓存的路由组件
  function addCache (componentName: string | string[]) {
    if (Array.isArray(componentName)) {
      componentName.forEach(addCache)
      return
    }

    if (!componentName || caches.value.includes(componentName)) return
    caches.value.push(componentName)
    console.log('[  caches.push] >', caches.value )
  }

  // 移除缓存的路由组件
  function removeCache (componentName: string | string[]) {
    if (Array.isArray(componentName)) {
      componentName.forEach(removeCache)
      return
    }
    
    const index = caches.value.indexOf(componentName)
    if (index > -1) {
      refreshPages.delete(componentName)
      return caches.value.splice(index, 1)
    }
  }
  // 清空所以缓存
  function removeAllCache () {
    caches.value = []
    clearRefreshPages()
  }
  // 移除缓存的路由组件的实例
  async function removeCacheEntry (componentName: string) {
    if (removeCache(componentName)) {
      await nextTick()
      addCache(componentName)
    }
  }
  // 保存需要刷新的页面 - 用于编辑页面变动后返回刷新列表
  function addRefreshPage (componentName?: string) {
    const { from } = route.meta
    const pageName = componentName || (from || '') as string
    const isHas = refreshPages.get(pageName)
    pageName && !isHas && refreshPages.set(pageName, true)
  }
  // 移除需要刷新的页面
  function checkIsNeedRefresh (componentName?: string) {
    const pageName = componentName || route.name as string
    const refresh = refreshPages.has(pageName)
    refreshPages.delete(pageName)
    return refresh
  }
  // 清除需要刷新的页面
  function clearRefreshPages () {
    refreshPages.clear()
  }
  function getRefreshPage () {
    return Array.from(refreshPages.keys())
  }
  return {
    collectCaches,
    caches,
    addCache,
    removeCache,
    addRefreshPage,
    removeAllCache,
    getRefreshPage,
    removeCacheEntry,
    checkIsNeedRefresh
  }
}
