<template>
  <div class="organizational">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索部门"
        clearable
        :prefix-icon="Search"/>
      <div class="treeList">
        <el-tree 
          node-key="id"
          ref="treeRef"
          :data="treeData"
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          @node-expand="nodeToggle"
          @node-collapse="nodeToggle"
          @node-click="nodeClick">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="custom-tree-node-item" :class="data.id === treeData?.[0].id ? 'treetop' : ''">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="`${data.name}(${data.len || 0})`"
                  placement="top-start"
                >
                  <span>{{data.name}}({{data.len || 0}})</span>
                </el-tooltip>
                <!-- 只在第一层展示 -->
                <span class="treeoperate" v-if="data.id === treeData?.[0].id" @click.stop="handleToggle">
                  {{treeStatue ? '收起' : '展开'}}子部门 
                </span>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    <div class="right">
      <el-radio-group
        v-model="checkUserId">
        <div 
          class="childNodeItem"
          v-for="(item, index) in userList"
          :key="index">
          <el-radio @change="selectChange(item)" :label="item.id">{{ item.realName }}</el-radio>
        </div>
      </el-radio-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { watch, ref, unref, computed, type PropType } from 'vue'
import type { ElTree } from 'element-plus'
import type { Tree } from 'element-plus/es/components/tree-v2/src/types'
import type { DeComponentTreeVO, UserVOList } from '../type'

const emit = defineEmits(['update:data', 'updateValue'])
const treeRef = ref<InstanceType<typeof ElTree>>()
const treeStatue = ref<boolean>(false) // 组织架构展开收起状态，默认收起
const userList = ref<any>([])
const currentNodeData = ref({}) // 当前选中节点
const checkUserId = ref<string>('')
const isIncludeChild = ref<boolean>(false)

const props = defineProps({
  data: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  },
  checkedUser: {
    type: Array as PropType<DeComponentTreeVO[]>,
    default: () => {[]}
  }
})

const treeData = computed(() => {
  return unref(props.data)
})

watch(() => props.checkedUser, async (checkedUser) => {
  checkUserId.value = checkedUser?.[0]?.id
}, {
  immediate: true,
  deep: true
})

console.log(treeData.value, 'deComponentTree123')

const personnelSelectionInput = ref('')

// 节点点击
async function nodeClick (currentNode: Recordable<any>) {
  console.log(currentNode)
  if(currentNode.len === 0) {
    return
  }
  currentNodeData.value = currentNode
  userList.value = currentNode.userVOList || []
}

// 展开收起全部
function handleToggle () {
  treeStatue.value = !treeStatue.value
  console.log(treeRef.value?.store.nodesMap)
  let nodesData = treeRef.value?.store.nodesMap
  for (let i in nodesData) {
    if(nodesData[i].childNodes && nodesData[i].childNodes.length > 0) {
      nodesData[i].expanded = treeStatue.value
    }
  }
}

function nodeToggle (data, currentNode, node) {
  console.log(data)
  console.log(currentNode)
  console.log(node)
  console.log(treeRef.value?.store.nodesMap)
  setTimeout(() => {
    const nodesData = treeRef.value?.store.nodesMap
    let expandedVal = false
    for (let i in nodesData) {
      if(nodesData[i].expanded) {
        expandedVal = true
      }
    }
    treeStatue.value = expandedVal
  }, 0)
}

const filterNode = (value: string, data: Tree) => {
  console.log(data)
  if (!value) return true
  return data?.name.includes(value)
}
watch(personnelSelectionInput, (val) => {
  treeRef.value!.filter(val)
})

// 父部门不包含子部门
async function handleIncludeChild (val:boolean){
  console.log('父部门不包含子部门', val)
  isIncludeChild.value = val
  userList.value = []
}

function selectChange (item: UserVOList) {
  console.log(checkUserId.value, 'checkUserId')
  emit('updateValue', [item])
}

defineExpose({
  handleIncludeChild
})

</script>

<style lang="scss" scoped>
.organizational {
  @include flex-center(row, normal, normal);
  height: 100%;
  .left {
    width: 284px;
    border-right: 1px solid #EBEEF5;
    padding: 16px 16px 16px 0;
    .custom-tree-node {
      // width: calc(100% - 24px);
      line-height: 12px;
      // @include flex-center(row, space-between, center);
      &-item {
        .treelabel {
          width: 100%;
          :deep(.custom-tooltip-content) {
            text-align: left !important;
          }
        }
        .treeoperate {
          font-size: $font-size-mini;
          color: $primary-color;
          margin-right: 16px;
        }
      }
      .treetop {
        width: 242px;
        @include flex-center(row, space-between, center);
      }
    }
    .treeList {
      overflow-y: auto;
      height: calc(100% - 32px);
      :deep(.el-tree-node.is-expanded>.el-tree-node__children) {
        display: block;
        min-width: 100%;
      }
    }
  }
  .right {
    flex: 1;
    padding: 16px 0 16px 16px;
    overflow-y: auto;
    .childNodeItem {
      display: block;
      width: 100%;
    }
  }
  .personnelSelectionInput {
    border-radius: 4px;
    :deep(.el-input__wrapper) {
      box-shadow: none;
      background: #F4F4F5;
    }
    margin-bottom: 13px;
  }
}
</style>