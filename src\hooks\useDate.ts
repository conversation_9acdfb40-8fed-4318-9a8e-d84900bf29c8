import { ref, onBeforeUnmount } from 'vue'
import dayJS from 'dayjs'
import duration from 'dayjs/plugin/duration'
dayJS.extend(duration)
export function useDateDown (date:string){
  const diff = ref()
  const dateText = ref({})
  let timerId: ReturnType<typeof setInterval> 
  const updateTime = () => {
    diff.value = dayJS.duration(dayJS(date).diff(dayJS()))
    dateText.value = {
      d: diff.value.days(),
      h: diff.value.hours(),
      m: diff.value.minutes(),
      s: diff.value.seconds()
    }
  }
  
  // eslint-disable-next-line prefer-const
  timerId = setInterval(updateTime, 1000)
  onBeforeUnmount(() => {
    clearInterval(timerId)
  })
  return {
    dateText
  }
}