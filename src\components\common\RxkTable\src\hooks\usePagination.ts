import { computed, ref, unref } from 'vue'
import { type ComputedRef } from 'vue'
import type { BasicTableProps, PaginationProps } from '../type'

export function usePagination (props: ComputedRef<BasicTableProps>){
  const paginationInfo = ref({
    pageSize: 20,
    pageNum: 1
  })
  const pageSizes = [5, 10, 20, 50, 100, 200]
  const getPaginationInfo = computed((): PaginationProps => {
    return { ...unref(paginationInfo) }
  })

  // 修改分页
  function handleSizeChange (val: number){
    paginationInfo.value.pageSize = val
  }
  function handleCurrentChange (val: number){
    paginationInfo.value.pageNum = val
  }
  return {
    getPaginationInfo,
    handleSizeChange,
    handleCurrentChange,
    pageSizes
  }
}
