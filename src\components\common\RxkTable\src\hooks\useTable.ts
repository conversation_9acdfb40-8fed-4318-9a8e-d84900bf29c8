// import { getScreenHeight } from '@/utils/dom'
import type { BasicTableProps, TableActionType } from '../type'
import { unref, ref, onUnmounted } from 'vue'
export function useTable (props: BasicTableProps):[(instance: TableActionType) => void, TableActionType] {
  // 获取表格要展示的高度
  // function getTableHeight () {
  //   const screenHeight = getScreenHeight()
  //   return screenHeight > 500 ? screenHeight - 430 : 700
  // }
  const tableRef = ref<Nullable<TableActionType>>(null)
  function register (instance: TableActionType) {
    onUnmounted(() => {
      tableRef.value = null
    })
    if (instance === unref(tableRef)) return
    tableRef.value = instance
    props && instance.setProps(props)
  }
  function getTableInstance (): TableActionType {
    const table = unref(tableRef)
    return table as TableActionType
  }
  const methods: TableActionType = {
    setProps: (props: Partial<BasicTableProps>) => {
      getTableInstance().setProps(props)
    },
    // 设置搜索条件
    setSearchInfo: (props: Recordable) => {
      getTableInstance().setSearchInfo(props)
    },
    // 重新请求表格数据
    reload: async () => {
      return await getTableInstance().reload()
    },
    // 获取已选项
    getSectionData: () => {
      return getTableInstance().getSectionData()
    },
    // 获取表格数据
    getTableData: () => {
      return getTableInstance()?.getTableData()
    }
  }
  return [register, methods]
}