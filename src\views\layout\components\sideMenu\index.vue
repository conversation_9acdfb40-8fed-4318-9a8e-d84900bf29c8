<template>
  <div :class="['sideMenu-content', config.layout.shrink ? 'shrink' : '']">
    <el-menu
      class="rxk-role-menu"
      :collapse="isCollapse"
      background-color="#202941"
      text-color="#fff"
      active-text-color="#fff"
      :defaultActive="defaultActiveName"
      :unique-opened="true"
    >
      <div class="logo" @click="hideVisible">
        <template v-if="!isCollapse">
          <img :src="logo" alt="" >
          <span class="iconfont icon icon-menu-unfold" />
        </template>
        <template v-else>
          <span class="iconfont icon icon-menu-fold" />
        </template>
      </div>
      <MenuItem :list="menuList" :collapse="config.layout.menuCollapse" />
    </el-menu>
  </div>
</template>
<script lang="ts" setup>
import { ref, unref, onMounted, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import MenuItem from './MenuItem.vue'
import type { RoleMenuType } from '@/types/menu'
import { isEmpty } from '@/utils/is'
import { recursion } from '@/utils/tools'
import { usePermissionStore } from '@/stores/modules/permission'
import { useConfig } from '@/stores/modules/config'
import { closeShade } from '@/utils/pageShade'
const isCollapse = ref(false)
const usePermission = usePermissionStore()
const config = useConfig()
const router = useRouter()
const route = useRoute()

const menuWidth = computed(() => config.menuWidth())
// 接口返回的权限菜单
const menuList = computed(() => {
  return usePermission.menuList
})
function hideVisible () {
  if(config.layout.shrink){
    config.toggleMenuExpand()
    closeShade()
  } else {
    isCollapse.value = !unref(isCollapse)
  }
}
const defaultActiveName = ref('')

const logo = computed(
  () =>
    new URL(
      '/src/assets/images/base/logo.png',
      import.meta.url
    ).href
)
const { currentRoute } = useRouter()
watch(
  () => unref(currentRoute).path,
  () => {
    getDefaultActive(route)
  }
)

// 获取当前高亮菜单
function getDefaultActive (route: any) {
  const menuListArr = unref(menuList)
  if (route.path === '/') {
    if (!isEmpty(menuListArr)) {
      const menu = menuListArr[0] as RoleMenuType
      if (menu) {
        let path = ''
        recursion(menu.children, (val) => {
          // 子级菜单showed都是false或子级为空就return
          if (val.children?.length) {
            const hasShowed = val.children?.find((el: Recordable) => el.showed)
            if (!hasShowed) {
              path = val.path
              return true
            }
          } else if (isEmpty(val.children)) {
            path = val.path
            return true
          }
        })
        jumpDefaultActive(path)
      }
    }
  } else {
    defaultActiveName.value = route.path
  }
}
function jumpDefaultActive (path: string) {
  if (route.path === path || route.path === '/login') return
  router.push({
    path
  })
}

onMounted(() => {
  getDefaultActive(route)
})
</script>
<style lang="scss" scoped>
.sideMenu-content {
  height: 100%;
  background-color: #202941;
  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    img {
      width: 100px;
      height: auto;
    }
    span {
      font-size: 20px;
      color: #fff;
      cursor: pointer;
    }
  }
  .collapse-logo {
    width: 45px;
  }
  .rxk-role-menu {
    height: 100%;
    border-right: none;
    overflow-y: auto;
    overflow-x: hidden;
    &:not(.el-menu--collapse) {
      width: 200px;
    }
    &.el-menu--collapse {
      width: 60px;
    }
  }
  .el-menu--collapse {
    :deep(.el-tooltip__trigger) {
      .el-sub-menu__icon-arrow {
        display: none;
      }
    }
  }
  .menu-logo {
    height: 64px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .menu-container {
    width: calc(100% + 10px);
    height: calc(100% - 64px);
    overflow-x: hidden;
    overflow-y: scroll;
    .el-menu-vertical {
      border-right: 0;
    }
  }
}
.shrink {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999999;
  background: var(--ba-bg-color-overlay);
  margin: 0;
  height: 100%;
  overflow: hidden;
  transition: width 0.3s ease;
  width: v-bind(menuWidth);
}
</style>
