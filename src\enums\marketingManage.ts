/**
 * 流量联盟相关枚举
 * 平台管理、产品管理等模块
 */

// 动态资质字段枚举
export enum FieldTypeEnum {
  INPUT = 10,
  SINGLE = 20,
  MULTIPLE = 30,
  SELECT = 40,
  IMG = 50,
  FILE = 60,
  VIDEO = 70
}
export const fieldTypes = [
  { value: FieldTypeEnum.INPUT, label: '输入框' },
  { value: FieldTypeEnum.SINGLE, label: '单选' },
  { value: FieldTypeEnum.MULTIPLE, label: '多选' },
  { value: FieldTypeEnum.SELECT, label: '下拉选择' },
  { value: FieldTypeEnum.IMG, label: '图片' },
  { value: FieldTypeEnum.FILE, label: '文件' },
  { value: FieldTypeEnum.VIDEO, label: '视频' }
]

export const merchantInfoSyncTypeList = [
  { value: 10, label: '线上API' },
  { value: 20, label: '线下' }
]

export const productLevelList = [
  { value: 1, label: 'A' },
  { value: 2, label: 'B' },
  { value: 3, label: 'C' },
  { value: 4, label: 'D' },
  { value: 5, label: 'E' }
]

export const productCategoryList = [
  { value: 1, label: '精准产品' },
  { value: 2, label: '商机产品' },
  { value: 3, label: '流量包产品' },
  { value: 4, label: '线索产品' },
  { value: 5, label: '多区域获客产品' }
]

export const productTypeList = [
  { value: 1, label: '房主贷' },
  { value: 2, label: '车主贷' },
  { value: 3, label: '社保贷' },
  { value: 4, label: '公积金贷' },
  { value: 5, label: '信用贷' }
]

export const businessProductTypeList = [{ value: 6, label: '轻松贷' }]

export const zoneProductTypeList = [
  { value: 7, label: '车抵贷' },
  { value: 8, label: '房抵贷' }
]
export const flowProductTypeList = [{ value: 9, label: '极速贷' }]

export const auditStatusList = [
  { value: 0, label: '待审核' },
  { value: 1, label: '通过' },
  { value: 2, label: '不通过' }
]
export const wechatAuditStatusList = [
  { value: 0, label: '待审核' },
  { value: 1, label: '通过' },
  { value: 2, label: '不通过' }
]

export const userLevelEnum = [
  { value: 0, label: '零星' },
  { value: 10, label: '一星' },
  { value: 20, label: '二星' },
  { value: 30, label: '三星' },
  { value: 40, label: '四星' },
  { value: 50, label: '五星' }
]
// 时段选择options
export const timeFrameOptions = () => {
  const arr: {value: number, label: string}[] = []
  for (let i = 0; i < 24; i++) {
    arr.push({ label: `${i}时`, value: i })
  }
  return arr
}

/**
 * 产品模式
 */
export const productModeEnum = [
  { label: '单筛', value: 1 },
  { label: '三选一', value: 2 },
  { label: '二选一', value: 3 },
  { label: '五选一', value: 4 }
]

/**
 * 是否枚举
 */
export const commonWhetherEnum = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]