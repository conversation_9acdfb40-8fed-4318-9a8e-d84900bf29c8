import process from 'process'
import { getAppEnvConfig } from '../utils'

export function commonConfig (mode: string) {
  const env = getAppEnvConfig(mode)
  return {
    root: process.cwd(),
    base: './', // 开发或生产环境服务的公共基础路径
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
          @import "@/assets/styles/variables.scss";
          @import "@/assets/styles/mixin.scss";
         `
        }
      }
    },
    server: {
      port: env.VITE_PORT,
      open: false,
      https: false,
      host: '0.0.0.0'
    }
  }
}
