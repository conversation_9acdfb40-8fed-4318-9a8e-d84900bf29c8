<template>
  <div class="permission">
    <div class="permission-right">
      <dt class="title">特殊业务逻辑</dt>
      <dl class="content">
        <dt>脱敏字段</dt>
        <dd>
          <template v-for="item in desensitizeList" :key="item.fieldCode">
            <RxkCheckbox
              v-model="item.checkFlag"
              :label="item.fieldName"
              :disabled="item.disabled"
            />
          </template>
        </dd>
        <!-- <dt class="special">
          <span>查看或修改产品价格</span>
          <el-tooltip
            effect="dark"
            content="控制是否允许查看或修改营销管理处【信贷产品】【站外信贷】的产品价格"
            placement="top"
          >
            <el-icon><WarningFilled /></el-icon>
          </el-tooltip>
        </dt>
        <dd>
          <el-radio-group
            v-model="productAuthList"
            :disabled="permissionManageStore.isSuper"
          >
            <el-radio
              v-for="item in productAuth"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </dd> -->
      </dl>
      <dt class="title">菜单与功能权限</dt>
      <div class="content" v-loading="roleMenuLoading">
        <menu-function
          ref="menuFunctionDom"
          :data="roleMenuList"
          :expanded="expandedKeys"
          :checked-list="roleMenuCheckedList"
          :handle-save-data="handleSaveData"
          @change-menu="handleSelectRole"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import MenuFunction from '@/views/systemManage/permissionManage/menuFunction/menuFunction.vue'
import { ref } from 'vue'
import {
  getDesensitizeApi,
  getRoleMenuApi,
  setDesensitizeApi,
  getProductPricePermissionApi,
  setProductPricePermissionApi
} from '@/apis/permissionManage'
import { usePermissionManageStore } from '@/stores/modules/permission'
import type {
  MenuResourceVOList,
  Permission
} from '@/apis/interface/permissionManage'
import RxkCheckbox from '@/components/common/RxkCheckbox/src/BasicCheckbox.vue'
import { WarningFilled } from '@element-plus/icons-vue'
import { isNullOrUndefOrEmpty } from '@/utils/is'
import { useAccountStore } from '@/stores/modules/account'
const useAccount = useAccountStore()

const permissionManageStore = usePermissionManageStore()

const menuFunctionDom = ref()
const roleMenuLoading = ref<boolean>(false)
const expandedKeys = ref<string[]>([])
const roleMenuCheckedList = ref<string[]>([])
const desensitizeList = ref<Permission.ReqGetDesensitizeReturn['fieldList']>([])
const roleMenuList = ref<Permission.ReqRoleMenuReturn[]>([])
const menuChildren = ref<Permission.ReqRoleMenuReturn[]>([])

const handleSaveDesensitize = async () => {
  const info = await getDesensitizeApi(permissionManageStore.roleId)
  const fieldList = desensitizeList.value.filter((item) => item.checkFlag)
  await setDesensitizeApi({
    appList: [{ appCode: info[0].appCode, fieldList }],
    roleId: permissionManageStore.roleId
  })
}

const getCheckedId = (list: Permission.ReqRoleMenuReturn[]) => {
  const idList: string[] = []
  list.forEach((item) => {
    if (permissionManageStore.isSuper) {
      idList.push(item.id)
    } else {
      if (item.isResource && item.checkFlag) {
        idList.push(item.id)
      } else if (item.checkFlag && !item.isHalf) idList.push(item.id)
    }
    if (item.children?.length) idList.push(...getCheckedId(item.children))
  })
  return idList
}

const handleSelectRole = async (id: Permission.ReqRoleListReturn['id']) => {
  try {
    roleMenuLoading.value = true
    roleMenuList.value = []
    menuChildren.value = []
    permissionManageStore.setMenuIndex(-1)
    await getDesensitize()
    // await getProductAuth()
    roleMenuList.value = formatChildren((await getRoleMenuApi(id)) || [])
    roleMenuCheckedList.value = getCheckedId(roleMenuList.value)
  } catch (e) {
    roleMenuList.value = []
    menuChildren.value = []
  } finally {
    roleMenuLoading.value = false
  }
}

const formatResourceList = (
  list: MenuResourceVOList[]
): MenuResourceVOList[] => {
  let resources: MenuResourceVOList[] = []
  if (list?.length) {
    resources = list.map((item) => {
      return {
        ...item,
        checkFlag: permissionManageStore.isSuper
          ? true
          : item?.checkFlag || false
      }
    })
  }
  return resources
}

const formatChildren = (children: Permission.ReqRoleMenuReturn[]) => {
  const list: Permission.ReqRoleMenuReturn[] = []
  children.forEach((item) => {
    list.push({
      ...item,
      children: formatChildren(item.children || []),
      checkFlag: permissionManageStore.isSuper ? true : item.checkFlag,
      resourceVOList: formatResourceList(item.resourceVOList),
      disabled: permissionManageStore.isSuper
    })
  })
  return list
}

const getDesensitize = async () => {
  try {
    const data = await getDesensitizeApi(permissionManageStore.roleId)
    if (data) {
      desensitizeList.value = data[0].fieldList.map((el) => {
        return {
          ...el,
          checkFlag: permissionManageStore.isSuper ? false : el.checkFlag,
          disabled: permissionManageStore.isSuper
        }
      })
    }
  } catch (e) {
    console.error(e)
  }
}

const productAuthList = ref()
const productAuth = [
  { value: 'NO', label: '无权限' },
  { value: 'VIEW', label: '可查看' },
  { value: 'ALL', label: '可查看和修改' }
]

const getProductAuth = async () => {
  const data = await getProductPricePermissionApi(permissionManageStore.roleId)
  if (!isNullOrUndefOrEmpty(data)) {
    productAuthList.value = data?.productPriceType?.name || 'NO'
  } else {
    if (permissionManageStore.isSuper) productAuthList.value = 'ALL'
    else productAuthList.value = 'NO'
  }
}

const saveProductAuth = async () => {
  await setProductPricePermissionApi({
    productPriceType: productAuthList.value,
    roleId: permissionManageStore.roleId
  })
  // useAccount.getPriceAuth()
}

const handleSaveData = async () => {
  await handleSaveDesensitize()
  // await saveProductAuth()
  await menuFunctionDom.value?.handleSavePermission()
}

defineExpose({
  handleSaveData,
  handleSelectRole
})
</script>

<style scoped lang="scss">
.permission {
  width: 100%;
  height: 100%;
  display: flex;

  .permission-left {
    width: 142px;
    border-right: 1px solid #ebeef5;
    padding: 10px 0;

    li {
      cursor: pointer;
      padding: 0 16px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;

      &.ac,
      &:hover {
        color: #5687ff;
        background-color: #eef3ff;
      }
    }
  }

  .permission-right {
    padding: 16px;
    width: 100%;
    height: 100%;
    overflow-y: auto;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 700;
      height: 32px;
    }

    .content {
      padding: 16px;
      background-color: #f5f7fa;
      margin-bottom: 16px;
      font-size: 14px;

      dt {
        padding-bottom: 18px;
      }

      dd {
        padding-bottom: 18px;
      }
      .special {
        display: flex;
        align-items: center;
        .el-icon {
          margin-left: 5px;
          color: #aaa;
        }
      }
    }
  }
}
</style>
