import { ref, unref, onMounted, onUnmounted } from 'vue'
import type { Ref } from 'vue'

export function useAutoHeight (option:Ref<HTMLElement>[]){
  const height = ref(0)
  const elementList = ref<Ref<HTMLElement>[]>(option)

  function computedHeight (){
    height.value = 0
    if(unref(elementList)?.length){
      unref(elementList).forEach((item) => {
        height.value += +unref(item)?.offsetHeight || 0
      })
    }
  }

  function heightStyle (){
    return `calc(100% - ${height.value}px)`
  }

  onMounted(() => {
    setTimeout(() => {
      computedHeight()
    }, 500)
    window.addEventListener('resize', computedHeight)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', computedHeight)
  })

  return {
    heightStyle
  }
}