<template>
  <div class="searchFilter-box" :class="[ config.layout.shrink ? 'shrink' : '' ]">
    <el-form
      v-on:submit.prevent
      ref="searchFormRef"
      :class="['custom-form',  config.layout.shrink ? 'custom-form-shrink' : '', collapse ? 'oneline' : '']"
      :inline="true"
      :model="formData"
    >
      <template v-for="schema in getSchema" :key="schema.fieldName">
        <FormItem :isDialog="isDialog"
                  :formData="formData"
                  :schema="schema" 
                  @keyup.enter.stop="handleEnter"
        />
      </template>
    </el-form>
    <div class="btn-box">
      <el-space :size="10" alignment="top">
        <RxkButton @click="search"
                   style="border: 1px solid #5687ff;
        color: #5687ff;">搜索</RxkButton>
        <RxkButton v-if="isRefresh" @click="reset">刷新</RxkButton>
        <div v-else class="reset-btn icon iconfont icon-refresh" @click="reset" />
        <div class="reset-btn icon iconfont icon-add" v-if="showSetting" @click="setting" />
        <div class="icon-btn pointer"
             v-if="showCollapse || getSchema?.length > 4" 
             @click="handleCollapse">
          <span
            class="iconfont icon"
            :class="[
              !collapse ? 'icon-Property1zhankai' : 'icon-Property1shouqi'
            ]"
          />
        </div>
        <slot />
      </el-space>
    </div>
  </div>
</template>
<script lang="ts" setup>
import FormItem from './components/formItem.vue'
import { onMounted, computed, ref, unref, reactive, watch, toRaw } from 'vue'
import type { FormActionType, FormProps, FormSchema } from './type'
import { useConfig } from '@/stores/modules/config'
import { isArray, isObj } from '@/utils/is'

const emit = defineEmits(['register', 'search', 'handleCollapse', 'setting'])
const formData = reactive<Recordable>({})
const config = useConfig()
const searchFormRef = ref()
const propsRef = ref<Partial<FormProps>>({})
const getProps = computed((): FormProps => {
  return { ...unref(propsRef) }
})
const getSchema = computed((): FormSchema[] => {
  const schemas: FormSchema[] = unref(getProps).schemas as any
  return schemas
})

const props = defineProps({
  showCollapse: {
    type: Boolean,
    default: useConfig().layout.shrink
  },
  isDialog: {
    // 如果在弹窗使用该组件，请传入该参数
    type: Boolean,
    default: false
  },
  showSetting: {
    type: Boolean,
    default: false
  },
  isRefresh: {
    type: Boolean,
    default: false
  }
})
watch(
  () => getSchema.value,
  (schema) => {
  
    if (schema.length) {
      initDefault()
    }
  },
  {
    deep: true
  }
)

const collapse = ref(config.layout.shrink)
function handleCollapse () {
  collapse.value = !collapse.value
  emit('handleCollapse', collapse.value)
}

function initDefault () {
  const schemas = unref(getSchema)
  
  schemas.forEach((item) => {
    const { key, val } = item
    formData[key] = val || ''
    // defaultValue[key] = val || ''
  })
}
function handleSubmit () {
  search()
}
function setProps (formProps: Partial<FormProps>) {
  propsRef.value = formProps
  
}
function resetFormData () {
  const keys = Object.keys(formData)

  for (let key of keys) {
    if (isArray(formData[key])) formData[key] = []
    else if (isObj(formData[key])) formData[key] = {}
    else formData[key] = ''
  }
}
const formActionType: Partial<FormActionType> = {
  submit: handleSubmit,
  setProps,
  resetFormData
}
onMounted(() => {
  emit('register', formActionType)
})

function search () {
  emit('search', toRaw(formData))
}
function reset () {
  const { defaultValue = {} } = unref(getProps)
  const keys = Object.keys(formData)

  for (let key of keys) {
    if (isArray(formData[key])) formData[key] = defaultValue[key] || []
    else if (isObj(formData[key])) formData[key] = defaultValue[key] || {}
    else formData[key] = defaultValue[key] || ''
  }
  emit('search', toRaw(formData), true)
}
function resetFiled (key: string, value: any) {
  formData[key] = value || ''
}

function setting () {
  emit('setting')
}

function handleEnter (){
  search()
}

defineExpose({
  formData,
  resetFiled
})

</script>
<style lang="scss" scoped>
.oneline {
  overflow: hidden;
  height: 44px !important;
}
.searchFilter-box {
  display: flex;
  padding: 16px 16px 4px;
  width: 100%;
  .el-form {
    .el-form-item {
      align-items: center;
    }
  }
  &.shrink {
    flex-direction: column!important;
    padding-bottom: 16px;
  }
  .custom-form {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    &-shrink {
      flex: none;
      .custom-search-item {
        width: 50%!important;
      }
    }
  }
}
@media screen and (max-width: 480px) {
  .searchFilter-box .custom-form {
    &-shrink {
      .custom-search-item, 
      .custom-search-item:first-child {
        width: 100%!important;
      }
    }
  }
}
.icon-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    color: #666666;
    font-size: 13px;
  }
}
.btn-box {
  display: flex;
  .reset-btn {
    cursor: pointer;
    display: flex;
    width: 32px;
    height: 32px;
    padding: 5px 16px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid var(---, #dcdfe6);
    background: #fff;
  }
  :deep(.el-space__item:last-child) {
    margin-right: 0px !important;
  }
}
</style>
