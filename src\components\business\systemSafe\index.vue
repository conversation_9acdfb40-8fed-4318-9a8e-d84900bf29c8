<template>
  <RxkDialog
    v-model="dialogFormVisible"
    title="提示"
    width="480"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    @closed="handleDialogClose"
  >
    <div class="idle tw-px-[24px] tw-py-[16px]">
      <el-text class="tw-leading-5"
      >由于您长时间未操作系统，为保证您的数据安全，已自动冻结页面，请输入个人信息-账户设置处配置的安全密码解冻页面</el-text
      >
      <el-form
        :model="formParams"
        class="tw-mt-3"
        ref="reLoginRef"
        @submit.prevent
      >
        <el-form-item
          label="安全密码"
          prop="password"
          :rules="[
            {
              required: true,
              trigger: 'blur',
              validator: validatePass
            }
          ]"
        >
          <el-input
            v-model="formParams.password"
            autocomplete="off"
            type="password"
            maxlength="16"
            placeholder="请输入8~16位需包括字母,数字,特殊字符"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer tw-px-[24px] tw-py-[16px]">
        <el-button @click="logoutHandler">重新登录</el-button>
        <el-button type="primary" @click="loginHandler" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </RxkDialog>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import dayjs from 'dayjs'
import { RxkDialog } from '@/components/common/RxkDialog'
import { loginSafetyCode } from '@/apis/user'
import watermark from '@/utils/libs/waterMark'
import { useAccountStore } from '@/stores/modules/account'

const accountStore = useAccountStore()
import { createLocalStorage } from '@/utils/cache'
const ls = createLocalStorage()
import { useIdle } from '@vueuse/core'
import type { FormInstance } from 'element-plus'
import { useRoute } from 'vue-router'
import { setToken } from '@/utils/auth'

const dialogFormVisible = ref(false)

function validatePass (rule: any, value: any, callback: any) {
  const reg =
    /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])(?![\u4e00-\u9fa5])[a-zA-Z0-9~!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,16}$/

  if (!value) {
    callback(new Error('请输入安全密码'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入8~16位字母,数字,特殊字符'))
  } else {
    callback()
  }
}

const route = useRoute()

// 用于测试安全密码弹出时间
const intervalTime = computed(() => route.query.isTestSecurityPW ? 1 * 60 * 1000 : 30 * 60 * 1000)
const { idle } = useIdle(intervalTime.value)

let channel: BroadcastChannel | null
watch(idle, (val) => {
  if (val) {
    if (dialogFormVisible.value) return
    dialogFormVisible.value = true
    ls.set('isActive', 'false')
  }
})

function handleDialogClose () {
  dialogFormVisible.value = false
  reLoginRef.value?.resetFields()
}

const loading = ref(false)
const formParams = ref({
  password: ''
})
const reLoginRef = ref<FormInstance>()

const loginHandler = () => {
  reLoginRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      loginSafetyCode({
        username: accountStore.userInfo.username,
        safetyCode: formParams.value.password
      })
      .then((res) => {
        loading.value = false
        dialogFormVisible.value = false
        ls.remove('isActive')
        // 更新token
        setToken('bearer ' + res)
        watermark.set(
            `${accountStore?.userInfo?.realName}-${
              accountStore?.userInfo?.clientIp
            }-${dayjs().format('YYYY-MM-DD HH:mm')},版权所有：重庆市渝中区科融小额贷款有限责任公司`,
            'main-container-id'
        )
        channel?.postMessage({ action: 'refresh' })
      })
      .catch(() => {
        loading.value = false
      })
    }
  })
}

const logoutHandler = () => {
  accountStore.logout()
}

onMounted(() => {
  if (ls.get('isActive') === 'false') {
    dialogFormVisible.value = true
  }
})
// 不同标签页同时弹窗，关于token更换后，通知另外一个标签页刷新
watch(dialogFormVisible, (val) => {
  if (val) {
    channel = new BroadcastChannel('app-channel')
    channel.onmessage = function (event: any) {
      if (event.data.action === 'refresh') {
        location.reload()
      }
    }
  } else {
    channel?.close()
    channel = null
  }
})
// onBeforeUnmount(() => {
//   window.removeEventListener('beforeunload', beforeunloadHandler)
// })
</script>
