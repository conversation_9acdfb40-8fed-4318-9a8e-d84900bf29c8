import { computed, type ComputedRef, unref } from 'vue'
import type { BasicTableProps } from '@/components/common/RxkVTable/src/type'
import { cloneDeep } from '@/utils/tools'
import type { ColumnType } from '@/types/table'

export function useColumns (props: ComputedRef<BasicTableProps>) {

  const getColumnsRef = computed(() => {
    // return cloneDeep(unref(props).columns)
    return unref(props).columns
  })

  const getColumns = computed(() => {
    // const columns = cloneDeep(unref(getColumnsRef))
    // console.log(columns, 'columns222')
    // return columns?.map?.((item: ColumnType) => {
    //   return {
    //     ...item
    //   }
    // })
    return unref(getColumnsRef)
  })
  return {
    getColumns
  }
}