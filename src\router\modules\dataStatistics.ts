import type { AppRouteRecordRaw } from '@/router/types'

export const dataStatistics: AppRouteRecordRaw[] = [
  {
    path: 'dataStatistics',
    name: 'DataStatistics',
    redirect: '/dataStatistics/dataOverview',
    meta: {
      title: '数据统计',
      icon: 'shu<PERSON><PERSON><PERSON><PERSON>'
    },
    children: [
      {
        path: 'dataOverview',
        name: 'DataOverview',
        component: () => import('@/views/dataStatistics/dataOverview/index.vue'),
        meta: {
          title: '数据概览'
        }
      },
      {
        path: 'channelData',
        name: 'ChannelData',
        component: () => import('@/views/dataStatistics/channelData/index.vue'),
        meta: {
          title: '渠道数据统计'
        }
      },
      {
        path: 'userManage',
        name: 'UserManage',
        component: () => import('@/views/dataStatistics/userManage/index.vue'),
        meta: {
          title: '用户表'
        }
      },
      {
        path: 'thirdDataStatistics',
        name: 'thirdDataStatistics',
        component: () => import('@/views/dataStatistics/thirdDataStatistics/index.vue'),
        meta: {
          title: '三方数据统计'
        }
      }
    ]
  }
]
