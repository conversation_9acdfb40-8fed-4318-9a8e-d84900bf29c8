<template>
  <div class="personnelSelection">
    <!-- 自定义触发内容 -->
    <div v-if="custom" style="width: inherit" ref="customRef">
      <slot name="custom" />
    </div>
    <div v-else style="width: inherit">
      <!-- 默认触发内容 -->
      <el-select
        style="width: 100%"
        v-model="showValue"
        ref="selectRef"
        multiple
        :collapse-tags="collapseTags"
        @visible-change="visibleChange"
        @focus="focus"
        @remove-tag="removeTag"
        v-bind="$attrs"
        :disabled="disabled"
        :placeholder="placeholder"
      >
        <el-option
          v-for="(item, index) in personelTree.data"
          :key="index"
          :label="item.realName"
          :disabled="item.disable"
          :value="item.id"
        />
      </el-select>
    </div>
    <el-popover
      :visible="custom ? innerVisible : undefined"
      :virtual-ref="virtualRef"
      ref="popoverRef"
      trigger="click"
      title=""
      :disabled="disabled"
      popper-class="personnelSelectionPopper"
      width="740"
      :placement="placement"
      destroy-on-close
      :popper-options="{
        modifiers: [{ enabled: true }],
        strategy: 'fixed',
        placement: 'auto'
      }"
      virtual-triggering
      @show="show"
    >
      <SelectPanel
        @registerPanel="registerPanel"
        @close="close"
        @update="update"
        @confirm="confirm"
      >
        <template #dynamic>
          <slot name="dynamic"/>
        </template>
      </SelectPanel>
    </el-popover>
  </div>
</template>

<script lang="ts" setup>
import { ref, unref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import SelectPanel from './selectPanel.vue'
import type { BasicProps, UserVOList } from './type'
import { usePanel } from './hooks/usePanel'
import { isFunc } from '@/utils/is'
const emit = defineEmits([
  'update:visible',
  'update:value',
  'sure',
  'getUserList'
])
const personelTree = reactive<any>({
  data: {}
})

const popoverRef = ref()
const selectRef = ref()
const customRef = ref()
const virtualRef = ref()
const showPanel = ref<boolean>(false)
const showValue = ref<string[]>([])
const loadNum = ref<number>(0)

const innerVisible = computed({
  get: () => props.visible,
  set: (visible: boolean) => emit('update:visible', visible)
})

const props = withDefaults(defineProps<BasicProps>(), {
  custom: false,
  visible: false,
  activeName: 'members',
  paramsData: {},
  placeholder: '请选择',
  placement: 'bottom',
  collapseTags: true,
  disabled: false,
  menuId: ''
})

const [
  registerPanel,
  {
    init,
    updateValue,
    getCheckedUser,
    getContainChildren,
    getParameter,
    getSaveDefault,
    getSelectedShow,
    reload,
    handleCheckedValue
  }
] = usePanel()
watch(() => props, () => {
  console.log(props.showValueData, '组件已渲染AA', loadNum.value, props.visible)
  if (!props.disabled) {
    virtualRef.value = props.custom ? customRef.value : selectRef.value
  } else {
    unref(popoverRef)?.hide?.()
    virtualRef.value = ''
  }

  const fn = () => {
    nextTick(() => {
      init(unref(props))
      loadNum.value++
    })
  }
  
  if (loadNum.value === 0) {
    if ((props.showValueData && props.showValueData.length > 0) || props.data || props.visible) {
      fn()
    }
  } else {
    if (props.data && !props.api) {
      fn()
    } else {
      handleCheckedValue(props.showValueData || [])
    }
  }
}, {
  deep: true,
  immediate: true
})
function focus () {
  selectRef.value.blur()
}

async function visibleChange () {
  console.log(loadNum.value, 'loadNum.value')
  selectRef.value.blur()
  if (props.visibleBeforeFn && isFunc(props.visibleBeforeFn)) {
    const flag = await props.visibleBeforeFn()
    if (!flag) {
      close()
    }
  } else {
    if (loadNum.value === 0) {
      init(props)
      loadNum.value++
    } else {
      reload(props)
    }
  }
  unref(popoverRef).popperRef?.delayHide?.()
  showPanel.value = true
}

function close () {
  if (props.custom) {
    innerVisible.value = false
  } else {
    unref(popoverRef).hide?.()
  }
  selectRef?.value?.blur()
  showPanel.value = false
  console.log(innerVisible.value)
}

function getData () {
  return {
    userInfoList: getCheckedUser(),
    saveDefult: getSaveDefault(),
    dynamicParameters: getParameter(),
    selectedShow: getSelectedShow(),
    containChildren: getContainChildren()
  }
}

function update (data: UserVOList[]) {
  personelTree.data = data || []
  showValue.value = data.map(item => item.id) || []
  emit('getUserList', data)
}

function confirm () {
  personelTree.data = getCheckedUser() || []
  showValue.value = getCheckedUser().map((item: Recordable) => item.id)
  emit('sure', getData())
  close()
}

function removeTag (tagValue: any) {
  console.log(tagValue)
  console.log(getCheckedUser(), 'checkedValuecheckedValue')
  const checkedValue = unref(getCheckedUser())
  console.log(checkedValue, 'checkedValuecheckedValue')
  const findIndex = checkedValue.findIndex(item => item.id === tagValue)
  if (findIndex !== -1) {
    checkedValue.splice(findIndex, 1)
  }
  updateValue(checkedValue, 'clear')
  emit('sure', getData())
}

onMounted(async () => {
  if (!props.disabled) {
    virtualRef.value = props.custom ? customRef.value : selectRef.value
  }
})

function show (){
  console.log('打开了')
}
</script>

<style lang="scss">
.personnelSelectionPopper {
  padding: 0 !important;
  overflow-x: hidden;
  overflow-y: auto;
  height: auto;
}
</style>
