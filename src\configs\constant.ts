import { getEnvMode, isProdMode } from '@/utils/env'
import { localEnv } from './env/local'
import { devEnv } from './env/dev'
import { testEnv } from './env/test'
import { prodEnv } from './env/prod'
import { preEnv } from './env/pre'
import type { GlobalEnv } from './type'

let currentNodeEnv: string = getEnvMode() // 默认值

if (!isProdMode()) {
  // 只有在非打包环境才能改变模式, 打包环境则直接根据配置的变量来
  // 如果要在本地连不同的环境，直接放开注释即可
  // currentNodeEnv = 'development' // 连后端本地的时候用
  // currentNodeEnv = 'dev' // 连开发环境
  currentNodeEnv = 'test' // 连测试环境
  // currentNodeEnv = 'production'// 连正式环境
  // currentNodeEnv = 'pressure'// 连压力测试环境
 
}

const allNodeEnv: Recordable<GlobalEnv> = {
  development: localEnv,
  dev: devEnv,
  test: testEnv,
  pre: preEnv,
  production: prodEnv,
  self: testEnv // 本地打包连接测试环境
}

export const environment = allNodeEnv[currentNodeEnv]
