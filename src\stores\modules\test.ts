// import { defineStore } from "pinia";
/**
 * @desc: store有两种用法
 *  1.Option Store
 *  2.Setup Store
 * */
// 用法1 类似 Vuex，用和之前一样的方式来定义 Store
// export const useCounterStore = defineStore("counter", {
//   state: () => {
//     return { count: 0 };
//   },
//   actions: {
//     increment () {
//       this.count++;
//     }
//   }
// });

// 用法2 实现更多高级用法，你甚至可以使用一个函数 (与组件 setup() 类似) 来定义一个 Store
// export const useCounterStore = defineStore("counter", () => {
//   const count = ref(0);
//   function increment () {
//     count.value++;
//   }
//   return { count, increment };
// });

// 取名字 use你的名字Store 如 useCountStore
// const userCounter = useCounterStore() 不要去解构userCounter，因为它破坏了响应性
// const { name } = storeToRefs(userCounter) 想解构，可以通过storeToRefs将每一个响应式属性创建引用，比如将name创建引用
// // 作为 action 的 increment 可以直接解构，也就是说如果只是访问方法就可以解构
// const { increment } = userCounter
