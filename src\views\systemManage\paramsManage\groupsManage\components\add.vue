<template>
  <RxkDialog
    v-model="addVisible"
    :title="titleText"
    width="480px"
    :showFooter="false"
    :close-on-click-modal="false"
    >
    <div class="setting-box">
      <el-form :model="state.form"  ref="ruleFormRef" labelPosition="left" :rules="state.rules" label-width="130">
        <el-form-item label="参数分组名称：" prop="name">
          <RxkInput v-model="state.form.name" placeholder="请输入" maxlength="7" />
        </el-form-item>
        <el-form-item label="参数分组描述：" prop="description">
          <RxkInput v-model="state.form.description" placeholder="请输入" maxlength="50" />
        </el-form-item>
      </el-form>
    </div>
    <div class="footer-content">
      <RxkButton @click="handleCancel">取消</RxkButton>
      <RxkButton type="primary" @click="handleConfirm">确定</RxkButton>
    </div>
  </RxkDialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from "vue" 
import { isEmptyObject } from "@/utils/is";
import { RxkButton } from '@/components/common/RxkButton'
import { RxkInput } from "@/components/common/RxkInput/index";
import { RxkDialog } from "@/components/common/RxkDialog/index";
import type{ paramsItem } from "../../type";
import { paramGroupListApi } from "@/apis/paramsManage";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: ()=>{}
  },
  type: {
    type: String,
    default: ''
  }
})
const titleText = computed(()=>{
  return {
    'add': '系统参数添加',
    'edit': '编辑',
  }[props.type]
})
const emit = defineEmits(['update:visible', 'close', 'confirm'])
const addVisible = computed({
  get(){
    return props?.visible
  },
  set: (value: any) => emit('update:visible', value)
})
const state = reactive<any>({
  form: {
    code: '',
    description: '',
    groupId: '',
    name: '',
    readOnly: '',
    value: '',
    id: ''
  } as paramsItem,
  paramsGroupOptions: [],
  rules: {
    name: [
      { required: true, message: '请输入参数分组名称', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '请输入参数分组描述', trigger: 'blur' }
    ]
  },
  seatIds: [] as string[],
  showBindLine: false
})
watch(()=> props?.data, (val)=>{
  if(!isEmptyObject(val)){
    const {code, description, groupId, name, readOnly, value, id} = val as paramsItem
    state.form = {code, description, groupId, name, readOnly, value, id}
  }else{
    state.form = {
      code: '',
      description: '',
      groupId: '',
      name: '',
      readOnly: '',
      value: '',
      id: ''
    }
  }
}, { immediate: true})

const ruleFormRef = ref()

function handleConfirm (fn: Fn) {
  ruleFormRef.value?.validate?.((res: boolean)=>{
    if(res){
      emit('confirm', state.form, ()=>{
        fn?.()
      })
    }
  })
}
function handleCancel (fn: Fn, sign?: string) {
  fn?.()
  emit('close');
}

// 获取参数分组
function getParamsGroups () {
  paramGroupListApi().then((res)=>{
    state.paramsGroupOptions = res.length ? res.map((ele)=>({value: ele.id, label: ele.name})) : []
  })
}
getParamsGroups()
</script>


<style scoped lang="scss">
.setting-box{
  overflow-y: auto;
  padding: 16px 24px 0;
}
.footer-content{
  border-top: 1px solid #EBEEF5;
  padding: 16px 24px;
  @include flex-center(row, flex-end, center);
}
</style>