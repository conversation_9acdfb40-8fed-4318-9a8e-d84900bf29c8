<template>
  <el-config-provider :locale="locale" :message="ElConfig">
    <router-view />
  </el-config-provider>
</template>

<script lang="ts" setup>
import { onBeforeMount, onMounted, reactive, ref } from 'vue'
import { ElConfigProvider } from 'element-plus'
import { useVersion } from '@/hooks/useVersion'
import useRouteCache from '@/hooks/useRouteCache'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { useConfig } from '@/stores/modules/config'
import { useEventListener } from '@vueuse/core'
const config = useConfig()
const locale = ref(zhCn)
const ElConfig = reactive({
  max: 1
})
if (process.env.NODE_ENV !== 'development') {
  useVersion({})
}
const { collectCaches } = useRouteCache()
const state = reactive({
  autoMenuCollapseLock: false
})
collectCaches()
onBeforeMount(() => {
  onAdaptiveLayout()
  useEventListener(window, 'resize', onAdaptiveLayout)
})
onMounted(() => {
  if (document.body.clientWidth < 1024) {
    config.setShrink(true)
  } else {
    config.setShrink(false)
  }
})
const onAdaptiveLayout = () => {

  const clientWidth = document.body.clientWidth
  if (clientWidth < 1024) {
    /**
         * 锁定窗口改变自动调整 menuCollapse
         * 避免已是小窗且打开了菜单栏时，意外的自动关闭菜单栏
         */
    if (!state.autoMenuCollapseLock) {
      state.autoMenuCollapseLock = true
      config.setMenuCollapse(true)
    }
    config.setShrink(true)
  } else {
    state.autoMenuCollapseLock = false
    config.setMenuCollapse(false)

    config.setShrink(false)
  }
}
</script>
<style>
/* 全局reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.el-menu .el-sub-menu__title:hover {
  background-color: unset;
}
</style>
