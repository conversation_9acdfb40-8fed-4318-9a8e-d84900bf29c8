import request from '@/utils/axios'
import type { ResPage } from '@/apis/interface'
import type { User } from '@/apis/interface/userManage'
import { ContentTypeEnum } from '@/enums/axios'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

/**
 * @name 用户管理模块
 */
// * 获取用户列表
export const getUserList = (params: User.ReqUserParams) => {
  return request.post<ResPage<User.ResUserList>>('/admin/user/page', params, jsonHeaderConfig)
}

// * 获取树形部门列表
export const getDepartmentTree = (data = undefined) => {
  return request.post<User.ResDepartment[]>('/admin/department/tree', data)
}

// * 新增用户
export const addUser = (params: User.ReqAddUserParams) => {
  return request.post('/admin/user/add', params, jsonHeaderConfig)
}

// * 修改用户
export const editUser = (params: User.ReqAddUserParams) => {
  return request.post('/admin/user/update', params, jsonHeaderConfig)
}

// * 用户详情
export const getUserDetail = (id:number) => {
  return request.post('/admin/user/detail', { id })
}

// * 根据部门ID查询当前部门主管
export const getManagerByDepartmentId = (params: { departmentId: number, userId: number | undefined }) => {
  return request.request({
    url: '/admin/department/manager',
    params
  })
}

/**
 * 用户启用禁用
 * @param params DISABLED  禁用'|'DRAFT'|'ENABLED 启用
 */
export const updateUser = (params: {id: number, state: 'DISABLED'|'DRAFT'|'ENABLED'}) => {
  return request.post( '/admin/user/enable', params)
}

export const userResign = (params: {id: number, followId: number}) => {
  return request.post('/admin/user/resign', params)
}

// *自动生成账号密码
export const createUserPassword = () => {
  return request.get('/admin/user/createUserPassword', {})
}
