<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>CoDesign Iconfont Demo</title>
  <link rel="stylesheet" href="iconfont.css">
  <link rel="stylesheet" href="demo.css">
  <script src="iconfont.js"></script>
</head>

<body>
  <div class="codesign-demo">
    <div class="codesign-demo__header">
      <h1>CoDesign Iconfont Demo</h1>
      <a class="codesign-demo__header-logo" href="https://codesign.qq.com/" target="_blank"></a>
    </div>
    <div class="codesign-demo__type">
      <div class="codesign-demo__type-block">
        <div class="codesign-demo__type-title">
          Symbol (推荐用法)：
        </div>
        <ul>
          <li>支持多色图标，支持调整颜色大小；</li>
          <li>兼容性支持 IE9+。</li>
        </ul>
      </div>
      <div class="codesign-demo__type-block">
        <div class="codesign-demo__type-title">
          Class：
        </div>
        <ul>
          <li>不支持多色图标，支持按字体方式调整颜色大小；</li>
          <li>需要对图标进行标准化处理，兼容性支持 IE8+。</li>
        </ul>
      </div>
      <div class="codesign-demo__type-block">
        <div class="codesign-demo__type-title">
          Unicode：
        </div>
        <ul>
          <li>不支持多色图标，支持按字体方式调整颜色大小；</li>
          <li>需要对图标进行标准化处理，兼容性支持 IE6+。</li>
        </ul>
      </div>
    </div>

    <div class="codesign-demo__description">
      <p>如引用Unicode或Class字体包，需要对上传的图标进行标准化处理，避免显示异常。</p>
      <ul>
        <li>闭合：图标路径描点要处于封闭状态</li>
        <li>合并：如果有多个图形进行组合，要对图形合并扩展</li>
        <li>轮廓化：要将描边转化为闭合图形，并填充颜色</li>
      </ul>
      <p>详情可见帮助中心：<a href="https://codesign.qq.com/hc/icons/upload/" target="_blank">https://codesign.qq.com/hc/icons/upload/</a></p>
    </div>

    <div class="codesign-demo__nav">
      <ul id="tabs" class="codesign-demo__nav-tabs">
        <li class="codesign-demo__nav-tab active" data-target="symbol"><span>Symbol</span></li>
        <li class="codesign-demo__nav-tab" data-target="iconfont"><span>Class</span></li>
        <li class="codesign-demo__nav-tab" data-target="unicode"><span>Unicode</span></li>
      </ul>

      <a href="https://codesign.qq.com/app/icon/13518/detail" target="_blank" class="codesign-demo__nav-project">查看图标库</a>

    </div>

    <div id="unicode" class="codesign-demo__container codesign-demo__container--unicode">
      <ul class="codesign-demo__icon-lists">
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe001;</span>
          <div class="name">文件夹-开_folder-open</div>
          <div class="code-name">&amp;#xe001</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe002;</span>
          <div class="name">消息</div>
          <div class="code-name">&amp;#xe002</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe003;</span>
          <div class="name">添加轮呼</div>
          <div class="code-name">&amp;#xe003</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe004;</span>
          <div class="name">列表配置</div>
          <div class="code-name">&amp;#xe004</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe005;</span>
          <div class="name">删除</div>
          <div class="code-name">&amp;#xe005</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe006;</span>
          <div class="name">导出</div>
          <div class="code-name">&amp;#xe006</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe007;</span>
          <div class="name">导入</div>
          <div class="code-name">&amp;#xe007</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe04e;</span>
          <div class="name">browse-off</div>
          <div class="code-name">&amp;#xe04e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe04f;</span>
          <div class="name">类型&#x3D;time, active&#x3D;no</div>
          <div class="code-name">&amp;#xe04f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe050;</span>
          <div class="name">类型&#x3D;日期时间, active&#x3D;no</div>
          <div class="code-name">&amp;#xe050</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe051;</span>
          <div class="name">类型&#x3D;部门多选, active&#x3D;no</div>
          <div class="code-name">&amp;#xe051</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe052;</span>
          <div class="name">类型&#x3D;部门单选, active&#x3D;no</div>
          <div class="code-name">&amp;#xe052</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe053;</span>
          <div class="name">类型&#x3D;选择数据, active&#x3D;no</div>
          <div class="code-name">&amp;#xe053</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe054;</span>
          <div class="name">类型&#x3D;自增编号, active&#x3D;no</div>
          <div class="code-name">&amp;#xe054</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe055;</span>
          <div class="name">类型&#x3D;邮箱地址, active&#x3D;no</div>
          <div class="code-name">&amp;#xe055</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe056;</span>
          <div class="name">类型&#x3D;身份证, active&#x3D;no</div>
          <div class="code-name">&amp;#xe056</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe057;</span>
          <div class="name">流水号</div>
          <div class="code-name">&amp;#xe057</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe058;</span>
          <div class="name">类型&#x3D;数字, active&#x3D;no</div>
          <div class="code-name">&amp;#xe058</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe059;</span>
          <div class="name">类型&#x3D;手机, active&#x3D;no</div>
          <div class="code-name">&amp;#xe059</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe05a;</span>
          <div class="name">类型&#x3D;百分比, active&#x3D;no</div>
          <div class="code-name">&amp;#xe05a</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe05b;</span>
          <div class="name">类型&#x3D;金额, active&#x3D;no</div>
          <div class="code-name">&amp;#xe05b</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe05c;</span>
          <div class="name">类型&#x3D;多行文本, active&#x3D;no</div>
          <div class="code-name">&amp;#xe05c</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe05d;</span>
          <div class="name">类型&#x3D;文字描述, active&#x3D;no</div>
          <div class="code-name">&amp;#xe05d</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe05e;</span>
          <div class="name">类型&#x3D;单行文本, active&#x3D;no</div>
          <div class="code-name">&amp;#xe05e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe05f;</span>
          <div class="name">类型&#x3D;滑块, active&#x3D;no</div>
          <div class="code-name">&amp;#xe05f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe060;</span>
          <div class="name">类型&#x3D;开关, active&#x3D;no</div>
          <div class="code-name">&amp;#xe060</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe061;</span>
          <div class="name">类型&#x3D;子表单, active&#x3D;no</div>
          <div class="code-name">&amp;#xe061</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe062;</span>
          <div class="name">类型&#x3D;分割线, active&#x3D;no</div>
          <div class="code-name">&amp;#xe062</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe063;</span>
          <div class="name">类型&#x3D;关联查询, active&#x3D;no</div>
          <div class="code-name">&amp;#xe063</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe064;</span>
          <div class="name">类型&#x3D;文字链接, active&#x3D;no</div>
          <div class="code-name">&amp;#xe064</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe065;</span>
          <div class="name">类型&#x3D;地址详情, active&#x3D;no</div>
          <div class="code-name">&amp;#xe065</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe066;</span>
          <div class="name">类型&#x3D;地址, active&#x3D;no</div>
          <div class="code-name">&amp;#xe066</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe067;</span>
          <div class="name">类型&#x3D;成员多选, active&#x3D;no</div>
          <div class="code-name">&amp;#xe067</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe068;</span>
          <div class="name">类型&#x3D;成员单选, active&#x3D;no</div>
          <div class="code-name">&amp;#xe068</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe069;</span>
          <div class="name">类型&#x3D;占位, active&#x3D;no</div>
          <div class="code-name">&amp;#xe069</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe06a;</span>
          <div class="name">类型&#x3D;图片, active&#x3D;no</div>
          <div class="code-name">&amp;#xe06a</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe06b;</span>
          <div class="name">类型&#x3D;附件, active&#x3D;no</div>
          <div class="code-name">&amp;#xe06b</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe06c;</span>
          <div class="name">类型&#x3D;radio, active&#x3D;no</div>
          <div class="code-name">&amp;#xe06c</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe06d;</span>
          <div class="name">类型&#x3D;复选, active&#x3D;no</div>
          <div class="code-name">&amp;#xe06d</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe06e;</span>
          <div class="name">类型&#x3D;tab, active&#x3D;no</div>
          <div class="code-name">&amp;#xe06e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe06f;</span>
          <div class="name">类型&#x3D;按钮, active&#x3D;no</div>
          <div class="code-name">&amp;#xe06f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe070;</span>
          <div class="name">类型&#x3D;下拉单选, active&#x3D;no</div>
          <div class="code-name">&amp;#xe070</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe071;</span>
          <div class="name">类型&#x3D;下拉复选, active&#x3D;no</div>
          <div class="code-name">&amp;#xe071</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe072;</span>
          <div class="name">类型&#x3D;座机, active&#x3D;no</div>
          <div class="code-name">&amp;#xe072</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe074;</span>
          <div class="name">类型&#x3D;信用代码, active&#x3D;no</div>
          <div class="code-name">&amp;#xe074</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe075;</span>
          <div class="name">复制</div>
          <div class="code-name">&amp;#xe075</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe076;</span>
          <div class="name">配置中心</div>
          <div class="code-name">&amp;#xe076</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe077;</span>
          <div class="name">close</div>
          <div class="code-name">&amp;#xe077</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe078;</span>
          <div class="name">系统管理</div>
          <div class="code-name">&amp;#xe078</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe07a;</span>
          <div class="name">拖动</div>
          <div class="code-name">&amp;#xe07a</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe07b;</span>
          <div class="name">filter</div>
          <div class="code-name">&amp;#xe07b</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe07c;</span>
          <div class="name">添加</div>
          <div class="code-name">&amp;#xe07c</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe07d;</span>
          <div class="name">menu-unfold</div>
          <div class="code-name">&amp;#xe07d</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe07e;</span>
          <div class="name">menu-fold</div>
          <div class="code-name">&amp;#xe07e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe07f;</span>
          <div class="name">撤消下一步</div>
          <div class="code-name">&amp;#xe07f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe080;</span>
          <div class="name">撤消上一步</div>
          <div class="code-name">&amp;#xe080</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe081;</span>
          <div class="name">抄送</div>
          <div class="code-name">&amp;#xe081</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe082;</span>
          <div class="name">条件</div>
          <div class="code-name">&amp;#xe082</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe083;</span>
          <div class="name">流程</div>
          <div class="code-name">&amp;#xe083</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe084;</span>
          <div class="name">删除-fill</div>
          <div class="code-name">&amp;#xe084</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe085;</span>
          <div class="name">公司</div>
          <div class="code-name">&amp;#xe085</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe086;</span>
          <div class="name">Property 1&#x3D;star-filled</div>
          <div class="code-name">&amp;#xe086</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe087;</span>
          <div class="name">Property 1&#x3D;star</div>
          <div class="code-name">&amp;#xe087</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe088;</span>
          <div class="name">document</div>
          <div class="code-name">&amp;#xe088</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe089;</span>
          <div class="name">success-outline</div>
          <div class="code-name">&amp;#xe089</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe08a;</span>
          <div class="name">delete</div>
          <div class="code-name">&amp;#xe08a</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe08b;</span>
          <div class="name">warning-1</div>
          <div class="code-name">&amp;#xe08b</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe08c;</span>
          <div class="name">warning</div>
          <div class="code-name">&amp;#xe08c</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe08d;</span>
          <div class="name">warning-outline</div>
          <div class="code-name">&amp;#xe08d</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe08e;</span>
          <div class="name">d-caret</div>
          <div class="code-name">&amp;#xe08e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe08f;</span>
          <div class="name">refresh</div>
          <div class="code-name">&amp;#xe08f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe090;</span>
          <div class="name">sort</div>
          <div class="code-name">&amp;#xe090</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe092;</span>
          <div class="name">lock-off</div>
          <div class="code-name">&amp;#xe092</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe093;</span>
          <div class="name">lock-on</div>
          <div class="code-name">&amp;#xe093</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe094;</span>
          <div class="name">上传</div>
          <div class="code-name">&amp;#xe094</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe095;</span>
          <div class="name">下载</div>
          <div class="code-name">&amp;#xe095</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe096;</span>
          <div class="name">安卓</div>
          <div class="code-name">&amp;#xe096</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe097;</span>
          <div class="name">苹果</div>
          <div class="code-name">&amp;#xe097</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe098;</span>
          <div class="name">批量操作</div>
          <div class="code-name">&amp;#xe098</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe09a;</span>
          <div class="name">fork</div>
          <div class="code-name">&amp;#xe09a</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe09b;</span>
          <div class="name">移交</div>
          <div class="code-name">&amp;#xe09b</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe09c;</span>
          <div class="name">Frame</div>
          <div class="code-name">&amp;#xe09c</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe09d;</span>
          <div class="name">关闭通知</div>
          <div class="code-name">&amp;#xe09d</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe09e;</span>
          <div class="name">add</div>
          <div class="code-name">&amp;#xe09e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe09f;</span>
          <div class="name">circle-删除</div>
          <div class="code-name">&amp;#xe09f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a0;</span>
          <div class="name">check</div>
          <div class="code-name">&amp;#xe0a0</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a1;</span>
          <div class="name">arrowRight</div>
          <div class="code-name">&amp;#xe0a1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a2;</span>
          <div class="name">arrowLeft</div>
          <div class="code-name">&amp;#xe0a2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a3;</span>
          <div class="name">arrowDown</div>
          <div class="code-name">&amp;#xe0a3</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a4;</span>
          <div class="name">arrowUp</div>
          <div class="code-name">&amp;#xe0a4</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a5;</span>
          <div class="name">同级</div>
          <div class="code-name">&amp;#xe0a5</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a6;</span>
          <div class="name">录音&#x3D;yes</div>
          <div class="code-name">&amp;#xe0a6</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a7;</span>
          <div class="name">录音&#x3D;no</div>
          <div class="code-name">&amp;#xe0a7</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a8;</span>
          <div class="name">类型&#x3D;写跟进</div>
          <div class="code-name">&amp;#xe0a8</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0a9;</span>
          <div class="name">类型&#x3D;已欠费</div>
          <div class="code-name">&amp;#xe0a9</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0aa;</span>
          <div class="name">类型&#x3D;已停止</div>
          <div class="code-name">&amp;#xe0aa</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ab;</span>
          <div class="name">类型&#x3D;离开</div>
          <div class="code-name">&amp;#xe0ab</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ac;</span>
          <div class="name">类型&#x3D;通话中</div>
          <div class="code-name">&amp;#xe0ac</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ad;</span>
          <div class="name">类型&#x3D;空闲</div>
          <div class="code-name">&amp;#xe0ad</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ae;</span>
          <div class="name">类型&#x3D;已就绪</div>
          <div class="code-name">&amp;#xe0ae</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0b3;</span>
          <div class="name">类型&#x3D;设置</div>
          <div class="code-name">&amp;#xe0b3</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0b4;</span>
          <div class="name">hover&#x3D;no</div>
          <div class="code-name">&amp;#xe0b4</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0b5;</span>
          <div class="name">类型&#x3D;呼出</div>
          <div class="code-name">&amp;#xe0b5</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0b6;</span>
          <div class="name">类型&#x3D;呼入</div>
          <div class="code-name">&amp;#xe0b6</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0b7;</span>
          <div class="name">播放</div>
          <div class="code-name">&amp;#xe0b7</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0b8;</span>
          <div class="name">暂停</div>
          <div class="code-name">&amp;#xe0b8</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0b9;</span>
          <div class="name">机构</div>
          <div class="code-name">&amp;#xe0b9</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ba;</span>
          <div class="name">解绑</div>
          <div class="code-name">&amp;#xe0ba</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0bb;</span>
          <div class="name">绑定</div>
          <div class="code-name">&amp;#xe0bb</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0bd;</span>
          <div class="name">Property 1&#x3D;填充</div>
          <div class="code-name">&amp;#xe0bd</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0be;</span>
          <div class="name">审批-2</div>
          <div class="code-name">&amp;#xe0be</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0bf;</span>
          <div class="name">业务</div>
          <div class="code-name">&amp;#xe0bf</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c0;</span>
          <div class="name">子节点</div>
          <div class="code-name">&amp;#xe0c0</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c1;</span>
          <div class="name">同级-1</div>
          <div class="code-name">&amp;#xe0c1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c2;</span>
          <div class="name">抄送-1</div>
          <div class="code-name">&amp;#xe0c2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c3;</span>
          <div class="name">添加-1</div>
          <div class="code-name">&amp;#xe0c3</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c4;</span>
          <div class="name">Property 1&#x3D;启用</div>
          <div class="code-name">&amp;#xe0c4</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c5;</span>
          <div class="name">Property 1&#x3D;禁用</div>
          <div class="code-name">&amp;#xe0c5</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c6;</span>
          <div class="name">类型&#x3D;点呼</div>
          <div class="code-name">&amp;#xe0c6</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c7;</span>
          <div class="name">充值</div>
          <div class="code-name">&amp;#xe0c7</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c8;</span>
          <div class="name">left</div>
          <div class="code-name">&amp;#xe0c8</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0c9;</span>
          <div class="name">right</div>
          <div class="code-name">&amp;#xe0c9</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ca;</span>
          <div class="name">down</div>
          <div class="code-name">&amp;#xe0ca</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0cb;</span>
          <div class="name">up</div>
          <div class="code-name">&amp;#xe0cb</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0cc;</span>
          <div class="name">号码检测</div>
          <div class="code-name">&amp;#xe0cc</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0cd;</span>
          <div class="name">Property 1&#x3D;收起</div>
          <div class="code-name">&amp;#xe0cd</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ce;</span>
          <div class="name">Property 1&#x3D;展开</div>
          <div class="code-name">&amp;#xe0ce</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0cf;</span>
          <div class="name">move</div>
          <div class="code-name">&amp;#xe0cf</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d0;</span>
          <div class="name">设置</div>
          <div class="code-name">&amp;#xe0d0</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d2;</span>
          <div class="name">全屏</div>
          <div class="code-name">&amp;#xe0d2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d3;</span>
          <div class="name">未读</div>
          <div class="code-name">&amp;#xe0d3</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d4;</span>
          <div class="name">已读</div>
          <div class="code-name">&amp;#xe0d4</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d5;</span>
          <div class="name">客户通知</div>
          <div class="code-name">&amp;#xe0d5</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d6;</span>
          <div class="name">help-circle</div>
          <div class="code-name">&amp;#xe0d6</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d7;</span>
          <div class="name">Property 1&#x3D;caret-left</div>
          <div class="code-name">&amp;#xe0d7</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d8;</span>
          <div class="name">Property 1&#x3D;caret-right</div>
          <div class="code-name">&amp;#xe0d8</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0d9;</span>
          <div class="name">Property 1&#x3D;caret-bottom</div>
          <div class="code-name">&amp;#xe0d9</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0da;</span>
          <div class="name">Property 1&#x3D;caret-top</div>
          <div class="code-name">&amp;#xe0da</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0db;</span>
          <div class="name">附件</div>
          <div class="code-name">&amp;#xe0db</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0dc;</span>
          <div class="name">公海</div>
          <div class="code-name">&amp;#xe0dc</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0dd;</span>
          <div class="name">编辑</div>
          <div class="code-name">&amp;#xe0dd</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0de;</span>
          <div class="name">阶段</div>
          <div class="code-name">&amp;#xe0de</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0df;</span>
          <div class="name">轮呼</div>
          <div class="code-name">&amp;#xe0df</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0e0;</span>
          <div class="name">类型&#x3D;挂断</div>
          <div class="code-name">&amp;#xe0e0</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0e1;</span>
          <div class="name">还原</div>
          <div class="code-name">&amp;#xe0e1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0e3;</span>
          <div class="name">领取</div>
          <div class="code-name">&amp;#xe0e3</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0e5;</span>
          <div class="name">回收站</div>
          <div class="code-name">&amp;#xe0e5</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0e6;</span>
          <div class="name">公海-1</div>
          <div class="code-name">&amp;#xe0e6</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0e7;</span>
          <div class="name">轮呼-1</div>
          <div class="code-name">&amp;#xe0e7</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0e8;</span>
          <div class="name">呼叫上一个</div>
          <div class="code-name">&amp;#xe0e8</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0e9;</span>
          <div class="name">通讯</div>
          <div class="code-name">&amp;#xe0e9</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ea;</span>
          <div class="name">运营管理</div>
          <div class="code-name">&amp;#xe0ea</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0eb;</span>
          <div class="name">手动关联</div>
          <div class="code-name">&amp;#xe0eb</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ec;</span>
          <div class="name">子节点-1</div>
          <div class="code-name">&amp;#xe0ec</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ed;</span>
          <div class="name">撤回</div>
          <div class="code-name">&amp;#xe0ed</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ee;</span>
          <div class="name">财务管理</div>
          <div class="code-name">&amp;#xe0ee</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ef;</span>
          <div class="name">数据统计</div>
          <div class="code-name">&amp;#xe0ef</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f0;</span>
          <div class="name">审批节点</div>
          <div class="code-name">&amp;#xe0f0</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f1;</span>
          <div class="name">Oval (Stroke)</div>
          <div class="code-name">&amp;#xe0f1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f2;</span>
          <div class="name">关闭</div>
          <div class="code-name">&amp;#xe0f2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f3;</span>
          <div class="name">minus-circle</div>
          <div class="code-name">&amp;#xe0f3</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f4;</span>
          <div class="name">任务</div>
          <div class="code-name">&amp;#xe0f4</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f5;</span>
          <div class="name">认证</div>
          <div class="code-name">&amp;#xe0f5</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f6;</span>
          <div class="name">Property 1&#x3D;冻结</div>
          <div class="code-name">&amp;#xe0f6</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f7;</span>
          <div class="name">Property 1&#x3D;活跃</div>
          <div class="code-name">&amp;#xe0f7</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f8;</span>
          <div class="name">Property 1&#x3D;预开</div>
          <div class="code-name">&amp;#xe0f8</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0f9;</span>
          <div class="name">问号</div>
          <div class="code-name">&amp;#xe0f9</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0fa;</span>
          <div class="name">多级联动</div>
          <div class="code-name">&amp;#xe0fa</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0fb;</span>
          <div class="name">分栏</div>
          <div class="code-name">&amp;#xe0fb</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0fc;</span>
          <div class="name">签名</div>
          <div class="code-name">&amp;#xe0fc</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0fd;</span>
          <div class="name">阶段-1</div>
          <div class="code-name">&amp;#xe0fd</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0fe;</span>
          <div class="name">创建</div>
          <div class="code-name">&amp;#xe0fe</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe0ff;</span>
          <div class="name">CO_TIME_OUT_UNFOLLOW</div>
          <div class="code-name">&amp;#xe0ff</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe100;</span>
          <div class="name">CO_RECYCLE_CST</div>
          <div class="code-name">&amp;#xe100</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe101;</span>
          <div class="name">CO_TODAY_NEW_CST</div>
          <div class="code-name">&amp;#xe101</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe102;</span>
          <div class="name">CO_CST_AT_HOME</div>
          <div class="code-name">&amp;#xe102</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe103;</span>
          <div class="name">CO_FOLLOWED_CST</div>
          <div class="code-name">&amp;#xe103</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe104;</span>
          <div class="name">CO_ATTENTION_CST</div>
          <div class="code-name">&amp;#xe104</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe105;</span>
          <div class="name">转客户</div>
          <div class="code-name">&amp;#xe105</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe106;</span>
          <div class="name">secured</div>
          <div class="code-name">&amp;#xe106</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe107;</span>
          <div class="name">22</div>
          <div class="code-name">&amp;#xe107</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe108;</span>
          <div class="name">search</div>
          <div class="code-name">&amp;#xe108</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe10a;</span>
          <div class="name">运营工具</div>
          <div class="code-name">&amp;#xe10a</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe10b;</span>
          <div class="name">应用商城</div>
          <div class="code-name">&amp;#xe10b</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe10c;</span>
          <div class="name">新建客户</div>
          <div class="code-name">&amp;#xe10c</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe10d;</span>
          <div class="name">还款试算</div>
          <div class="code-name">&amp;#xe10d</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe10e;</span>
          <div class="name">线下拓客</div>
          <div class="code-name">&amp;#xe10e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe10f;</span>
          <div class="name">新建签单</div>
          <div class="code-name">&amp;#xe10f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe110;</span>
          <div class="name">预约上门</div>
          <div class="code-name">&amp;#xe110</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe111;</span>
          <div class="name">场景工具</div>
          <div class="code-name">&amp;#xe111</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe112;</span>
          <div class="name">客户概览</div>
          <div class="code-name">&amp;#xe112</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe113;</span>
          <div class="name">排行榜</div>
          <div class="code-name">&amp;#xe113</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe114;</span>
          <div class="name">任务中心</div>
          <div class="code-name">&amp;#xe114</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe116;</span>
          <div class="name">业务数据</div>
          <div class="code-name">&amp;#xe116</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe117;</span>
          <div class="name">Frame 9266</div>
          <div class="code-name">&amp;#xe117</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe118;</span>
          <div class="name">日程通知</div>
          <div class="code-name">&amp;#xe118</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe119;</span>
          <div class="name">业务-1</div>
          <div class="code-name">&amp;#xe119</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe11a;</span>
          <div class="name">审批-1</div>
          <div class="code-name">&amp;#xe11a</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe11c;</span>
          <div class="name">业务-2</div>
          <div class="code-name">&amp;#xe11c</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe11d;</span>
          <div class="name">日程-已读</div>
          <div class="code-name">&amp;#xe11d</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe11e;</span>
          <div class="name">审批-已读</div>
          <div class="code-name">&amp;#xe11e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe11f;</span>
          <div class="name">业务-已读</div>
          <div class="code-name">&amp;#xe11f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe120;</span>
          <div class="name">日程-1</div>
          <div class="code-name">&amp;#xe120</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe121;</span>
          <div class="name">审批</div>
          <div class="code-name">&amp;#xe121</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe122;</span>
          <div class="name">横向收起</div>
          <div class="code-name">&amp;#xe122</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe123;</span>
          <div class="name">横向展开</div>
          <div class="code-name">&amp;#xe123</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe124;</span>
          <div class="name">上升</div>
          <div class="code-name">&amp;#xe124</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe125;</span>
          <div class="name">下降</div>
          <div class="code-name">&amp;#xe125</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe126;</span>
          <div class="name">营销管理</div>
          <div class="code-name">&amp;#xe126</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe127;</span>
          <div class="name">browse</div>
          <div class="code-name">&amp;#xe127</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe128;</span>
          <div class="name">处理中</div>
          <div class="code-name">&amp;#xe128</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe129;</span>
          <div class="name">失败</div>
          <div class="code-name">&amp;#xe129</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe12a;</span>
          <div class="name">数据管理</div>
          <div class="code-name">&amp;#xe12a</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe12b;</span>
          <div class="name">产品管理</div>
          <div class="code-name">&amp;#xe12b</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe12c;</span>
          <div class="name">渠道管理</div>
          <div class="code-name">&amp;#xe12c</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe12d;</span>
          <div class="name">营销数据</div>
          <div class="code-name">&amp;#xe12d</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe12e;</span>
          <div class="name">评论</div>
          <div class="code-name">&amp;#xe12e</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe12f;</span>
          <div class="name">转账还款</div>
          <div class="code-name">&amp;#xe12f</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe134;</span>
          <div class="name">文件</div>
          <div class="code-name">&amp;#xe134</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe135;</span>
          <div class="name">发起签约</div>
          <div class="code-name">&amp;#xe135</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe136;</span>
          <div class="name">列表标记</div>
          <div class="code-name">&amp;#xe136</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe137;</span>
          <div class="name">自定义列表颜色</div>
          <div class="code-name">&amp;#xe137</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 unicode">
          <span class="icon iconfont">&#xe138;</span>
          <div class="name">公证中心</div>
          <div class="code-name">&amp;#xe138</div>
        </li>
      </ul>
      <div class="codesign-demo__usage markdown">
        <h2>Unicode 引用</h2>
        <h3>1. 添加 <code>@font-face</code> 块, 引用图标字体文件；</h3>
        <pre><code>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
  url('iconfont.woff2') format('woff2'),
  url('iconfont.woff') format('woff'),
  url('iconfont.ttf') format('truetype'),
  url('iconfont.svg#iconfont') format('svg');
}
        </code></pre>
        <h3>2. 设置 <code>.iconfont</code> 的基本样式；</h3>
        <pre><code>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
        </code></pre>
        <h3>3. 应用图标，输入对应的 Unicode 值。</h3>
        <pre><code>&lt;span class="iconfont"&gt;&amp;#xe624;&lt;/span&gt;</code></pre>
        <blockquote>
          <p>
            "iconfont" 是图标库下的 “iconfont family” 字段，可以通过编辑图标库查看，默认是 "iconfont"。
          </p>
        </blockquote>
      </div>
    </div>

    <div id="iconfont" class="codesign-demo__container codesign-demo__container--iconfont">
      <ul class="codesign-demo__icon-lists">
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-wenjianga-kai_folder-open"></span>
          <div class="name">
            文件夹-开_folder-open
          </div>
          <div class="code-name" title=".icon-wenjianga-kai_folder-open">
            .icon-wenjianga-kai_folder-open</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-xiaoxi"></span>
          <div class="name">
            消息
          </div>
          <div class="code-name" title=".icon-xiaoxi">
            .icon-xiaoxi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-tianjialunxu"></span>
          <div class="name">
            添加轮呼
          </div>
          <div class="code-name" title=".icon-tianjialunxu">
            .icon-tianjialunxu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-libiaopeizhi"></span>
          <div class="name">
            列表配置
          </div>
          <div class="code-name" title=".icon-libiaopeizhi">
            .icon-libiaopeizhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shanshu"></span>
          <div class="name">
            删除
          </div>
          <div class="code-name" title=".icon-shanshu">
            .icon-shanshu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-daochu"></span>
          <div class="name">
            导出
          </div>
          <div class="code-name" title=".icon-daochu">
            .icon-daochu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-daoru"></span>
          <div class="name">
            导入
          </div>
          <div class="code-name" title=".icon-daoru">
            .icon-daoru</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-browse-off"></span>
          <div class="name">
            browse-off
          </div>
          <div class="code-name" title=".icon-browse-off">
            .icon-browse-off</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingtimeactiveno"></span>
          <div class="name">
            类型&#x3D;time, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingtimeactiveno">
            .icon-leixingtimeactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingrijishijianactiveno"></span>
          <div class="name">
            类型&#x3D;日期时间, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingrijishijianactiveno">
            .icon-leixingrijishijianactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingbumenduoxuanactiveno"></span>
          <div class="name">
            类型&#x3D;部门多选, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingbumenduoxuanactiveno">
            .icon-leixingbumenduoxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingbumenshanxuanactiveno"></span>
          <div class="name">
            类型&#x3D;部门单选, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingbumenshanxuanactiveno">
            .icon-leixingbumenshanxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingxuanzhaishujuactiveno"></span>
          <div class="name">
            类型&#x3D;选择数据, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingxuanzhaishujuactiveno">
            .icon-leixingxuanzhaishujuactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingzicengbianxiaoactiveno"></span>
          <div class="name">
            类型&#x3D;自增编号, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingzicengbianxiaoactiveno">
            .icon-leixingzicengbianxiaoactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingyouxiangdezhiactiveno"></span>
          <div class="name">
            类型&#x3D;邮箱地址, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingyouxiangdezhiactiveno">
            .icon-leixingyouxiangdezhiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingshenfenzhengactiveno"></span>
          <div class="name">
            类型&#x3D;身份证, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingshenfenzhengactiveno">
            .icon-leixingshenfenzhengactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingyouzhengbianmaactiveno"></span>
          <div class="name">
            流水号
          </div>
          <div class="code-name" title=".icon-leixingyouzhengbianmaactiveno">
            .icon-leixingyouzhengbianmaactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingshuziactiveno"></span>
          <div class="name">
            类型&#x3D;数字, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingshuziactiveno">
            .icon-leixingshuziactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingshoujiactiveno"></span>
          <div class="name">
            类型&#x3D;手机, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingshoujiactiveno">
            .icon-leixingshoujiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingmofenbiactiveno"></span>
          <div class="name">
            类型&#x3D;百分比, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingmofenbiactiveno">
            .icon-leixingmofenbiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingjineactiveno"></span>
          <div class="name">
            类型&#x3D;金额, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingjineactiveno">
            .icon-leixingjineactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingduoxingwenbenactiveno"></span>
          <div class="name">
            类型&#x3D;多行文本, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingduoxingwenbenactiveno">
            .icon-leixingduoxingwenbenactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingwenzimaoshuactiveno"></span>
          <div class="name">
            类型&#x3D;文字描述, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingwenzimaoshuactiveno">
            .icon-leixingwenzimaoshuactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingshanxingwenbenactiveno"></span>
          <div class="name">
            类型&#x3D;单行文本, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingshanxingwenbenactiveno">
            .icon-leixingshanxingwenbenactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixinghuakuaiactiveno"></span>
          <div class="name">
            类型&#x3D;滑块, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixinghuakuaiactiveno">
            .icon-leixinghuakuaiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingkaiguanactiveno"></span>
          <div class="name">
            类型&#x3D;开关, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingkaiguanactiveno">
            .icon-leixingkaiguanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingzibiaoshanactiveno"></span>
          <div class="name">
            类型&#x3D;子表单, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingzibiaoshanactiveno">
            .icon-leixingzibiaoshanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingfengexianactiveno"></span>
          <div class="name">
            类型&#x3D;分割线, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingfengexianactiveno">
            .icon-leixingfengexianactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingguanlianzhaxunactiveno"></span>
          <div class="name">
            类型&#x3D;关联查询, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingguanlianzhaxunactiveno">
            .icon-leixingguanlianzhaxunactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingwenzilianxieactiveno"></span>
          <div class="name">
            类型&#x3D;文字链接, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingwenzilianxieactiveno">
            .icon-leixingwenzilianxieactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingdezhixiangqingactiveno"></span>
          <div class="name">
            类型&#x3D;地址详情, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingdezhixiangqingactiveno">
            .icon-leixingdezhixiangqingactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingdezhiactiveno"></span>
          <div class="name">
            类型&#x3D;地址, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingdezhiactiveno">
            .icon-leixingdezhiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingchengyunduoxuanactiveno"></span>
          <div class="name">
            类型&#x3D;成员多选, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingchengyunduoxuanactiveno">
            .icon-leixingchengyunduoxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingchengyunshanxuanactiveno"></span>
          <div class="name">
            类型&#x3D;成员单选, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingchengyunshanxuanactiveno">
            .icon-leixingchengyunshanxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingtieweiactiveno"></span>
          <div class="name">
            类型&#x3D;占位, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingtieweiactiveno">
            .icon-leixingtieweiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingtupianactiveno"></span>
          <div class="name">
            类型&#x3D;图片, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingtupianactiveno">
            .icon-leixingtupianactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingbujianactiveno"></span>
          <div class="name">
            类型&#x3D;附件, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingbujianactiveno">
            .icon-leixingbujianactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingradioactiveno"></span>
          <div class="name">
            类型&#x3D;radio, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingradioactiveno">
            .icon-leixingradioactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingfuxuanactiveno"></span>
          <div class="name">
            类型&#x3D;复选, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingfuxuanactiveno">
            .icon-leixingfuxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingtabactiveno"></span>
          <div class="name">
            类型&#x3D;tab, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingtabactiveno">
            .icon-leixingtabactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixinganniuactiveno"></span>
          <div class="name">
            类型&#x3D;按钮, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixinganniuactiveno">
            .icon-leixinganniuactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingxialashanxuanactiveno"></span>
          <div class="name">
            类型&#x3D;下拉单选, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingxialashanxuanactiveno">
            .icon-leixingxialashanxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingxialafuxuanactiveno"></span>
          <div class="name">
            类型&#x3D;下拉复选, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingxialafuxuanactiveno">
            .icon-leixingxialafuxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingzuojiactiveno"></span>
          <div class="name">
            类型&#x3D;座机, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingzuojiactiveno">
            .icon-leixingzuojiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingxinyongdaimaactiveno"></span>
          <div class="name">
            类型&#x3D;信用代码, active&#x3D;no
          </div>
          <div class="code-name" title=".icon-leixingxinyongdaimaactiveno">
            .icon-leixingxinyongdaimaactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-fuzhi"></span>
          <div class="name">
            复制
          </div>
          <div class="code-name" title=".icon-fuzhi">
            .icon-fuzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-peizhizhongxin"></span>
          <div class="name">
            配置中心
          </div>
          <div class="code-name" title=".icon-peizhizhongxin">
            .icon-peizhizhongxin</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-close"></span>
          <div class="name">
            close
          </div>
          <div class="code-name" title=".icon-close">
            .icon-close</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-jitongguanli"></span>
          <div class="name">
            系统管理
          </div>
          <div class="code-name" title=".icon-jitongguanli">
            .icon-jitongguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-tuodong"></span>
          <div class="name">
            拖动
          </div>
          <div class="code-name" title=".icon-tuodong">
            .icon-tuodong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-filter"></span>
          <div class="name">
            filter
          </div>
          <div class="code-name" title=".icon-filter">
            .icon-filter</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-tianjia"></span>
          <div class="name">
            添加
          </div>
          <div class="code-name" title=".icon-tianjia">
            .icon-tianjia</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-menu-unfold"></span>
          <div class="name">
            menu-unfold
          </div>
          <div class="code-name" title=".icon-menu-unfold">
            .icon-menu-unfold</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-menu-fold"></span>
          <div class="name">
            menu-fold
          </div>
          <div class="code-name" title=".icon-menu-fold">
            .icon-menu-fold</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame-4"></span>
          <div class="name">
            撤消下一步
          </div>
          <div class="code-name" title=".icon-Frame-4">
            .icon-Frame-4</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame-3"></span>
          <div class="name">
            撤消上一步
          </div>
          <div class="code-name" title=".icon-Frame-3">
            .icon-Frame-3</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame-2"></span>
          <div class="name">
            抄送
          </div>
          <div class="code-name" title=".icon-Frame-2">
            .icon-Frame-2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame-1"></span>
          <div class="name">
            条件
          </div>
          <div class="code-name" title=".icon-Frame-1">
            .icon-Frame-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame"></span>
          <div class="name">
            流程
          </div>
          <div class="code-name" title=".icon-Frame">
            .icon-Frame</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shanshu-1"></span>
          <div class="name">
            删除-fill
          </div>
          <div class="code-name" title=".icon-shanshu-1">
            .icon-shanshu-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame-5"></span>
          <div class="name">
            公司
          </div>
          <div class="code-name" title=".icon-Frame-5">
            .icon-Frame-5</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1star-filled"></span>
          <div class="name">
            Property 1&#x3D;star-filled
          </div>
          <div class="code-name" title=".icon-Property1star-filled">
            .icon-Property1star-filled</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1star"></span>
          <div class="name">
            Property 1&#x3D;star
          </div>
          <div class="code-name" title=".icon-Property1star">
            .icon-Property1star</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-document"></span>
          <div class="name">
            document
          </div>
          <div class="code-name" title=".icon-document">
            .icon-document</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-success-outline"></span>
          <div class="name">
            success-outline
          </div>
          <div class="code-name" title=".icon-success-outline">
            .icon-success-outline</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-delete"></span>
          <div class="name">
            delete
          </div>
          <div class="code-name" title=".icon-delete">
            .icon-delete</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-warning-1"></span>
          <div class="name">
            warning-1
          </div>
          <div class="code-name" title=".icon-warning-1">
            .icon-warning-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-warning"></span>
          <div class="name">
            warning
          </div>
          <div class="code-name" title=".icon-warning">
            .icon-warning</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-warning-outline"></span>
          <div class="name">
            warning-outline
          </div>
          <div class="code-name" title=".icon-warning-outline">
            .icon-warning-outline</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-d-caret"></span>
          <div class="name">
            d-caret
          </div>
          <div class="code-name" title=".icon-d-caret">
            .icon-d-caret</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-refresh"></span>
          <div class="name">
            refresh
          </div>
          <div class="code-name" title=".icon-refresh">
            .icon-refresh</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-sort"></span>
          <div class="name">
            sort
          </div>
          <div class="code-name" title=".icon-sort">
            .icon-sort</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-lock-off"></span>
          <div class="name">
            lock-off
          </div>
          <div class="code-name" title=".icon-lock-off">
            .icon-lock-off</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-lock-on"></span>
          <div class="name">
            lock-on
          </div>
          <div class="code-name" title=".icon-lock-on">
            .icon-lock-on</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shangzhuan"></span>
          <div class="name">
            上传
          </div>
          <div class="code-name" title=".icon-shangzhuan">
            .icon-shangzhuan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-xiazai"></span>
          <div class="name">
            下载
          </div>
          <div class="code-name" title=".icon-xiazai">
            .icon-xiazai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-anzhuo"></span>
          <div class="name">
            安卓
          </div>
          <div class="code-name" title=".icon-anzhuo">
            .icon-anzhuo</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-pengluo"></span>
          <div class="name">
            苹果
          </div>
          <div class="code-name" title=".icon-pengluo">
            .icon-pengluo</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-piliangcaozuo"></span>
          <div class="name">
            批量操作
          </div>
          <div class="code-name" title=".icon-piliangcaozuo">
            .icon-piliangcaozuo</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-fork"></span>
          <div class="name">
            fork
          </div>
          <div class="code-name" title=".icon-fork">
            .icon-fork</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yijiao"></span>
          <div class="name">
            移交
          </div>
          <div class="code-name" title=".icon-yijiao">
            .icon-yijiao</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame-6"></span>
          <div class="name">
            Frame
          </div>
          <div class="code-name" title=".icon-Frame-6">
            .icon-Frame-6</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-guanbitongzhi"></span>
          <div class="name">
            关闭通知
          </div>
          <div class="code-name" title=".icon-guanbitongzhi">
            .icon-guanbitongzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-add"></span>
          <div class="name">
            add
          </div>
          <div class="code-name" title=".icon-add">
            .icon-add</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-circle-close1"></span>
          <div class="name">
            circle-删除
          </div>
          <div class="code-name" title=".icon-circle-close1">
            .icon-circle-close1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-check"></span>
          <div class="name">
            check
          </div>
          <div class="code-name" title=".icon-check">
            .icon-check</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-arrowRight"></span>
          <div class="name">
            arrowRight
          </div>
          <div class="code-name" title=".icon-arrowRight">
            .icon-arrowRight</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-arrowLeft"></span>
          <div class="name">
            arrowLeft
          </div>
          <div class="code-name" title=".icon-arrowLeft">
            .icon-arrowLeft</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-arrowDown"></span>
          <div class="name">
            arrowDown
          </div>
          <div class="code-name" title=".icon-arrowDown">
            .icon-arrowDown</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-arrowUp"></span>
          <div class="name">
            arrowUp
          </div>
          <div class="code-name" title=".icon-arrowUp">
            .icon-arrowUp</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-tongji"></span>
          <div class="name">
            同级
          </div>
          <div class="code-name" title=".icon-tongji">
            .icon-tongji</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-unmute"></span>
          <div class="name">
            录音&#x3D;yes
          </div>
          <div class="code-name" title=".icon-unmute">
            .icon-unmute</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-mute"></span>
          <div class="name">
            录音&#x3D;no
          </div>
          <div class="code-name" title=".icon-mute">
            .icon-mute</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CLEANUP"></span>
          <div class="name">
            类型&#x3D;写跟进
          </div>
          <div class="code-name" title=".icon-CLEANUP">
            .icon-CLEANUP</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-EXPIRED"></span>
          <div class="name">
            类型&#x3D;已欠费
          </div>
          <div class="code-name" title=".icon-EXPIRED">
            .icon-EXPIRED</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-STOP"></span>
          <div class="name">
            类型&#x3D;已停止
          </div>
          <div class="code-name" title=".icon-STOP">
            .icon-STOP</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-LEAVE"></span>
          <div class="name">
            类型&#x3D;离开
          </div>
          <div class="code-name" title=".icon-LEAVE">
            .icon-LEAVE</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CALLING"></span>
          <div class="name">
            类型&#x3D;通话中
          </div>
          <div class="code-name" title=".icon-CALLING">
            .icon-CALLING</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-FREE"></span>
          <div class="name">
            类型&#x3D;空闲
          </div>
          <div class="code-name" title=".icon-FREE">
            .icon-FREE</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-READY"></span>
          <div class="name">
            类型&#x3D;已就绪
          </div>
          <div class="code-name" title=".icon-READY">
            .icon-READY</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-setting"></span>
          <div class="name">
            类型&#x3D;设置
          </div>
          <div class="code-name" title=".icon-setting">
            .icon-setting</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-hoverno"></span>
          <div class="name">
            hover&#x3D;no
          </div>
          <div class="code-name" title=".icon-hoverno">
            .icon-hoverno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CORG"></span>
          <div class="name">
            类型&#x3D;呼出
          </div>
          <div class="code-name" title=".icon-CORG">
            .icon-CORG</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingxuru"></span>
          <div class="name">
            类型&#x3D;呼入
          </div>
          <div class="code-name" title=".icon-leixingxuru">
            .icon-leixingxuru</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-play"></span>
          <div class="name">
            播放
          </div>
          <div class="code-name" title=".icon-play">
            .icon-play</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-pause"></span>
          <div class="name">
            暂停
          </div>
          <div class="code-name" title=".icon-pause">
            .icon-pause</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-jigou"></span>
          <div class="name">
            机构
          </div>
          <div class="code-name" title=".icon-jigou">
            .icon-jigou</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-jiebang"></span>
          <div class="name">
            解绑
          </div>
          <div class="code-name" title=".icon-jiebang">
            .icon-jiebang</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-bangding"></span>
          <div class="name">
            绑定
          </div>
          <div class="code-name" title=".icon-bangding">
            .icon-bangding</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1chenchong"></span>
          <div class="name">
            Property 1&#x3D;填充
          </div>
          <div class="code-name" title=".icon-Property1chenchong">
            .icon-Property1chenchong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shenpi-2"></span>
          <div class="name">
            审批-2
          </div>
          <div class="code-name" title=".icon-shenpi-2">
            .icon-shenpi-2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yewu"></span>
          <div class="name">
            业务
          </div>
          <div class="code-name" title=".icon-yewu">
            .icon-yewu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-zijiedian"></span>
          <div class="name">
            子节点
          </div>
          <div class="code-name" title=".icon-zijiedian">
            .icon-zijiedian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-tongji-1"></span>
          <div class="name">
            同级-1
          </div>
          <div class="code-name" title=".icon-tongji-1">
            .icon-tongji-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame-2-1"></span>
          <div class="name">
            抄送-1
          </div>
          <div class="code-name" title=".icon-Frame-2-1">
            .icon-Frame-2-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-tianjia-1"></span>
          <div class="name">
            添加-1
          </div>
          <div class="code-name" title=".icon-tianjia-1">
            .icon-tianjia-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-qiyong"></span>
          <div class="name">
            Property 1&#x3D;启用
          </div>
          <div class="code-name" title=".icon-qiyong">
            .icon-qiyong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-jinyong"></span>
          <div class="name">
            Property 1&#x3D;禁用
          </div>
          <div class="code-name" title=".icon-jinyong">
            .icon-jinyong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-leixingdianxu"></span>
          <div class="name">
            类型&#x3D;点呼
          </div>
          <div class="code-name" title=".icon-leixingdianxu">
            .icon-leixingdianxu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-chongzhi"></span>
          <div class="name">
            充值
          </div>
          <div class="code-name" title=".icon-chongzhi">
            .icon-chongzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-left"></span>
          <div class="name">
            left
          </div>
          <div class="code-name" title=".icon-left">
            .icon-left</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-right"></span>
          <div class="name">
            right
          </div>
          <div class="code-name" title=".icon-right">
            .icon-right</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-down"></span>
          <div class="name">
            down
          </div>
          <div class="code-name" title=".icon-down">
            .icon-down</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-up"></span>
          <div class="name">
            up
          </div>
          <div class="code-name" title=".icon-up">
            .icon-up</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame-6-1"></span>
          <div class="name">
            号码检测
          </div>
          <div class="code-name" title=".icon-Frame-6-1">
            .icon-Frame-6-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1shouqi"></span>
          <div class="name">
            Property 1&#x3D;收起
          </div>
          <div class="code-name" title=".icon-Property1shouqi">
            .icon-Property1shouqi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1zhankai"></span>
          <div class="name">
            Property 1&#x3D;展开
          </div>
          <div class="code-name" title=".icon-Property1zhankai">
            .icon-Property1zhankai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-move"></span>
          <div class="name">
            move
          </div>
          <div class="code-name" title=".icon-move">
            .icon-move</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shezhi"></span>
          <div class="name">
            设置
          </div>
          <div class="code-name" title=".icon-shezhi">
            .icon-shezhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-quanping"></span>
          <div class="name">
            全屏
          </div>
          <div class="code-name" title=".icon-quanping">
            .icon-quanping</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-weidu"></span>
          <div class="name">
            未读
          </div>
          <div class="code-name" title=".icon-weidu">
            .icon-weidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-sidu"></span>
          <div class="name">
            已读
          </div>
          <div class="code-name" title=".icon-sidu">
            .icon-sidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-qiahutongzhi"></span>
          <div class="name">
            客户通知
          </div>
          <div class="code-name" title=".icon-qiahutongzhi">
            .icon-qiahutongzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-help-circle"></span>
          <div class="name">
            help-circle
          </div>
          <div class="code-name" title=".icon-help-circle">
            .icon-help-circle</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1caret-left"></span>
          <div class="name">
            Property 1&#x3D;caret-left
          </div>
          <div class="code-name" title=".icon-Property1caret-left">
            .icon-Property1caret-left</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1caret-right"></span>
          <div class="name">
            Property 1&#x3D;caret-right
          </div>
          <div class="code-name" title=".icon-Property1caret-right">
            .icon-Property1caret-right</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1caret-bottom"></span>
          <div class="name">
            Property 1&#x3D;caret-bottom
          </div>
          <div class="code-name" title=".icon-Property1caret-bottom">
            .icon-Property1caret-bottom</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1caret-top"></span>
          <div class="name">
            Property 1&#x3D;caret-top
          </div>
          <div class="code-name" title=".icon-Property1caret-top">
            .icon-Property1caret-top</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-bujian"></span>
          <div class="name">
            附件
          </div>
          <div class="code-name" title=".icon-bujian">
            .icon-bujian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-gonghai"></span>
          <div class="name">
            公海
          </div>
          <div class="code-name" title=".icon-gonghai">
            .icon-gonghai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-bianji"></span>
          <div class="name">
            编辑
          </div>
          <div class="code-name" title=".icon-bianji">
            .icon-bianji</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-jieduan"></span>
          <div class="name">
            阶段
          </div>
          <div class="code-name" title=".icon-jieduan">
            .icon-jieduan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-lunxu"></span>
          <div class="name">
            轮呼
          </div>
          <div class="code-name" title=".icon-lunxu">
            .icon-lunxu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-hangup"></span>
          <div class="name">
            类型&#x3D;挂断
          </div>
          <div class="code-name" title=".icon-hangup">
            .icon-hangup</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-restore"></span>
          <div class="name">
            还原
          </div>
          <div class="code-name" title=".icon-restore">
            .icon-restore</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-receive"></span>
          <div class="name">
            领取
          </div>
          <div class="code-name" title=".icon-receive">
            .icon-receive</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-huishouzhan"></span>
          <div class="name">
            回收站
          </div>
          <div class="code-name" title=".icon-huishouzhan">
            .icon-huishouzhan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-gonghai-1"></span>
          <div class="name">
            公海-1
          </div>
          <div class="code-name" title=".icon-gonghai-1">
            .icon-gonghai-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-call-round"></span>
          <div class="name">
            轮呼-1
          </div>
          <div class="code-name" title=".icon-call-round">
            .icon-call-round</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-recall"></span>
          <div class="name">
            呼叫上一个
          </div>
          <div class="code-name" title=".icon-recall">
            .icon-recall</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-tongxun"></span>
          <div class="name">
            通讯
          </div>
          <div class="code-name" title=".icon-tongxun">
            .icon-tongxun</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yunyingguanli"></span>
          <div class="name">
            运营管理
          </div>
          <div class="code-name" title=".icon-yunyingguanli">
            .icon-yunyingguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shoudongguanlian"></span>
          <div class="name">
            手动关联
          </div>
          <div class="code-name" title=".icon-shoudongguanlian">
            .icon-shoudongguanlian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-zijiedian-1"></span>
          <div class="name">
            子节点-1
          </div>
          <div class="code-name" title=".icon-zijiedian-1">
            .icon-zijiedian-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-chehui"></span>
          <div class="name">
            撤回
          </div>
          <div class="code-name" title=".icon-chehui">
            .icon-chehui</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-caiwuguanli"></span>
          <div class="name">
            财务管理
          </div>
          <div class="code-name" title=".icon-caiwuguanli">
            .icon-caiwuguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shujutongji"></span>
          <div class="name">
            数据统计
          </div>
          <div class="code-name" title=".icon-shujutongji">
            .icon-shujutongji</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shenpijiedian"></span>
          <div class="name">
            审批节点
          </div>
          <div class="code-name" title=".icon-shenpijiedian">
            .icon-shenpijiedian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-OvalStroke"></span>
          <div class="name">
            Oval (Stroke)
          </div>
          <div class="code-name" title=".icon-OvalStroke">
            .icon-OvalStroke</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-guanbi"></span>
          <div class="name">
            关闭
          </div>
          <div class="code-name" title=".icon-guanbi">
            .icon-guanbi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-minus-circle"></span>
          <div class="name">
            minus-circle
          </div>
          <div class="code-name" title=".icon-minus-circle">
            .icon-minus-circle</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-linwu"></span>
          <div class="name">
            任务
          </div>
          <div class="code-name" title=".icon-linwu">
            .icon-linwu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-renzheng"></span>
          <div class="name">
            认证
          </div>
          <div class="code-name" title=".icon-renzheng">
            .icon-renzheng</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1dongjie"></span>
          <div class="name">
            Property 1&#x3D;冻结
          </div>
          <div class="code-name" title=".icon-Property1dongjie">
            .icon-Property1dongjie</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1huoyue"></span>
          <div class="name">
            Property 1&#x3D;活跃
          </div>
          <div class="code-name" title=".icon-Property1huoyue">
            .icon-Property1huoyue</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Property1yukai"></span>
          <div class="name">
            Property 1&#x3D;预开
          </div>
          <div class="code-name" title=".icon-Property1yukai">
            .icon-Property1yukai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-wenxiao"></span>
          <div class="name">
            问号
          </div>
          <div class="code-name" title=".icon-wenxiao">
            .icon-wenxiao</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-duojiliandong"></span>
          <div class="name">
            多级联动
          </div>
          <div class="code-name" title=".icon-duojiliandong">
            .icon-duojiliandong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-fenlan"></span>
          <div class="name">
            分栏
          </div>
          <div class="code-name" title=".icon-fenlan">
            .icon-fenlan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-qianming"></span>
          <div class="name">
            签名
          </div>
          <div class="code-name" title=".icon-qianming">
            .icon-qianming</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-jieduan-1"></span>
          <div class="name">
            阶段-1
          </div>
          <div class="code-name" title=".icon-jieduan-1">
            .icon-jieduan-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-chuangjian"></span>
          <div class="name">
            创建
          </div>
          <div class="code-name" title=".icon-chuangjian">
            .icon-chuangjian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CO_TIME_OUT_UNFOLLOW"></span>
          <div class="name">
            CO_TIME_OUT_UNFOLLOW
          </div>
          <div class="code-name" title=".icon-CO_TIME_OUT_UNFOLLOW">
            .icon-CO_TIME_OUT_UNFOLLOW</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CO_RECYCLE_CST"></span>
          <div class="name">
            CO_RECYCLE_CST
          </div>
          <div class="code-name" title=".icon-CO_RECYCLE_CST">
            .icon-CO_RECYCLE_CST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CO_TODAY_NEW_CST"></span>
          <div class="name">
            CO_TODAY_NEW_CST
          </div>
          <div class="code-name" title=".icon-CO_TODAY_NEW_CST">
            .icon-CO_TODAY_NEW_CST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CO_CST_AT_HOME"></span>
          <div class="name">
            CO_CST_AT_HOME
          </div>
          <div class="code-name" title=".icon-CO_CST_AT_HOME">
            .icon-CO_CST_AT_HOME</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CO_FOLLOWED_CST"></span>
          <div class="name">
            CO_FOLLOWED_CST
          </div>
          <div class="code-name" title=".icon-CO_FOLLOWED_CST">
            .icon-CO_FOLLOWED_CST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CO_ATTENTION_CST"></span>
          <div class="name">
            CO_ATTENTION_CST
          </div>
          <div class="code-name" title=".icon-CO_ATTENTION_CST">
            .icon-CO_ATTENTION_CST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-zhuanqiahu"></span>
          <div class="name">
            转客户
          </div>
          <div class="code-name" title=".icon-zhuanqiahu">
            .icon-zhuanqiahu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-secured"></span>
          <div class="name">
            secured
          </div>
          <div class="code-name" title=".icon-secured">
            .icon-secured</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-22"></span>
          <div class="name">
            22
          </div>
          <div class="code-name" title=".icon-22">
            .icon-22</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-search"></span>
          <div class="name">
            search
          </div>
          <div class="code-name" title=".icon-search">
            .icon-search</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yunyinggongju"></span>
          <div class="name">
            运营工具
          </div>
          <div class="code-name" title=".icon-yunyinggongju">
            .icon-yunyinggongju</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yingyongshangcheng"></span>
          <div class="name">
            应用商城
          </div>
          <div class="code-name" title=".icon-yingyongshangcheng">
            .icon-yingyongshangcheng</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-ST_NEW_ST"></span>
          <div class="name">
            新建客户
          </div>
          <div class="code-name" title=".icon-ST_NEW_ST">
            .icon-ST_NEW_ST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-ST_REPAYMENT"></span>
          <div class="name">
            还款试算
          </div>
          <div class="code-name" title=".icon-ST_REPAYMENT">
            .icon-ST_REPAYMENT</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-xianxiatuoqia"></span>
          <div class="name">
            线下拓客
          </div>
          <div class="code-name" title=".icon-xianxiatuoqia">
            .icon-xianxiatuoqia</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-xinjianqianshan"></span>
          <div class="name">
            新建签单
          </div>
          <div class="code-name" title=".icon-xinjianqianshan">
            .icon-xinjianqianshan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-ST_APPOINT_AT_HOME"></span>
          <div class="name">
            预约上门
          </div>
          <div class="code-name" title=".icon-ST_APPOINT_AT_HOME">
            .icon-ST_APPOINT_AT_HOME</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-SCENE_TOOLS"></span>
          <div class="name">
            场景工具
          </div>
          <div class="code-name" title=".icon-SCENE_TOOLS">
            .icon-SCENE_TOOLS</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-CST_OVERVIEW"></span>
          <div class="name">
            客户概览
          </div>
          <div class="code-name" title=".icon-CST_OVERVIEW">
            .icon-CST_OVERVIEW</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-RANK_LIST"></span>
          <div class="name">
            排行榜
          </div>
          <div class="code-name" title=".icon-RANK_LIST">
            .icon-RANK_LIST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-TASK_CENTER"></span>
          <div class="name">
            任务中心
          </div>
          <div class="code-name" title=".icon-TASK_CENTER">
            .icon-TASK_CENTER</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-BIZ_DATA"></span>
          <div class="name">
            业务数据
          </div>
          <div class="code-name" title=".icon-BIZ_DATA">
            .icon-BIZ_DATA</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-Frame9266"></span>
          <div class="name">
            Frame 9266
          </div>
          <div class="code-name" title=".icon-Frame9266">
            .icon-Frame9266</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-richengtongzhi"></span>
          <div class="name">
            日程通知
          </div>
          <div class="code-name" title=".icon-richengtongzhi">
            .icon-richengtongzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yewu-1"></span>
          <div class="name">
            业务-1
          </div>
          <div class="code-name" title=".icon-yewu-1">
            .icon-yewu-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shenpi-1"></span>
          <div class="name">
            审批-1
          </div>
          <div class="code-name" title=".icon-shenpi-1">
            .icon-shenpi-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yewu-2"></span>
          <div class="name">
            业务-2
          </div>
          <div class="code-name" title=".icon-yewu-2">
            .icon-yewu-2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-richeng-sidu"></span>
          <div class="name">
            日程-已读
          </div>
          <div class="code-name" title=".icon-richeng-sidu">
            .icon-richeng-sidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shenpi-sidu"></span>
          <div class="name">
            审批-已读
          </div>
          <div class="code-name" title=".icon-shenpi-sidu">
            .icon-shenpi-sidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yewu-sidu"></span>
          <div class="name">
            业务-已读
          </div>
          <div class="code-name" title=".icon-yewu-sidu">
            .icon-yewu-sidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-P_SCHEDULE"></span>
          <div class="name">
            日程-1
          </div>
          <div class="code-name" title=".icon-P_SCHEDULE">
            .icon-P_SCHEDULE</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shenpi"></span>
          <div class="name">
            审批
          </div>
          <div class="code-name" title=".icon-shenpi">
            .icon-shenpi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-huangxiangshouqi"></span>
          <div class="name">
            横向收起
          </div>
          <div class="code-name" title=".icon-huangxiangshouqi">
            .icon-huangxiangshouqi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-huangxiangzhankai"></span>
          <div class="name">
            横向展开
          </div>
          <div class="code-name" title=".icon-huangxiangzhankai">
            .icon-huangxiangzhankai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shangsheng"></span>
          <div class="name">
            上升
          </div>
          <div class="code-name" title=".icon-shangsheng">
            .icon-shangsheng</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-xiajiang"></span>
          <div class="name">
            下降
          </div>
          <div class="code-name" title=".icon-xiajiang">
            .icon-xiajiang</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yingxiaoguanli"></span>
          <div class="name">
            营销管理
          </div>
          <div class="code-name" title=".icon-yingxiaoguanli">
            .icon-yingxiaoguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-browse"></span>
          <div class="name">
            browse
          </div>
          <div class="code-name" title=".icon-browse">
            .icon-browse</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-chulizhong"></span>
          <div class="name">
            处理中
          </div>
          <div class="code-name" title=".icon-chulizhong">
            .icon-chulizhong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yibai"></span>
          <div class="name">
            失败
          </div>
          <div class="code-name" title=".icon-yibai">
            .icon-yibai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-shujuguanli"></span>
          <div class="name">
            数据管理
          </div>
          <div class="code-name" title=".icon-shujuguanli">
            .icon-shujuguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-chanpinguanli"></span>
          <div class="name">
            产品管理
          </div>
          <div class="code-name" title=".icon-chanpinguanli">
            .icon-chanpinguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-judaoguanli"></span>
          <div class="name">
            渠道管理
          </div>
          <div class="code-name" title=".icon-judaoguanli">
            .icon-judaoguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-yingxiaoshuju"></span>
          <div class="name">
            营销数据
          </div>
          <div class="code-name" title=".icon-yingxiaoshuju">
            .icon-yingxiaoshuju</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-pinglun"></span>
          <div class="name">
            评论
          </div>
          <div class="code-name" title=".icon-pinglun">
            .icon-pinglun</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-zhuanzhanghaikuan"></span>
          <div class="name">
            转账还款
          </div>
          <div class="code-name" title=".icon-zhuanzhanghaikuan">
            .icon-zhuanzhanghaikuan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-wenjian"></span>
          <div class="name">
            文件
          </div>
          <div class="code-name" title=".icon-wenjian">
            .icon-wenjian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-faqiqianyue"></span>
          <div class="name">
            发起签约
          </div>
          <div class="code-name" title=".icon-faqiqianyue">
            .icon-faqiqianyue</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-libiaobiaoji"></span>
          <div class="name">
            列表标记
          </div>
          <div class="code-name" title=".icon-libiaobiaoji">
            .icon-libiaobiaoji</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-zidingyilibiaoyanse"></span>
          <div class="name">
            自定义列表颜色
          </div>
          <div class="code-name" title=".icon-zidingyilibiaoyanse">
            .icon-zidingyilibiaoyanse</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制类名">
          <span class="icon iconfont icon-gongzhengzhongxin"></span>
          <div class="name">
            公证中心
          </div>
          <div class="code-name" title=".icon-gongzhengzhongxin">
            .icon-gongzhengzhongxin</div>
        </li>
      </ul>
      <div class="codesign-demo__usage markdown">
        <h2>Class 引用</h2>
        <h3>1. 引入已经将 unicode 与类名一一对应的 <code>iconfont.css</code> 文件；</h3>
        <pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
        </code></pre>
        <h3>2. 应用图标，使用对应的类名。</h3>
        <pre><code class="language-html">&lt;span class="iconfont prefix-classname"&gt;&lt;/span&gt;
        </code></pre>
        <blockquote>
          <p>
            "iconfont" 是图标库下的 “iconfont family” 字段，可以通过编辑图标库查看，默认是 "iconfont"。<br>
            "prefix-" 是图标库下的“iconfont 前缀”字段，可以通过编辑图标库查看，默认是 "icon-"。
          </p>
        </blockquote>
      </div>
    </div>

    <div id="symbol" class="codesign-demo__container codesign-demo__container--symbol active">
      <ul class="codesign-demo__icon-lists">
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-wenjianga-kai_folder-open"></use>
          </svg>
          <div class="name">文件夹-开_folder-open</div>
          <div class="code-name">#icon-wenjianga-kai_folder-open</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-xiaoxi"></use>
          </svg>
          <div class="name">消息</div>
          <div class="code-name">#icon-xiaoxi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-tianjialunxu"></use>
          </svg>
          <div class="name">添加轮呼</div>
          <div class="code-name">#icon-tianjialunxu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-libiaopeizhi"></use>
          </svg>
          <div class="name">列表配置</div>
          <div class="code-name">#icon-libiaopeizhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shanshu"></use>
          </svg>
          <div class="name">删除</div>
          <div class="code-name">#icon-shanshu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-daochu"></use>
          </svg>
          <div class="name">导出</div>
          <div class="code-name">#icon-daochu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-daoru"></use>
          </svg>
          <div class="name">导入</div>
          <div class="code-name">#icon-daoru</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-browse-off"></use>
          </svg>
          <div class="name">browse-off</div>
          <div class="code-name">#icon-browse-off</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingtimeactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;time, active&#x3D;no</div>
          <div class="code-name">#icon-leixingtimeactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingrijishijianactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;日期时间, active&#x3D;no</div>
          <div class="code-name">#icon-leixingrijishijianactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingbumenduoxuanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;部门多选, active&#x3D;no</div>
          <div class="code-name">#icon-leixingbumenduoxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingbumenshanxuanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;部门单选, active&#x3D;no</div>
          <div class="code-name">#icon-leixingbumenshanxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingxuanzhaishujuactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;选择数据, active&#x3D;no</div>
          <div class="code-name">#icon-leixingxuanzhaishujuactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingzicengbianxiaoactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;自增编号, active&#x3D;no</div>
          <div class="code-name">#icon-leixingzicengbianxiaoactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingyouxiangdezhiactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;邮箱地址, active&#x3D;no</div>
          <div class="code-name">#icon-leixingyouxiangdezhiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingshenfenzhengactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;身份证, active&#x3D;no</div>
          <div class="code-name">#icon-leixingshenfenzhengactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingyouzhengbianmaactiveno"></use>
          </svg>
          <div class="name">流水号</div>
          <div class="code-name">#icon-leixingyouzhengbianmaactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingshuziactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;数字, active&#x3D;no</div>
          <div class="code-name">#icon-leixingshuziactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingshoujiactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;手机, active&#x3D;no</div>
          <div class="code-name">#icon-leixingshoujiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingmofenbiactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;百分比, active&#x3D;no</div>
          <div class="code-name">#icon-leixingmofenbiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingjineactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;金额, active&#x3D;no</div>
          <div class="code-name">#icon-leixingjineactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingduoxingwenbenactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;多行文本, active&#x3D;no</div>
          <div class="code-name">#icon-leixingduoxingwenbenactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingwenzimaoshuactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;文字描述, active&#x3D;no</div>
          <div class="code-name">#icon-leixingwenzimaoshuactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingshanxingwenbenactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;单行文本, active&#x3D;no</div>
          <div class="code-name">#icon-leixingshanxingwenbenactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixinghuakuaiactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;滑块, active&#x3D;no</div>
          <div class="code-name">#icon-leixinghuakuaiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingkaiguanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;开关, active&#x3D;no</div>
          <div class="code-name">#icon-leixingkaiguanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingzibiaoshanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;子表单, active&#x3D;no</div>
          <div class="code-name">#icon-leixingzibiaoshanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingfengexianactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;分割线, active&#x3D;no</div>
          <div class="code-name">#icon-leixingfengexianactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingguanlianzhaxunactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;关联查询, active&#x3D;no</div>
          <div class="code-name">#icon-leixingguanlianzhaxunactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingwenzilianxieactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;文字链接, active&#x3D;no</div>
          <div class="code-name">#icon-leixingwenzilianxieactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingdezhixiangqingactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;地址详情, active&#x3D;no</div>
          <div class="code-name">#icon-leixingdezhixiangqingactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingdezhiactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;地址, active&#x3D;no</div>
          <div class="code-name">#icon-leixingdezhiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingchengyunduoxuanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;成员多选, active&#x3D;no</div>
          <div class="code-name">#icon-leixingchengyunduoxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingchengyunshanxuanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;成员单选, active&#x3D;no</div>
          <div class="code-name">#icon-leixingchengyunshanxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingtieweiactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;占位, active&#x3D;no</div>
          <div class="code-name">#icon-leixingtieweiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingtupianactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;图片, active&#x3D;no</div>
          <div class="code-name">#icon-leixingtupianactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingbujianactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;附件, active&#x3D;no</div>
          <div class="code-name">#icon-leixingbujianactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingradioactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;radio, active&#x3D;no</div>
          <div class="code-name">#icon-leixingradioactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingfuxuanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;复选, active&#x3D;no</div>
          <div class="code-name">#icon-leixingfuxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingtabactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;tab, active&#x3D;no</div>
          <div class="code-name">#icon-leixingtabactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixinganniuactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;按钮, active&#x3D;no</div>
          <div class="code-name">#icon-leixinganniuactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingxialashanxuanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;下拉单选, active&#x3D;no</div>
          <div class="code-name">#icon-leixingxialashanxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingxialafuxuanactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;下拉复选, active&#x3D;no</div>
          <div class="code-name">#icon-leixingxialafuxuanactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingzuojiactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;座机, active&#x3D;no</div>
          <div class="code-name">#icon-leixingzuojiactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingxinyongdaimaactiveno"></use>
          </svg>
          <div class="name">类型&#x3D;信用代码, active&#x3D;no</div>
          <div class="code-name">#icon-leixingxinyongdaimaactiveno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-fuzhi"></use>
          </svg>
          <div class="name">复制</div>
          <div class="code-name">#icon-fuzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-peizhizhongxin"></use>
          </svg>
          <div class="name">配置中心</div>
          <div class="code-name">#icon-peizhizhongxin</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-close"></use>
          </svg>
          <div class="name">close</div>
          <div class="code-name">#icon-close</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-jitongguanli"></use>
          </svg>
          <div class="name">系统管理</div>
          <div class="code-name">#icon-jitongguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-tuodong"></use>
          </svg>
          <div class="name">拖动</div>
          <div class="code-name">#icon-tuodong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-filter"></use>
          </svg>
          <div class="name">filter</div>
          <div class="code-name">#icon-filter</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-tianjia"></use>
          </svg>
          <div class="name">添加</div>
          <div class="code-name">#icon-tianjia</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-menu-unfold"></use>
          </svg>
          <div class="name">menu-unfold</div>
          <div class="code-name">#icon-menu-unfold</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-menu-fold"></use>
          </svg>
          <div class="name">menu-fold</div>
          <div class="code-name">#icon-menu-fold</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame-4"></use>
          </svg>
          <div class="name">撤消下一步</div>
          <div class="code-name">#icon-Frame-4</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame-3"></use>
          </svg>
          <div class="name">撤消上一步</div>
          <div class="code-name">#icon-Frame-3</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame-2"></use>
          </svg>
          <div class="name">抄送</div>
          <div class="code-name">#icon-Frame-2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame-1"></use>
          </svg>
          <div class="name">条件</div>
          <div class="code-name">#icon-Frame-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame"></use>
          </svg>
          <div class="name">流程</div>
          <div class="code-name">#icon-Frame</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shanshu-1"></use>
          </svg>
          <div class="name">删除-fill</div>
          <div class="code-name">#icon-shanshu-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame-5"></use>
          </svg>
          <div class="name">公司</div>
          <div class="code-name">#icon-Frame-5</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1star-filled"></use>
          </svg>
          <div class="name">Property 1&#x3D;star-filled</div>
          <div class="code-name">#icon-Property1star-filled</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1star"></use>
          </svg>
          <div class="name">Property 1&#x3D;star</div>
          <div class="code-name">#icon-Property1star</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-document"></use>
          </svg>
          <div class="name">document</div>
          <div class="code-name">#icon-document</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-success-outline"></use>
          </svg>
          <div class="name">success-outline</div>
          <div class="code-name">#icon-success-outline</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-delete"></use>
          </svg>
          <div class="name">delete</div>
          <div class="code-name">#icon-delete</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-warning-1"></use>
          </svg>
          <div class="name">warning-1</div>
          <div class="code-name">#icon-warning-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-warning"></use>
          </svg>
          <div class="name">warning</div>
          <div class="code-name">#icon-warning</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-warning-outline"></use>
          </svg>
          <div class="name">warning-outline</div>
          <div class="code-name">#icon-warning-outline</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-d-caret"></use>
          </svg>
          <div class="name">d-caret</div>
          <div class="code-name">#icon-d-caret</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-refresh"></use>
          </svg>
          <div class="name">refresh</div>
          <div class="code-name">#icon-refresh</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-sort"></use>
          </svg>
          <div class="name">sort</div>
          <div class="code-name">#icon-sort</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-lock-off"></use>
          </svg>
          <div class="name">lock-off</div>
          <div class="code-name">#icon-lock-off</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-lock-on"></use>
          </svg>
          <div class="name">lock-on</div>
          <div class="code-name">#icon-lock-on</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shangzhuan"></use>
          </svg>
          <div class="name">上传</div>
          <div class="code-name">#icon-shangzhuan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-xiazai"></use>
          </svg>
          <div class="name">下载</div>
          <div class="code-name">#icon-xiazai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-anzhuo"></use>
          </svg>
          <div class="name">安卓</div>
          <div class="code-name">#icon-anzhuo</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-pengluo"></use>
          </svg>
          <div class="name">苹果</div>
          <div class="code-name">#icon-pengluo</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-piliangcaozuo"></use>
          </svg>
          <div class="name">批量操作</div>
          <div class="code-name">#icon-piliangcaozuo</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-fork"></use>
          </svg>
          <div class="name">fork</div>
          <div class="code-name">#icon-fork</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yijiao"></use>
          </svg>
          <div class="name">移交</div>
          <div class="code-name">#icon-yijiao</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame-6"></use>
          </svg>
          <div class="name">Frame</div>
          <div class="code-name">#icon-Frame-6</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-guanbitongzhi"></use>
          </svg>
          <div class="name">关闭通知</div>
          <div class="code-name">#icon-guanbitongzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-add"></use>
          </svg>
          <div class="name">add</div>
          <div class="code-name">#icon-add</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-circle-close1"></use>
          </svg>
          <div class="name">circle-删除</div>
          <div class="code-name">#icon-circle-close1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-check"></use>
          </svg>
          <div class="name">check</div>
          <div class="code-name">#icon-check</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-arrowRight"></use>
          </svg>
          <div class="name">arrowRight</div>
          <div class="code-name">#icon-arrowRight</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-arrowLeft"></use>
          </svg>
          <div class="name">arrowLeft</div>
          <div class="code-name">#icon-arrowLeft</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-arrowDown"></use>
          </svg>
          <div class="name">arrowDown</div>
          <div class="code-name">#icon-arrowDown</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-arrowUp"></use>
          </svg>
          <div class="name">arrowUp</div>
          <div class="code-name">#icon-arrowUp</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-tongji"></use>
          </svg>
          <div class="name">同级</div>
          <div class="code-name">#icon-tongji</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-unmute"></use>
          </svg>
          <div class="name">录音&#x3D;yes</div>
          <div class="code-name">#icon-unmute</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-mute"></use>
          </svg>
          <div class="name">录音&#x3D;no</div>
          <div class="code-name">#icon-mute</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CLEANUP"></use>
          </svg>
          <div class="name">类型&#x3D;写跟进</div>
          <div class="code-name">#icon-CLEANUP</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-EXPIRED"></use>
          </svg>
          <div class="name">类型&#x3D;已欠费</div>
          <div class="code-name">#icon-EXPIRED</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-STOP"></use>
          </svg>
          <div class="name">类型&#x3D;已停止</div>
          <div class="code-name">#icon-STOP</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-LEAVE"></use>
          </svg>
          <div class="name">类型&#x3D;离开</div>
          <div class="code-name">#icon-LEAVE</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CALLING"></use>
          </svg>
          <div class="name">类型&#x3D;通话中</div>
          <div class="code-name">#icon-CALLING</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-FREE"></use>
          </svg>
          <div class="name">类型&#x3D;空闲</div>
          <div class="code-name">#icon-FREE</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-READY"></use>
          </svg>
          <div class="name">类型&#x3D;已就绪</div>
          <div class="code-name">#icon-READY</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-setting"></use>
          </svg>
          <div class="name">类型&#x3D;设置</div>
          <div class="code-name">#icon-setting</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-hoverno"></use>
          </svg>
          <div class="name">hover&#x3D;no</div>
          <div class="code-name">#icon-hoverno</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CORG"></use>
          </svg>
          <div class="name">类型&#x3D;呼出</div>
          <div class="code-name">#icon-CORG</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingxuru"></use>
          </svg>
          <div class="name">类型&#x3D;呼入</div>
          <div class="code-name">#icon-leixingxuru</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-play"></use>
          </svg>
          <div class="name">播放</div>
          <div class="code-name">#icon-play</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-pause"></use>
          </svg>
          <div class="name">暂停</div>
          <div class="code-name">#icon-pause</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-jigou"></use>
          </svg>
          <div class="name">机构</div>
          <div class="code-name">#icon-jigou</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-jiebang"></use>
          </svg>
          <div class="name">解绑</div>
          <div class="code-name">#icon-jiebang</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-bangding"></use>
          </svg>
          <div class="name">绑定</div>
          <div class="code-name">#icon-bangding</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1chenchong"></use>
          </svg>
          <div class="name">Property 1&#x3D;填充</div>
          <div class="code-name">#icon-Property1chenchong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shenpi-2"></use>
          </svg>
          <div class="name">审批-2</div>
          <div class="code-name">#icon-shenpi-2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yewu"></use>
          </svg>
          <div class="name">业务</div>
          <div class="code-name">#icon-yewu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-zijiedian"></use>
          </svg>
          <div class="name">子节点</div>
          <div class="code-name">#icon-zijiedian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-tongji-1"></use>
          </svg>
          <div class="name">同级-1</div>
          <div class="code-name">#icon-tongji-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame-2-1"></use>
          </svg>
          <div class="name">抄送-1</div>
          <div class="code-name">#icon-Frame-2-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-tianjia-1"></use>
          </svg>
          <div class="name">添加-1</div>
          <div class="code-name">#icon-tianjia-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-qiyong"></use>
          </svg>
          <div class="name">Property 1&#x3D;启用</div>
          <div class="code-name">#icon-qiyong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-jinyong"></use>
          </svg>
          <div class="name">Property 1&#x3D;禁用</div>
          <div class="code-name">#icon-jinyong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-leixingdianxu"></use>
          </svg>
          <div class="name">类型&#x3D;点呼</div>
          <div class="code-name">#icon-leixingdianxu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-chongzhi"></use>
          </svg>
          <div class="name">充值</div>
          <div class="code-name">#icon-chongzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-left"></use>
          </svg>
          <div class="name">left</div>
          <div class="code-name">#icon-left</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-right"></use>
          </svg>
          <div class="name">right</div>
          <div class="code-name">#icon-right</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-down"></use>
          </svg>
          <div class="name">down</div>
          <div class="code-name">#icon-down</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-up"></use>
          </svg>
          <div class="name">up</div>
          <div class="code-name">#icon-up</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame-6-1"></use>
          </svg>
          <div class="name">号码检测</div>
          <div class="code-name">#icon-Frame-6-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1shouqi"></use>
          </svg>
          <div class="name">Property 1&#x3D;收起</div>
          <div class="code-name">#icon-Property1shouqi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1zhankai"></use>
          </svg>
          <div class="name">Property 1&#x3D;展开</div>
          <div class="code-name">#icon-Property1zhankai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-move"></use>
          </svg>
          <div class="name">move</div>
          <div class="code-name">#icon-move</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shezhi"></use>
          </svg>
          <div class="name">设置</div>
          <div class="code-name">#icon-shezhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-quanping"></use>
          </svg>
          <div class="name">全屏</div>
          <div class="code-name">#icon-quanping</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-weidu"></use>
          </svg>
          <div class="name">未读</div>
          <div class="code-name">#icon-weidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-sidu"></use>
          </svg>
          <div class="name">已读</div>
          <div class="code-name">#icon-sidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-qiahutongzhi"></use>
          </svg>
          <div class="name">客户通知</div>
          <div class="code-name">#icon-qiahutongzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-help-circle"></use>
          </svg>
          <div class="name">help-circle</div>
          <div class="code-name">#icon-help-circle</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1caret-left"></use>
          </svg>
          <div class="name">Property 1&#x3D;caret-left</div>
          <div class="code-name">#icon-Property1caret-left</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1caret-right"></use>
          </svg>
          <div class="name">Property 1&#x3D;caret-right</div>
          <div class="code-name">#icon-Property1caret-right</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1caret-bottom"></use>
          </svg>
          <div class="name">Property 1&#x3D;caret-bottom</div>
          <div class="code-name">#icon-Property1caret-bottom</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1caret-top"></use>
          </svg>
          <div class="name">Property 1&#x3D;caret-top</div>
          <div class="code-name">#icon-Property1caret-top</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-bujian"></use>
          </svg>
          <div class="name">附件</div>
          <div class="code-name">#icon-bujian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-gonghai"></use>
          </svg>
          <div class="name">公海</div>
          <div class="code-name">#icon-gonghai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-bianji"></use>
          </svg>
          <div class="name">编辑</div>
          <div class="code-name">#icon-bianji</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-jieduan"></use>
          </svg>
          <div class="name">阶段</div>
          <div class="code-name">#icon-jieduan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-lunxu"></use>
          </svg>
          <div class="name">轮呼</div>
          <div class="code-name">#icon-lunxu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-hangup"></use>
          </svg>
          <div class="name">类型&#x3D;挂断</div>
          <div class="code-name">#icon-hangup</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-restore"></use>
          </svg>
          <div class="name">还原</div>
          <div class="code-name">#icon-restore</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-receive"></use>
          </svg>
          <div class="name">领取</div>
          <div class="code-name">#icon-receive</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-huishouzhan"></use>
          </svg>
          <div class="name">回收站</div>
          <div class="code-name">#icon-huishouzhan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-gonghai-1"></use>
          </svg>
          <div class="name">公海-1</div>
          <div class="code-name">#icon-gonghai-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-call-round"></use>
          </svg>
          <div class="name">轮呼-1</div>
          <div class="code-name">#icon-call-round</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-recall"></use>
          </svg>
          <div class="name">呼叫上一个</div>
          <div class="code-name">#icon-recall</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-tongxun"></use>
          </svg>
          <div class="name">通讯</div>
          <div class="code-name">#icon-tongxun</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yunyingguanli"></use>
          </svg>
          <div class="name">运营管理</div>
          <div class="code-name">#icon-yunyingguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shoudongguanlian"></use>
          </svg>
          <div class="name">手动关联</div>
          <div class="code-name">#icon-shoudongguanlian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-zijiedian-1"></use>
          </svg>
          <div class="name">子节点-1</div>
          <div class="code-name">#icon-zijiedian-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-chehui"></use>
          </svg>
          <div class="name">撤回</div>
          <div class="code-name">#icon-chehui</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-caiwuguanli"></use>
          </svg>
          <div class="name">财务管理</div>
          <div class="code-name">#icon-caiwuguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shujutongji"></use>
          </svg>
          <div class="name">数据统计</div>
          <div class="code-name">#icon-shujutongji</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shenpijiedian"></use>
          </svg>
          <div class="name">审批节点</div>
          <div class="code-name">#icon-shenpijiedian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-OvalStroke"></use>
          </svg>
          <div class="name">Oval (Stroke)</div>
          <div class="code-name">#icon-OvalStroke</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-guanbi"></use>
          </svg>
          <div class="name">关闭</div>
          <div class="code-name">#icon-guanbi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-minus-circle"></use>
          </svg>
          <div class="name">minus-circle</div>
          <div class="code-name">#icon-minus-circle</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-linwu"></use>
          </svg>
          <div class="name">任务</div>
          <div class="code-name">#icon-linwu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-renzheng"></use>
          </svg>
          <div class="name">认证</div>
          <div class="code-name">#icon-renzheng</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1dongjie"></use>
          </svg>
          <div class="name">Property 1&#x3D;冻结</div>
          <div class="code-name">#icon-Property1dongjie</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1huoyue"></use>
          </svg>
          <div class="name">Property 1&#x3D;活跃</div>
          <div class="code-name">#icon-Property1huoyue</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Property1yukai"></use>
          </svg>
          <div class="name">Property 1&#x3D;预开</div>
          <div class="code-name">#icon-Property1yukai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-wenxiao"></use>
          </svg>
          <div class="name">问号</div>
          <div class="code-name">#icon-wenxiao</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-duojiliandong"></use>
          </svg>
          <div class="name">多级联动</div>
          <div class="code-name">#icon-duojiliandong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-fenlan"></use>
          </svg>
          <div class="name">分栏</div>
          <div class="code-name">#icon-fenlan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-qianming"></use>
          </svg>
          <div class="name">签名</div>
          <div class="code-name">#icon-qianming</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-jieduan-1"></use>
          </svg>
          <div class="name">阶段-1</div>
          <div class="code-name">#icon-jieduan-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-chuangjian"></use>
          </svg>
          <div class="name">创建</div>
          <div class="code-name">#icon-chuangjian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CO_TIME_OUT_UNFOLLOW"></use>
          </svg>
          <div class="name">CO_TIME_OUT_UNFOLLOW</div>
          <div class="code-name">#icon-CO_TIME_OUT_UNFOLLOW</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CO_RECYCLE_CST"></use>
          </svg>
          <div class="name">CO_RECYCLE_CST</div>
          <div class="code-name">#icon-CO_RECYCLE_CST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CO_TODAY_NEW_CST"></use>
          </svg>
          <div class="name">CO_TODAY_NEW_CST</div>
          <div class="code-name">#icon-CO_TODAY_NEW_CST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CO_CST_AT_HOME"></use>
          </svg>
          <div class="name">CO_CST_AT_HOME</div>
          <div class="code-name">#icon-CO_CST_AT_HOME</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CO_FOLLOWED_CST"></use>
          </svg>
          <div class="name">CO_FOLLOWED_CST</div>
          <div class="code-name">#icon-CO_FOLLOWED_CST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CO_ATTENTION_CST"></use>
          </svg>
          <div class="name">CO_ATTENTION_CST</div>
          <div class="code-name">#icon-CO_ATTENTION_CST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-zhuanqiahu"></use>
          </svg>
          <div class="name">转客户</div>
          <div class="code-name">#icon-zhuanqiahu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-secured"></use>
          </svg>
          <div class="name">secured</div>
          <div class="code-name">#icon-secured</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-22"></use>
          </svg>
          <div class="name">22</div>
          <div class="code-name">#icon-22</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-search"></use>
          </svg>
          <div class="name">search</div>
          <div class="code-name">#icon-search</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yunyinggongju"></use>
          </svg>
          <div class="name">运营工具</div>
          <div class="code-name">#icon-yunyinggongju</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yingyongshangcheng"></use>
          </svg>
          <div class="name">应用商城</div>
          <div class="code-name">#icon-yingyongshangcheng</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-ST_NEW_ST"></use>
          </svg>
          <div class="name">新建客户</div>
          <div class="code-name">#icon-ST_NEW_ST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-ST_REPAYMENT"></use>
          </svg>
          <div class="name">还款试算</div>
          <div class="code-name">#icon-ST_REPAYMENT</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-xianxiatuoqia"></use>
          </svg>
          <div class="name">线下拓客</div>
          <div class="code-name">#icon-xianxiatuoqia</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-xinjianqianshan"></use>
          </svg>
          <div class="name">新建签单</div>
          <div class="code-name">#icon-xinjianqianshan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-ST_APPOINT_AT_HOME"></use>
          </svg>
          <div class="name">预约上门</div>
          <div class="code-name">#icon-ST_APPOINT_AT_HOME</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-SCENE_TOOLS"></use>
          </svg>
          <div class="name">场景工具</div>
          <div class="code-name">#icon-SCENE_TOOLS</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-CST_OVERVIEW"></use>
          </svg>
          <div class="name">客户概览</div>
          <div class="code-name">#icon-CST_OVERVIEW</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-RANK_LIST"></use>
          </svg>
          <div class="name">排行榜</div>
          <div class="code-name">#icon-RANK_LIST</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-TASK_CENTER"></use>
          </svg>
          <div class="name">任务中心</div>
          <div class="code-name">#icon-TASK_CENTER</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-BIZ_DATA"></use>
          </svg>
          <div class="name">业务数据</div>
          <div class="code-name">#icon-BIZ_DATA</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-Frame9266"></use>
          </svg>
          <div class="name">Frame 9266</div>
          <div class="code-name">#icon-Frame9266</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-richengtongzhi"></use>
          </svg>
          <div class="name">日程通知</div>
          <div class="code-name">#icon-richengtongzhi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yewu-1"></use>
          </svg>
          <div class="name">业务-1</div>
          <div class="code-name">#icon-yewu-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shenpi-1"></use>
          </svg>
          <div class="name">审批-1</div>
          <div class="code-name">#icon-shenpi-1</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yewu-2"></use>
          </svg>
          <div class="name">业务-2</div>
          <div class="code-name">#icon-yewu-2</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-richeng-sidu"></use>
          </svg>
          <div class="name">日程-已读</div>
          <div class="code-name">#icon-richeng-sidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shenpi-sidu"></use>
          </svg>
          <div class="name">审批-已读</div>
          <div class="code-name">#icon-shenpi-sidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yewu-sidu"></use>
          </svg>
          <div class="name">业务-已读</div>
          <div class="code-name">#icon-yewu-sidu</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-P_SCHEDULE"></use>
          </svg>
          <div class="name">日程-1</div>
          <div class="code-name">#icon-P_SCHEDULE</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shenpi"></use>
          </svg>
          <div class="name">审批</div>
          <div class="code-name">#icon-shenpi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-huangxiangshouqi"></use>
          </svg>
          <div class="name">横向收起</div>
          <div class="code-name">#icon-huangxiangshouqi</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-huangxiangzhankai"></use>
          </svg>
          <div class="name">横向展开</div>
          <div class="code-name">#icon-huangxiangzhankai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shangsheng"></use>
          </svg>
          <div class="name">上升</div>
          <div class="code-name">#icon-shangsheng</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-xiajiang"></use>
          </svg>
          <div class="name">下降</div>
          <div class="code-name">#icon-xiajiang</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yingxiaoguanli"></use>
          </svg>
          <div class="name">营销管理</div>
          <div class="code-name">#icon-yingxiaoguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-browse"></use>
          </svg>
          <div class="name">browse</div>
          <div class="code-name">#icon-browse</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-chulizhong"></use>
          </svg>
          <div class="name">处理中</div>
          <div class="code-name">#icon-chulizhong</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yibai"></use>
          </svg>
          <div class="name">失败</div>
          <div class="code-name">#icon-yibai</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shujuguanli"></use>
          </svg>
          <div class="name">数据管理</div>
          <div class="code-name">#icon-shujuguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-chanpinguanli"></use>
          </svg>
          <div class="name">产品管理</div>
          <div class="code-name">#icon-chanpinguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-judaoguanli"></use>
          </svg>
          <div class="name">渠道管理</div>
          <div class="code-name">#icon-judaoguanli</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-yingxiaoshuju"></use>
          </svg>
          <div class="name">营销数据</div>
          <div class="code-name">#icon-yingxiaoshuju</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-pinglun"></use>
          </svg>
          <div class="name">评论</div>
          <div class="code-name">#icon-pinglun</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-zhuanzhanghaikuan"></use>
          </svg>
          <div class="name">转账还款</div>
          <div class="code-name">#icon-zhuanzhanghaikuan</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-wenjian"></use>
          </svg>
          <div class="name">文件</div>
          <div class="code-name">#icon-wenjian</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-faqiqianyue"></use>
          </svg>
          <div class="name">发起签约</div>
          <div class="code-name">#icon-faqiqianyue</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-libiaobiaoji"></use>
          </svg>
          <div class="name">列表标记</div>
          <div class="code-name">#icon-libiaobiaoji</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-zidingyilibiaoyanse"></use>
          </svg>
          <div class="name">自定义列表颜色</div>
          <div class="code-name">#icon-zidingyilibiaoyanse</div>
        </li>
        <li class="codesign-demo__icon-item" title="双击复制 id">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#icon-gongzhengzhongxin"></use>
          </svg>
          <div class="name">公证中心</div>
          <div class="code-name">#icon-gongzhengzhongxin</div>
        </li>
      </ul>
      <div class="codesign-demo__usage markdown">
        <h2>Symbol 引用</h2>
        <h3>1. 引入 <code>iconfont.js</code> 文件，向页面添加 SVG sprite；</h3>
        <pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
        <h3>2. 设置基本样式；</h3>
        <pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
        <h3>3. 应用图标，使用对应的 id 值。</h3>
        <pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
&lt;use xlink:href="#prefix-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
        <blockquote>
          <p>
            "iconfont" 是图标库下的 “iconfont family” 字段，可以通过编辑图标库查看，默认是 "iconfont"。<br>
            "prefix-" 是图标库下的“iconfont 前缀”字段，可以通过编辑图标库查看，默认是 "icon-"。
          </p>
        </blockquote>
      </div>
    </div>
  </div>

  <script>
    function ready(callback) {
      if (document.readyState != 'loading') callback();
      else if (document.addEventListener) document.addEventListener('DOMContentLoaded', callback);
      else document.attachEvent('onreadystatechange', function () {
        if (document.readyState == 'complete') callback();
      });
    }

    ready(function () {
      const tabs = document.querySelectorAll('.codesign-demo__nav-tab');
      const tabPanels = document.querySelectorAll(".codesign-demo__container");

      const tabClickHandler = function (tabClickEvent) {
        for (let i = 0; i < tabs.length; i++) {
          tabs[i].classList.remove("active");
        }

        let clickedTab = tabClickEvent.currentTarget;
        clickedTab.classList.add("active");
        tabClickEvent.preventDefault();

        for (i = 0; i < tabPanels.length; i++) {
          tabPanels[i].classList.remove("active");
        }
        let activePanelId = clickedTab.getAttribute("data-target");
        let activePanel = document.querySelector("#" + activePanelId);
        activePanel.classList.add("active");
      }

      for (i = 0; i < tabs.length; i++) {
        tabs[i].addEventListener("click", tabClickHandler);
      }

      const icons =  document.querySelectorAll(".codesign-demo__icon-item")

      //给每个li绑定事件
      for(let i = 0; i < icons.length; i++){
        icons[i].ondblclick = function (e) {
          //弹出对应的li节点里面的内容
          console.log(e.currentTarget)
          const iconName = e.currentTarget.querySelector('.code-name')
          console.log(iconName)

          const input = document.createElement('input')
          document.body.appendChild(input)
          input.setAttribute('value', iconName.innerText)
          input.select()
          if (document.execCommand('copy')) {
            document.execCommand('copy')
            console.log('复制成功')
          }
          document.body.removeChild(input)

        }
      }
    });
  </script>
</body>

</html>