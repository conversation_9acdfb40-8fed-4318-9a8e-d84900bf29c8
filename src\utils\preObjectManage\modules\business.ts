import BaseData from '../common'
import type { ComponentType, OptionType } from '@/types/preObjectManage'
import { fieldTypeConfig } from '@/enums/preObjectManage'

// 客户标签
export class Tags extends BaseData {
  public fieldProperty: string
  constructor (data: ComponentType, options: OptionType) {
    const fieldTypeName = '客户标签'
    // 字段默认数据
    const defaultData: ComponentType = {
      ...data,
      columnInfo: {
        ...data.columnInfo
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.tags
  }
}

// 转介绍人
export class ReferralPerson extends BaseData {
  public fieldProperty: string
  constructor (data: ComponentType, options: OptionType) {
    const fieldTypeName = '转介绍人'
    // 字段默认数据
    const defaultData: ComponentType = {
      ...data,
      columnInfo: {
        ...data.columnInfo
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.referralPerson
  }
}

// 转介绍人来源
export class ReferralPersonSource extends BaseData {
  public fieldProperty: string
  constructor (data: ComponentType, options: OptionType) {
    const fieldTypeName = '转介绍人来源'
    // 字段默认数据
    const defaultData: ComponentType = {
      ...data,
      columnInfo: {
        ...data.columnInfo
      }
    }
    super(defaultData, options, fieldTypeName)
    this.fieldProperty = fieldTypeConfig.referralPersonSource
  }
}
