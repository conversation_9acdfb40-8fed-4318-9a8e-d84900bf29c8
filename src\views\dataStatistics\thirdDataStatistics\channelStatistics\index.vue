<template>
  <div class="channel-statistics-page">
    <SearchFilter @register="registerSetting" @search="search" />

    <div class="table-box">
      <RxkVTable @register="registerTable"> </RxkVTable>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { unref, onMounted } from 'vue'
import { SearchFilter } from '@/components/business/searchFilter'
import { RxkVTable } from '@/components/common/RxkVTable'
import { useSearch } from '@/components/business/searchFilter/src/hooks/useSearch'
import { useTable } from '@/components/common/RxkVTable/src/hooks/useTable'
import { channelStatistics } from '../data'
import { fetchGetPushData } from '@/apis/carlife'

onMounted(() => {
  reload()
})

// 搜索表单数据
const [registerSetting] = useSearch({
  schemas: unref(channelStatistics.searchFormData)
})
const searchInfo: Record<string, any> = {
  model: {
    productName: '',
    userName: '',
    userPhone: ''
  }
}

const [registerTable, { reload, setSearchInfo }] = useTable({
  api: fetchGetPushData,
  columns: unref(channelStatistics.columns),
  searchInfo: searchInfo,
  immediate: false // 是否立刻请求
})

const search = (val: { [k: string]: any }) => {
  Object.assign(searchInfo.model, val)
  setSearchInfo(searchInfo)
  reload()
}
</script>

<style lang="scss" scoped>
.channel-statistics-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-form--inline .el-form-item {
    margin-right: 40px;
  }
  .btn-box {
    padding: 0 16px 4px;
    display: flex;
    justify-content: flex-end;
  }
  .table-box {
    width: 100%;
    flex: 1;
    overflow-y: hidden;
  }
}

.sort-btn {
  &:hover {
    color: $primary-color;
  }
  cursor: pointer;
  margin-left: 8px;
}
</style>
