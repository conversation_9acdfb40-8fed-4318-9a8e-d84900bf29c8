<template>
  <div class="role">
    <div class="left">
      <el-input
        v-model="personnelSelectionInput"
        class="personnelSelectionInput"
        placeholder="搜索角色"
        clearable
        :prefix-icon="Search"/>
      <div class="roleList">
        <div 
          v-for="(item, index) in roleList"
          :key="index">
          <el-checkbox @change="(val: boolean) => selectChange(val,item)" v-model="item.checked" :indeterminate="item.indeterminate"/>
          <span @click.stop="selectroleItem(item)">{{ item.name }}</span>
        </div>
      </div>
    </div>
    <div class="center">
      <div 
        class="childNodeItem"
        v-for="(item, index) in userList"
        :key="index">
        <el-checkbox 
          :disabled="item?.jobStatus?.code === 2"
          v-model="item.checked"
          @change="(val: boolean) => changeUserCheck(val,item)"
        />
        <span> {{ item.realName }}<template v-if="item?.jobStatus?.code === 2">（离职）</template></span>
      </div>
    </div>
    <div class="right">
      <div class="top">
        <span>已选择{{ checkUserList.length }}项</span>
        <span class="clear" @click="clearAll">清空</span>
      </div>
      <div 
        class="checkUserList"
        ref="scrollEl">
        <div class="checkUserList-item" v-for="item in showUserList" :key="item.id">
          <span>{{ item.realName }}</span>
          <i v-if="!item?.disable" class="iconfont icon-close" @click="delItem(item)"/>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script lang="ts" setup>
import { ref, watch, unref, type PropType, computed } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { cloneDeep, recursion } from '@/utils/tools'
import type { RoleVOList } from '../type'
import type { UserVOList } from '../type'
import { useInfiniteScroll } from '@vueuse/core'

import { VxeList } from 'vxe-table'
const personnelSelectionInput = ref<string>('')
const checkUserList = ref<UserVOList[]>([])
const userList = ref<UserVOList[]>([])
const currentRole = ref<Recordable>()
const roleList = ref<RoleVOList[]>([])
const count = ref<number>(10)
const emit = defineEmits(['update:data', 'updateValue'])
const scrollEl = ref<HTMLElement>(null)
const props = defineProps({
  data: {
    type: Array as PropType<RoleVOList[]>,
    default: () => {[]}
  },
  checkedUser: {
    type: Array as PropType<UserVOList[]>,
    default: () => {[]}
  }
})

useInfiniteScroll(
  scrollEl,
  () => {
    // load more
    // data.value.push(...moreData)
    if ( showUserList.value.length >= checkUserList.value.length) return
    count.value += 30
  },
  { distance: 10 }
)
const orginRoleData = computed(() => {
  return unref(props.data)
})
const showUserList = computed(() => {
  return checkUserList.value.slice(0, count.value)
})

watch(() => props.data, (newValue) => {
  roleList.value = cloneDeep(newValue)
}, {
  immediate: true,
  deep: true
})

console.log(roleList.value, 'roleList', props.data)
// watch(() => props.checkedUser, async (checkedUser) => {
//   checkUserList.value = cloneDeep(checkedUser || [])
//   await handleRoleCheckbox()
// }, {
//   immediate: true,
//   deep: true
// })

// handleRightData()
// 角色复选框点击
async function selectChange (checked:boolean, itemData: Recordable) {
  const checkedUserIds = new Set(checkUserList.value.map((item: { id: any }) => item.id))
  
  itemData.checked = checked
  itemData.indeterminate = !checked
  itemData.userVOList.forEach((item: UserVOList) => {
    item.checked = checked
    if(checked) {
      if(!checkedUserIds.has(item.id)) {
        checkUserList.value.push(item)
      }
    } else {
      const index = checkUserList.value.findIndex((citem: { id: any }) => item.id === citem.id)
      if(index !== -1) {
        checkUserList.value.splice(index, 1)
      }
      itemData.indeterminate = false
    }
  })
  emit('updateValue', checkUserList.value)
}

// 角色点击
async function selectroleItem (itemData: Recordable) {
  currentRole.value = itemData
  userList.value = itemData.userVOList
}

function compareArrays (userList: UserVOList[], userInfoList: UserVOList[]): UserVOList[] {
    
  const checkedUserMap = new Map(userInfoList.map((user, index) => [user.id, { checked: user.checked, index }]))
  const result: UserVOList[] = []
  for (const itemA of userList) {
    // const indexB = userInfoList?.findIndex(itemB => itemB.id === itemA.id)
    const checkedInfo = checkedUserMap.get(itemA.id)
    if (!checkedInfo && itemA.checked) {
      result.push(itemA)
    } else if (checkedInfo) {
      if(!itemA.checked) {
        userInfoList.splice(checkedInfo.index, 1)
      } else if (itemA.checked !== checkedInfo.checked) {
        userInfoList[checkedInfo.index] = itemA
      }
    }
  }
  return result
}

// 处理右侧数据选中
async function handleRightData () {
  const mergeArraysAndModifyData = compareArrays(unref(userList.value), unref(checkUserList.value || []))
  console.log(mergeArraysAndModifyData, 'compareArrays')
  checkUserList.value.push(...mergeArraysAndModifyData)
  emit('updateValue', cloneDeep(checkUserList.value))
  // 处理左侧角色checbox
  await handleRoleCheckbox()
}

async function handleRoleCheckbox () {
  recursion(roleList.value, item => {
    const checkedUserMap = new Map(checkUserList.value.map(user => [user.id, user]))

    item.userVOList.forEach((user: Recordable) => {
      user.checked = Boolean(checkedUserMap.get(user.id))
    })

    const filteredUsers = item.userVOList
    const allChecked = filteredUsers.every((user: { checked: any }) => user.checked)

    item.checked = allChecked
    item.indeterminate = !allChecked && filteredUsers.some((user: { checked: any }) => user.checked)
  })
}

async function changeUserCheck (checked: boolean, itemData: Recordable) {

  await handleRightData()
}

function clearAll () {
  checkUserList.value = checkUserList.value.filter(item => item.disable)
  roleList.value.map(item => {
    item.checked = false
    item.indeterminate = false
  })
  userList.value.map(item => item.checked = false)
  emit('updateValue', checkUserList.value)
}

async function delItem (itemData: Recordable) {
  const findId = checkUserList.value.findIndex(el => el.id === itemData.id)
  if(findId !== -1) {
    checkUserList.value.splice(findId, 1)
  }
  emit('updateValue', unref(checkUserList.value))
  await handleRoleCheckbox()
}

watch(personnelSelectionInput, (val) => {
  roleList.value = orginRoleData.value?.filter(item => item.name.includes(val))
})

function clear () {
  userList.value = []
}

async function updateCheck (checkedUser: UserVOList[]) {
  checkUserList.value = cloneDeep(checkedUser || [])
  await handleRoleCheckbox()
}

defineExpose({
  clear,
  updateCheck
})

</script>
  
  <style lang="scss" scoped>
  .role {
    @include flex-center(row, normal, normal);
    height: 100%;
    .left {
      flex: 1;
      border-right: 1px solid #EBEEF5;
      padding: 16px 16px 16px 0;
      .roleList {
        overflow-y: auto;
        height: calc(100% - 32px);
        .el-checkbox {
          margin-right: 10px;
        }
        span {
          cursor: pointer;
        }
      }
      .personnelSelectionInput {
        border-radius: 4px;
        width: 268px;
        :deep(.el-input__wrapper) {
          box-shadow: none;
          background: #F4F4F5;
        }
        margin-bottom: 13px;
      }
    }
    .center {
      width: 200px;
      border-right: 1px solid #EBEEF5;
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      :deep(.vxe-list--virtual-wrapper) {
        padding: 0px 16px;
    }
      .childNodeItem {
        .el-checkbox {
          margin-right: 10px;
        }
        span {
          cursor: pointer;
        }
      }
    }
    .right {
      flex: 1;
      overflow-y: auto;
      padding: 16px 0 16px 16px;
      font-size: $font-size-mini;
      color: $main-text-color;
    .top {
      @include flex-center(row, space-between, center);
      margin-bottom: 12px;
      .clear {
        color: $primary-color;
        cursor: pointer;
      }
    }
    .checkUserList {
      overflow-y: auto;
      height: calc(100% - 30px);
      &-item {
        display: inline-block;
        padding: 2px 6px 2px 10px;
        border-radius: 2px;
        background: #EEF3FF;
        margin-right: 8px;
        margin-bottom: 8px;
        .icon-close {
          cursor: pointer;
          font-size: $font-size-mini;
          margin-left: 8px;
        }
      }
    }
  }
  }
  </style>
