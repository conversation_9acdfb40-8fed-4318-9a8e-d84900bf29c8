<template>
  <div class="group-container">
    <div class="text-btn">
      <div class="text">列表组</div>
      <i class="iconfont icon icon-shezhi" @click="handleShowDrawer" />
    </div>
    <div class="tabs-list">
      <div
        v-for="item in outsideGroupList"
        :key="item.id"
        :class="['item', { active: item.id === outsideSelectGroupId }]"
        @click="handleClickTabsItem(item.id)"
      >
        {{ item.groupName }}
      </div>
    </div>
  </div>

  <RxkDrawer
    v-model="isShowDrawer"
    title="列表配置"
    size="1000"
    :footer="false"
    :btnLoading="false"
    :fullLoading="drawerLoading"
    primaryText="确定"
    class="table-column-setting-drawer"
    @close="drawerClose"
  >
    <template #content>
      <div class="drawer-content">
        <div class="list-group-area">
          <div class="title">
            <div>列表组</div>
            <div class="btn" @click="handleAddGroup">添加组</div>
          </div>

          <draggable
            :list="drawerTabsGroupList"
            tag="ul"
            animation="300"
            item-key="id"
            handle=".rank"
            @end="onGroupDragEnd"
          >
            <template #item="{ element }">
              <div
                :class="[
                  'item',
                  {
                    active:
                      element.id === drawerCurSelectItem.id || element.isEdit
                  },
                  { error: element.error }
                ]"
              >
                <el-icon
                  v-if="!element.isAdd"
                  class="rank tw-cursor-pointer tw-mr-[8px]"
                >
                  <Rank />
                </el-icon>
                <div class="name">
                  <el-input
                    v-if="element.isEdit"
                    v-model="element.groupName"
                    ref="inputRef"
                    placeholder="请输入"
                    maxlength="8"
                    clearable
                    @blur="() => (element.isEdit = false)"
                    @change="() => (element.hasEdit = true)"
                  />
                  <div v-else @click="handleClickGroupItem(element)">
                    {{ element.groupName }}
                  </div>
                </div>
                <i
                  v-if="
                    !element.isEdit && element.id === drawerCurSelectItem.id
                  "
                  class="iconfont icon-bianji tw-cursor-pointer tw-mr-[8px]"
                  @click="handleEditGroupItem(element)"
                />
                <i
                  v-if="
                    element.id === drawerCurSelectItem.id &&
                      element.id !== defaultGroupItemId
                  "
                  class="iconfont icon-shanshu tw-cursor-pointer"
                  @click="handleDeleteGroupItem(element)"
                />
              </div>
            </template>
          </draggable>
        </div>

        <div
          class="middle-tree-area"
          :style="{ width: isMultiLevel ? '300px' : '400px' }"
        >
          <div class="title">
            <div>
              <RxkCheckbox
                @change="changeAll"
                :indeterminate="inDeterminate"
                v-model="isCheckAll"
              />
              <span>全部字段({{ getAllTreeNodes().length }})</span>
            </div>
            <div class="btn" v-if="isMultiLevel" @click="hadleExpand">
              {{ isExpand ? '全部收起' : '全部展开' }}
            </div>
          </div>
          <RxkInput
            class="custom-input"
            :prefix-icon="Search"
            v-model="filterText"
          />

          <ul class="field-ul">
            <el-tree
              ref="treeRef"
              node-key="key"
              show-checkbox
              default-expand-all
              :class="{ 'single-level': !isMultiLevel }"
              :props="treeDefaultProps"
              :filter-node-method="filterNode"
              :data="formatColumns"
              @check="handleCheckChange"
            >
              <template #default="{ node }">
                <div class="custom-tree-node" :title="node.label">
                  {{ node.label }}
                </div>
              </template>
            </el-tree>
          </ul>
        </div>

        <div
          class="rigth-drag-area"
          :style="{ width: isMultiLevel ? '400px' : '300px' }"
        >
          <div class="title">
            <span>已选字段({{ curCheckedTreeItems.length }})</span>
            <div class="btn" v-if="isMultiLevel" @click="clearAll">清除</div>
          </div>
          <div class="sub-title tw-text-[#AAAAAA] tw-mb-[12px]">
            点击
            <span
              style="color: #666666"
              class="iconfont icon-zidingyilibiaoyanse"
            />
            后，该列突出显示为<span class="tw-ml-[4px] tw-text-[#F44D4D]"
            >红色</span
            >
          </div>
          <el-tree
            ref="rightDragTreeRef"
            node-key="key"
            class="drag-ul"
            default-expand-all
            :expand-on-click-node="false"
            draggable
            :indent="0"
            :props="treeDefaultProps"
            :allow-drop="allowDrop"
            :allow-drag="allowDrag"
            :data="rightDragList"
            @node-drag-end="handleDragEnd"
          >
            <template #default="{ data }">
              <div class="wrapper-item">
                <li
                  :class="['drag-item', { 'is-first-level': data.level === 1 }]"
                >
                  <el-icon :class="['rank', { move: !data.fixed }]"
                  ><Rank
                  /></el-icon>
                  <span
                    @click.stop="data.highlight = !data.highlight"
                    v-if="data.level === 1"
                    class="iconfont custome_icon icon-zidingyilibiaoyanse"
                    :class="{ active: data.highlight }"
                  />
                  <span
                    :class="['name', { disable: data.fixed }]"
                    :title="data.title"
                  >{{ data.title }}</span
                  >
                  <template v-if="data.level === 1">
                    <span
                      @click.stop="handleLock(data)"
                      v-if="data.fixed"
                      class="pointer lock iconfont icon icon-lock-on"
                    />
                    <span
                      @click.stop="handleLock(data)"
                      v-if="!data.fixed"
                      class="pointer lock iconfont icon icon-lock-off"
                    />
                    <span
                      @click="handleDelete(data)"
                      class="pointer iconfont icon icon-shanshu"
                    />
                  </template>
                </li>
              </div>
            </template>
          </el-tree>
        </div>

        <div class="btn-area">
          <el-button type="primary" @click="submit()">保存当前组</el-button>
        </div>
      </div>
    </template>

    <template #footerAfter>
      <span style="font-size: 12px; color: #aaaaaa"
      >注：此处字段将在列表展示，锁定后将在冻结列展示
        最多支持锁定两个字段</span
      >
    </template>
  </RxkDrawer>
</template>

<script setup lang="ts">
import { ref, watch, computed, type PropType, nextTick } from 'vue'
import type {
  AllowDropType,
  TreeNodeData
} from 'element-plus/es/components/tree/src/tree.type'
import draggable from 'vuedraggable'
import { cloneDeep, findLastIndex } from 'lodash-es'
import { ElMessage, ElTree } from 'element-plus'
import { Rank } from '@element-plus/icons-vue'
import { Search } from '@element-plus/icons-vue'

import { RxkDrawer } from '@/components/common/RxkDrawer'
import { RxkCheckbox } from '@/components/common/RxkCheckbox'
import { RxkInput } from '@/components/common/RxkInput'

import { useResource } from '@/hooks/useResource'
import {
  getInstitutionOption,
  setInstitutionOption
} from '@/apis/institutionManage'
import {
  fetchDragColumnGroup,
  fetchDeleteColumnGroup,
  getFetchColumnGroupList,
  fetchUpdateSelectedGroup
} from '@/apis/tableColumns'

import {
  type ItemType,
  getAllKeysTool,
  recursionFilterTool,
  findItemTool,
  recursion
} from '@/utils/tools'
import type { ColumnType } from '@/types/table'
import type { ColumnSettingEmitType } from '@/components/common/RxkVTable/src/type'

const emit = defineEmits(['columnLoaded'])
const { getMenuId } = useResource()

const props = defineProps({
  // 是否属于多级表头
  isMultiLevelHeader: {
    type: Boolean,
    default: false
  },
  // 是否拼接场景key值
  isJoin: {
    type: Boolean,
    default: true
  },
  // 进行字段分组的场景key值
  extraKey: {
    type: String,
    default: ''
  },
  // 所有可展示的字段数据
  columns: {
    type: Array as PropType<ColumnType[]>,
    default: () => []
  }
})

// 是否属于多级表头
const isMultiLevel = computed(() => props.isMultiLevelHeader)
// 表格字段项配置，数据查询所需参数的全局唯一key值
const businessType = computed(() => {
  return props.isJoin
    ? 'table-' + getMenuId() + (props.extraKey ? `-${props.extraKey}` : '')
    : props.extraKey
})
const formatColumns = computed(() => {
  const _deal = (data: ColumnType[], level = 1, parentKey = '') => {
    return data.map((item) => {
      const obj = {
        ...item,
        level,
        parentKey
      }

      if (item.children) {
        obj.children = _deal(item.children, level + 1, item.key)
      }
      return obj
    })
  }

  return _deal(props.columns)
})

getColumnGroupList()

/** ----------------------------------------(外部列表上方)切换组相关👇👇👇---------------------------------------- */
enum StateEnum {
  YES = 0,
  NO = 1
}

// 列表上方分组list
const outsideGroupList = ref<Recordable[]>([])
// 当前列表实际选中的分组ID
const outsideSelectGroupId = computed(
  () =>
    outsideGroupList.value?.find((i) => i.currentSelect === StateEnum.YES)?.id
)
// 默认组id
const defaultGroupItemId = computed(
  () =>
    outsideGroupList.value?.find((i) => i.defaultGroup === StateEnum.YES)?.id
)

// 初始渲染选中tabs
watch(
  () => outsideSelectGroupId.value,
  (id) => id && getConfigData(id, true)
)

async function getColumnGroupList () {
  const res = await getFetchColumnGroupList({
    businessType: businessType.value
  })
  outsideGroupList.value = res || []
  drawerTabsGroupList.value = cloneDeep(outsideGroupList.value)
}

async function handleClickTabsItem (id: string) {
  // 已选中项点击无效
  if (id === outsideSelectGroupId.value) {
    return
  }

  await fetchUpdateSelectedGroup(id)
  await getColumnGroupList()
}

async function handleShowDrawer ( ) {
  // 如果分组list数据未返回或者为空，不打开弹窗
  if(!outsideGroupList.value?.length) return 
  
  isShowDrawer.value = true
}

/** ----------------------------------------(抽屉左侧)tabs分组相关👇👇👇---------------------------------------- */
const TIPS_TEXT = '请保存当前列表组！'
const inputRef = ref()
// 已存在新增项
const hasAdd = computed(() => drawerTabsGroupList.value.find((i) => i.isAdd))
// 存在未完成的新增项
const hasNewItem = computed(() =>
  drawerTabsGroupList.value.find((i) => i.hasEdit || i.isEdit)
)
// 抽屉初始选中组-默认显示第一项
const drawerInitSelectItem = computed(() => outsideGroupList.value?.[0])

const drawerTabsGroupList = ref<Recordable[]>([])
const drawerCurSelectItem = ref<Recordable>({})

// 移除未完成的新增项
const removeUnfinished = () =>
  (drawerTabsGroupList.value = drawerTabsGroupList.value.filter(
    (i) => !i.isAdd
  ))
const handleAddGroup = () => {
  if (drawerTabsGroupList.value.length === 8) {
    ElMessage.error('最多可添加8组！')
    return
  }

  if (hasNewItem.value || hasAdd.value) {
    ElMessage.error(TIPS_TEXT)
    drawerCurSelectItem.value.error = true
    return
  }

  clearAll()

  drawerTabsGroupList.value.push({ isAdd: true, isEdit: true })
  drawerCurSelectItem.value =
    drawerTabsGroupList.value?.find((i) => i.isEdit) || {}

  // 输入框自动聚焦
  nextTick(() => inputRef.value?.focus?.())
}

const handleClickGroupItem = (item: any) => {
  // 已选中项、新增项点击无效
  if (!item.id || item.id === drawerCurSelectItem.value?.id) {
    return
  }

  if (hasNewItem.value) {
    ElMessage.error(TIPS_TEXT)
    let item = drawerTabsGroupList.value.find(
      (i) => i.id === drawerCurSelectItem.value.id
    )
    item && (item.error = true)
    return
  }

  dealConfigRefresh(item)
}

const handleEditGroupItem = (item: Recordable) => {
  drawerCurSelectItem.value = item
  item.isEdit = true

  // 输入框自动聚焦
  nextTick(() => inputRef.value?.focus?.())
}

const handleDeleteGroupItem = async (item: Recordable) => {
  const { id, isAdd } = item
  // 是否切换组的标志
  let toggleFlag = false

  isAdd && removeUnfinished()

  if (id) {
    await fetchDeleteColumnGroup(id)

    // 如果删除的组为初始选中组，则自动切换为默认组
    if (id === outsideSelectGroupId.value) {
      toggleFlag = true
      await fetchUpdateSelectedGroup(defaultGroupItemId.value)
    }
    await getColumnGroupList()
  }

  dealConfigRefresh(drawerInitSelectItem.value, toggleFlag)
}

async function onGroupDragEnd () {
  // 新增字段不参与排序
  const params = drawerTabsGroupList.value
  ?.filter((i) => !i.isAdd)
  ?.map((i, index) => ({ ...i, sort: index }))

  await fetchDragColumnGroup(params)
  await getColumnGroupList()
  dealConfigRefresh(drawerInitSelectItem.value)
}

/** -----------------------------------------(抽屉中间)树形节点组件相关👇👇👇----------------------------------------- */
const treeDefaultProps = {
  children: 'children',
  label: 'title',
  id: 'key'
}

const treeRef = ref<InstanceType<typeof ElTree>>()
const isExpand = ref(true)
const inDeterminate = ref(false)
const isCheckAll = ref(false)
// 获取当前树形组件所有节点
const getAllTreeNodes = () =>
  (treeRef.value && treeRef.value.store._getAllNodes()) || []
// 当前树形组件所有选中的节点key(包含父节点)
const allCheckedKeys = computed(
  () => (treeRef.value?.getCheckedKeys(false) || []) as string[]
)
// 当前树形组件已选项item数组
const curCheckedTreeItems = ref<TreeNodeData[]>([])
// 是否手动勾选
const isManualCheckFlag = ref(false)
// 排序keys
const sortKeys = ref<string[]>([])
const curCheckItem = ref<Recordable>()

// 过滤操作
const filterText = ref('')
const filterNode = (value: string, data: Recordable) =>
  value ? data.title.includes(value) : true
watch(
  () => filterText.value,
  (val) => treeRef.value!.filter(val)
)

function getFinalSortKeys () {
  let finalSortKeys: string[] = []
  // 初始化渲染
  if (!isManualCheckFlag.value) {
    finalSortKeys = allColumnKeys.value || []
  } else {
    // 后续手动触发
    if (!curCheckItem.value) return

    let _curItemKeys = []
    if (curCheckItem.value.level === 1) {
      _curItemKeys = [curCheckItem.value.key]
    } else {
      const item = findItemTool(
        formatColumns.value,
        'key',
        curCheckItem.value.parentKey
      )
      _curItemKeys = getAllKeysTool([item] as ItemType[]) || []
    }

    sortKeys.value = [...new Set(sortKeys.value)].concat(_curItemKeys)
    finalSortKeys = [...allColumnKeys.value, ...sortKeys.value]
  }

  // 例如[1,2,3,1,4]，期望得到[2,3,1,4]，而不是[1,2,3,4]
  return [...new Set(finalSortKeys.reverse())].reverse()
}

watch(
  () => treeRef.value?.getCheckedNodes(false, true),
  (val) => {
    nextTick(() => {
      const _sortFunc = (a: Recordable, b: Recordable) =>
        allColumnKeys.value.indexOf(a.key) - allColumnKeys.value.indexOf(b.key)
      curCheckedTreeItems.value = val?.sort(_sortFunc) || []

      const finalSortKeys = getFinalSortKeys()
      rightDragList.value = recursionFilterTool(
        cloneDeep(formatColumns.value),
        (i: Recordable) =>
          val
          ?.filter((i) => !i.children)
          ?.map((i) => i.key)
          .includes(i.key),
        finalSortKeys
      )
      dealCheckState(val?.length)

      rightDragTreeCheckAll()
    })
  }
)

const changeAll = (val: boolean) => {
  const _keys = val ? getAllTreeNodes().map((i) => i.key) : []
  treeRef.value!.setCheckedKeys(_keys)
  isManualCheckFlag.value = true
}

// 全部展开、收起
const hadleExpand = () => {
  getAllTreeNodes().forEach((item) => (item.expanded = !isExpand.value))
  isExpand.value = !isExpand.value
}

// 全选按钮状态处理
const dealCheckState = (lth: number = 0) => {
  inDeterminate.value = lth !== 0 && lth < getAllTreeNodes().length
  isCheckAll.value = lth === getAllTreeNodes().length
}

function handleCheckChange (targetItem: ItemType | undefined) {
  curCheckItem.value = targetItem
  isManualCheckFlag.value = true

  hasDataChange()
}

/** -----------------------------------------(抽屉右侧)拖拽排序组件相关👇👇👇----------------------------------------- */
const rightDragTreeRef = ref<InstanceType<typeof ElTree>>()
const rightDragList = ref<TreeNodeData[]>([])
// 选中全部(选中一级也就是选中了全部)
const rightDragTreeCheckAll = () =>
  rightDragTreeRef.value?.setCheckedKeys(
    rightDragList.value?.map((i) => i.key) || []
  )

const allowDrag = (draggingNode: TreeNodeData) => !draggingNode.data.fixed
const allowDrop = (
  draggingNode: TreeNodeData,
  dropNode: TreeNodeData,
  type: AllowDropType
) => {
  return (
    type !== 'inner' &&
    draggingNode.data.level === dropNode.data.level &&
    draggingNode.data.parentKey === dropNode.data.parentKey &&
    !dropNode.data.fixed
  )
}

function handleDelete (item: ItemType) {
  if (rightDragList.value.length === 1) {
    ElMessage.warning('至少保留一个')
    return
  }

  const _allKeys = getAllKeysTool([item])
  const _filterKeys =
    allCheckedKeys.value?.filter((i) => !_allKeys.includes(i)) || []
  treeRef.value?.setCheckedKeys(_filterKeys)

  hasDataChange()
}
function handleLock (item: Recordable) {
  if (!item.fixed) {
    const filterFixedArr = rightDragList.value.filter((item) => item.fixed)
    if (filterFixedArr.length >= 2) {
      ElMessage.warning('最多支持锁定两个字段')
      return
    }
    item.fixed = 'left'
  } else {
    item.fixed = undefined
  }

  // 锁定项默认置顶
  rightDragList.value.forEach((item, index) => {
    if (item.fixed) {
      rightDragList.value.splice(index, 1)
      rightDragList.value.unshift({ ...item })
    }
  })

  hasDataChange()

  nextTick(() => rightDragTreeCheckAll())
}

// 处理拖拽后选中项失效的问题
const handleDragEnd = () => {
  rightDragTreeCheckAll()

  hasDataChange()
}

// 清空已勾选数据
function clearAll () {
  treeRef.value?.setCheckedKeys([])

  allColumnKeys.value = []
  columnKeys.value = []
  lockKeys.value = []
  sortKeys.value = []

  hasDataChange()
}

/** -----------------------------------------组件整体通用相关👇👇👇----------------------------------------- */
const isShowDrawer = ref(false)
const drawerLoading = ref(false)

// 抽屉打开时，默认显示第一项分组，而不是外面的选中tab
watch(
  () => isShowDrawer.value,
  (val) => {
    if (val) {
      getColumnGroupList()
      dealConfigRefresh(drawerInitSelectItem.value)
    }
  }
)

// 统一处理抽屉配置项刷新
const dealConfigRefresh = (data: Recordable, isReload = false) => {
  drawerCurSelectItem.value = data || {}
  getConfigData(
    drawerCurSelectItem.value?.id || outsideSelectGroupId.value,
    isReload
  )
}

// 当前组数据发生改变
const hasDataChange = () => {
  let item = drawerTabsGroupList.value.find(
    (i) => i.id === drawerCurSelectItem.value.id
  )
  item && (item.hasEdit = true)
}

const drawerClose = () => {
  isShowDrawer.value = false
  removeUnfinished()
}

const dealLock = (keys: string[], customKeys: string[]) => {
  nextTick(() => {
    // 使用 Map 提高查找效率
    const keysMap = new Map(keys.map(key => [key, true]))
    const customKeysMap = new Map(customKeys.map(key => [key, true]))

    // 如果包含与keys 该项添加 fixed = true,
    rightDragList.value = rightDragList.value
    .map(item => ({
      ...item,
      fixed: !!keysMap.get(item.key),
      highlight: !!customKeysMap.get(item.key)
    }))
    .sort((a, b) => (a.fixed ? -1 : 1) - (b.fixed ? -1 : 1))
  })
}

/**
 * @description: 获取列表项配置
 * @param groupId 分组id
 * @param isRefresh 是否刷新列表
 */
const allColumnKeys = ref<string[]>([])
const columnKeys = ref<string[]>([])
const lockKeys = ref<string[]>([])
const customColorKeys = ref<string[]>([])

async function getConfigData (groupId: string, isRefresh = false) {
  drawerLoading.value = true
  const res = await getInstitutionOption({
    businessType: businessType.value,
    groupId
  })
  // 若未进行过配置，接口整体返回null，采用原始默认数据
  const data = JSON.parse(res || '{}')
  console.log('[ data2222 ] >', data)
  columnKeys.value = data?.column
    ? data.column
    : getAllKeysTool(formatColumns.value)
  lockKeys.value = data?.lock ? data.lock : []
  customColorKeys.value = data?.customColor ? data.customColor : []
  allColumnKeys.value = data?.allColumnKeys || []
  treeRef.value?.setCheckedKeys(columnKeys.value)

  const finalColumnList: Recordable[] = recursionFilterTool(
    cloneDeep(formatColumns.value),
    (i: Recordable) => {
      // 同时处理固定列的问题
      if (lockKeys.value.includes(i.key)) {
        i.fixed = 'left'
      }else{
        if(data?.lock){
          i.fixed = undefined
        }
      }
      if (customColorKeys.value.includes(i.key)) {
        i.highlight = true
      }
      return columnKeys.value.includes(i.key)
    },
    allColumnKeys.value
  )

  if (isRefresh) {
    const fixedItemArr = finalColumnList.filter((i) => i.fixed)
    const customColorArr: string[] = []

    recursion(
      finalColumnList.filter((i) => i.highlight),
      (item) => {
        customColorArr.push(item.key)
      }
    )
    emit('columnLoaded', finalColumnList, {
      fixedItemArr,
      customColorArr,
      searchObj: { searchData: data?.search || [], saveSearchFn: submit }
    } as ColumnSettingEmitType)
  }

  nextTick(() => {
    dealLock(lockKeys.value, customColorKeys.value)
    drawerLoading.value = false
  })
}

/**
 * @description: 列表项配置保存
 * @param search 搜索条件(兼容搜索条件的保存，例如机构列表)
 */
async function submit (search?: string[]) {
  /**
   * 兼容外部列表上方搜索条件变更
   *    抽屉未打开时，调用该函数只能是变更搜索项，需要跳过该校验
   *    目前仅有"机构列表"有这种情况(2024-11-21)
   */
  if (!rightDragList.value.length && isShowDrawer.value) {
    ElMessage.error('请至少勾选一项！')
    return
  }

  const finalGroupItem = Object.keys(drawerCurSelectItem.value)?.length
    ? drawerCurSelectItem.value
    : outsideGroupList.value.find((i) => i.id === outsideSelectGroupId.value)

  if (!finalGroupItem?.groupName) {
    ElMessage.error('请输入组名称！')
    return
  }

  const lock: string[] = []
  const customColor: string[] = []
  rightDragList.value.forEach((item) => {
    if (item.fixed) {
      lock.push(item.key)
    }
    if (item.highlight) {
      customColor.push(item.key)
    }
  })

  const params = {
    search: search || [],
    column: rightDragTreeRef.value?.getCheckedKeys(true) || columnKeys.value,
    allColumnKeys:
      rightDragTreeRef.value?.getCheckedKeys(false) || allColumnKeys.value,
    lock: lock || lockKeys.value,
    groupId: finalGroupItem?.id || '',
    groupName: finalGroupItem?.groupName,
    customColor: customColor || customColorKeys.value
  }
  await setInstitutionOption({
    businessType: businessType.value,
    fieldJson: JSON.stringify(params)
  })
  ElMessage.success('保存成功！')

  await getColumnGroupList()

  // 默认选中变更项，要同时兼容编辑和新增(无id)
  const targetSelectItem: any = outsideGroupList.value?.find(
    (i) => i.groupName === drawerCurSelectItem.value.groupName
  )
  // 如果修改的是初始选中项本身，需要刷新列表
  const isRefresh = finalGroupItem?.id === outsideSelectGroupId.value
  dealConfigRefresh(targetSelectItem, isRefresh)
}
</script>

<style lang="scss" scoped>
.group-container {
  min-width: 0;
  display: flex;
  align-items: baseline;
  font-size: 14px;
  color: #666666;
  :deep(.el-loading-spinner svg) {
    width: 20px;
  }

  .text-btn {
    display: flex;
    align-items: center;
    font-weight: 400;
    line-height: 16px;
    margin-right: 10px;
    .text {
      white-space: nowrap;
      margin-right: 4px;
    }
    .icon {
      cursor: pointer;
    }
  }
  .tabs-list {
    display: flex;
    flex-wrap: wrap;
    .item {
      max-width: 132px;
      height: 32px;
      line-height: 32px;
      padding: 0 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    .active {
      color: #5687ff;
      background: #eef3ff;
    }
  }
}

.drawer-content {
  position: relative;
  height: 100%;
  display: flex;
  font-size: 14px;
  .list-group-area {
    padding: 8px 16px;
    width: 300px;
    height: 100%;
    .title {
      color: #666666;
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      height: 32px;
      line-height: 32px;

      .btn {
        color: #5687ff;
        cursor: pointer;
      }
    }
    .item {
      display: flex;
      height: 40px;
      align-items: center;
      align-self: stretch;
      border-radius: 4px;
      padding: 0 12px;
      color: #333333;
      &:hover {
        background-color: #eef3ff;
      }
      .rank {
        cursor: move;
      }
      .name {
        flex: 1;
        line-height: 40px;
        height: 100%;
        cursor: pointer;
        :deep(.el-input__wrapper) {
          padding-left: unset;
          background-color: unset;
          box-shadow: unset;
        }
      }
      .icon-shanshu {
        color: #f44d4d;
      }
    }
    .active {
      background: #eef3ff;
    }
    .error {
      border: 1px solid #f44d4d;
    }
  }
  .middle-tree-area {
    height: 100%;
    border-left: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    padding: 8px 16px;
    padding-bottom: 72px;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #666666;
      margin: 7px 0 8px;
      .el-checkbox {
        height: auto;
      }
      .btn {
        color: #5687ff;
        cursor: pointer;
      }
    }
    .custom-input {
      margin-bottom: 12px;
    }
    .field-ul {
      display: flex;
      flex-wrap: wrap;
      overflow-y: auto;
      max-height: calc(100% - 70px);
      :deep(.el-tree) {
        width: 100%;
        .custom-tree-node {
          display: inline-block;
          min-width: 0;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      :deep(.single-level) {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .el-tree-node {
          width: calc(100% / 3);
          padding: 0 8px;
          height: 40px;
          .el-tree-node__content {
            width: 100%;
            display: flex;
          }
          .el-tree-node__expand-icon {
            display: none;
          }
        }
      }
    }
  }
  .rigth-drag-area {
    height: 100%;
    padding: 8px 16px;
    padding-bottom: 62px;
    overflow-x: hidden;

    .title {
      height: 32px;
      line-height: 32px;
      display: flex;
      align-items: center;
      color: #666666;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btn {
        color: #5687ff;
        cursor: pointer;
      }
    }
    .drag-ul {
      height: calc(100% - 48px);
      overflow-y: auto;
      // 解决设置margin导致拖拽报错的问题
      .wrapper-item {
        width: 100%;
        height: 48px;
        display: flex;
        align-items: center;
      }

      .drag-item {
        width: 100%;
        height: 40px;
        display: flex;
        border-radius: 4px;
        align-items: center;
        background-color: #f5f7fa;
        color: #666666;
        padding: 0 12px;

        .rank {
          margin-right: 8px;
        }
        .move {
          cursor: move;
        }

        &:hover {
          background-color: #eef3ff;
          .icon {
            display: block;
          }
        }
        .name {
          color: #666666;
          min-width: 0;
          flex: 1;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .disable {
          color: #aaaaaa;
        }
        .icon {
          display: none;
          color: #f44d4d;
        }
        .custome_icon {
          margin-right: 4px;
          &.active {
            color: #f44d4d;
            transition: all 0.2s;
          }
        }
        .lock {
          color: #999;
          padding-right: 10px;
        }
      }

      .is-first-level {
        &::before {
          content: none;
        }
      }
    }

    :deep(.el-tree) {
      --el-tree-node-hover-bg-color: transparent;

      .el-tree-node {
        position: relative;
        .el-tree-node__content {
          height: 48px;
          border-radius: 4px;
          .el-tree-node__expand-icon {
            // 影响拖拽的高亮线条位置
            /* display: none; */

            /* visibility: hidden;
            pointer-events: none;
            width: 0;
            height: 0;
            padding: unset; */
          }
          /* .is-leaf {
            display: none !important;
          } */
        }
        .el-tree-node__children {
          padding-left: 32px;

          .el-tree-node {
            &::before {
              content: '';
              position: absolute;
              left: -20px;
              top: -12px;
              width: 1px;
              height: calc(100% + 36px);
              background-color: #ebeef5;
            }
            &::after {
              content: '';
              position: absolute;
              left: -20px;
              top: 24px;
              width: 20px;
              height: 1px;
              background-color: #ebeef5;
            }
            &:last-child::before {
              height: 28px;
              top: -3px;
            }
          }
        }
      }

      /* .el-tree__drop-indicator {
        display: none !important;
      } */
    }
  }
  .btn-area {
    z-index: 100;
    border-left: 1px solid #ebeef5;
    border-top: 1px solid #ebeef5;
    background-color: #fff;
    width: 700px;
    height: 64px;
    padding: 16px;
    text-align: right;
    position: absolute;
    right: 0;
    bottom: 0;
  }
}
</style>

<style lang="scss">
.table-column-setting-drawer {
  .rxkDrawer-content {
    width: 1000px;
  }
  .rxkDrawer-footer {
    justify-content: flex-start;
    column-gap: 12px;
  }
}
</style>
