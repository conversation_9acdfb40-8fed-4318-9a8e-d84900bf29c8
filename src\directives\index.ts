/**
 * 全局注册指令
 */
import type { App } from 'vue'
/**
 * 全局指令注册
 */
export default {
  async install (app: App) {
    const directives = import.meta.glob('./modules/*.ts', { eager: true, import: 'default' })
    for (const [key, value] of Object.entries(directives)) {
      // 拼接组件注册的 name
      const arr = key.split('/')
      const directiveName = arr[arr.length - 1].replace('.ts', '')
      // 完成注册
      app.directive(directiveName, (value as any))
    }
  }
}
