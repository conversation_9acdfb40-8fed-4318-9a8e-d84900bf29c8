<template>
  <div class="main-user-manage">
    <div class="left-content">
      <li
        v-for="item in roleList"
        :key="item.id"
        :class="{ ac: permissionManageStore.roleId === item.id }"
        @click="handleSelectRole(item)"
      >
        {{ item.name }}
      </li>
    </div>
    <div class="right-content">
      <div
        class="all"
        v-show="activeName === 'permission' && !permissionManageStore.isSuper"
      >
        <RxkButton
          class="table-action-btn !tw-mr-[24px]"
          :loading="saveLoading"
          text
          @click="handleSave"
        >
          保存
        </RxkButton>
        <RxkButton class="table-action-btn"
                   text
                   @click="getRoleDetail"
        >全局数据权限</RxkButton
        >
      </div>
      <el-tabs v-model="activeName" class="tabs" @tab-click="handleClick">
        <el-tab-pane label="权限设置" name="permission" />
        <el-tab-pane label="用户列表" name="user" />
      </el-tabs>
      <div
        class="tw-flex-1 tw-overflow-y-hidden"
        :class="{ 'tw-pb-[16px]': activeName === 'permission' }"
      >
        <permission-set
          v-show="activeName === 'permission'"
          ref="permissionSetDom"
        />
        <user-list ref="userListDom" v-if="activeName === 'user'" />
      </div>
    </div>
  </div>
  <RxkDialog v-model="globalVisible" title="全局数据权限" width="480px">
    <GlobalDialog
      v-if="globalVisible"
      :data="globalRoleScope"
      :isGlobal="true"
      :department-id-list="globalDepartmentIdList"
      ref="globalPermissionDom"
    />
    <template #footer>
      <div class="footer">
        <RxkButton :disabled="globalLoading"
                   @click="globalVisible = false"
        >取消</RxkButton
        >
        <RxkButton
          type="primary"
          :loading="globalLoading"
          @click="handleGlobalPermissionSure"
        >确定</RxkButton
        >
      </div>
    </template>
  </RxkDialog>
</template>

<script setup lang="ts">
import { RxkDialog } from '@/components/common/RxkDialog'
import GlobalDialog from '@/views/systemManage/permissionManage/globalDialog/globalDialog.vue'
import PermissionSet from './permissionSet/index.vue'
import UserList from './userList/index.vue'
import { nextTick, onBeforeMount, ref } from 'vue'
import {
  getRoleDetailApi,
  getAllRoleListApi,
  setRoleDetailApi
} from '@/apis/permissionManage'
import { usePermissionManageStore } from '@/stores/modules/permission'
import { ElMessage, ElNotification, type TabsPaneContext } from 'element-plus'
import { EDataScope, type Permission } from '@/apis/interface/permissionManage'

const permissionManageStore = usePermissionManageStore()

const permissionSetDom = ref()
const userListDom = ref()
const globalPermissionDom = ref()
const saveLoading = ref<boolean>(false)
const globalLoading = ref<boolean>(false)
const globalVisible = ref<boolean>(false)
const globalRoleScope = ref<Permission.ReqRoleDetailReturn['dataScopeList']>([])
const globalDepartmentIdList = ref<string[]>([])
const roleList = ref<Permission.ReqRoleListReturn[]>([])
const activeName = ref<'permission' | 'user'>('permission')

const handleGlobalPermissionSure = async (isSaved = false) => {
  const data: { department: string[]; dataScopeList: string[] } =
    globalPermissionDom.value.getData()
  if (data.dataScopeList.includes(EDataScope.DEPARTMENT_CUSTOM)) {
    if (!data.department.length) return ElMessage.warning('请选择自定义部门')
  } else {
    data.department = []
  }
  try {
    globalLoading.value = true
    await setRoleDetailApi(
      permissionManageStore.roleId,
      data.dataScopeList,
      data.department
    )
    await permissionSetDom.value?.handleSaveData()
    permissionSetDom.value?.handleSelectRole(permissionManageStore.roleId)
    globalVisible.value = false
  } finally {
    globalLoading.value = false
  }
}

const handleSelectRole = async (item: any) => {
  permissionManageStore.setIsSuper(item)
  permissionManageStore.setRoleId(item.id)
  permissionSetDom.value?.handleSelectRole(item.id)
  userListDom.value?.handleSelectRole(item.id)
}

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}

const handleSave = async () => {
  try {
    saveLoading.value = true
    permissionManageStore.setRefreshFlag(true)
    await permissionSetDom.value?.handleSaveData()
    permissionSetDom.value?.handleSelectRole(permissionManageStore.roleId)
    ElMessage.success('保存成功')
  } catch (e) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const getRoleDetail = async () => {
  const { dataScopeList, departmentIdList } = await getRoleDetailApi(
    permissionManageStore.roleId
  )
  globalRoleScope.value = dataScopeList
  globalDepartmentIdList.value = departmentIdList
  globalVisible.value = true
}

const getRoleList = async () => {
  const res = await getAllRoleListApi()
  roleList.value = res.filter((el: any) => el.state.name === 'ENABLED')
  await handleSelectRole(roleList.value[0])
}

onBeforeMount(() => {
  getRoleList()
})
</script>

<style scoped lang="scss">
.main-user-manage {
  width: 100%;
  height: 100%;
  display: flex;
  min-width: 1200px;
  .left-content {
    flex: 0 140px;
    border-right: 1px solid #ebeef5;
    padding: 8px;
    box-sizing: border-box;
    height: 100%;
    overflow: scroll;
    li {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      height: 40px;
      line-height: 40px;
      padding: 0 12px;
      align-items: center;
      gap: 12px;
      align-self: stretch;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
      margin-bottom: 2px;

      &.ac,
      &:hover {
        color: #5687ff;
        background-color: #eef3ff;
      }
    }
  }

  .right-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    position: relative;
    background: #fff;
    .all {
      z-index: 1;
      position: absolute;
      right: 10px;
      top: 12px;
    }

    .tabs {
      width: 100%;
      :deep(.is-active) {
        font-weight: bold;
      }
      :deep(.el-tabs__item) {
        height: 56px;
      }
      :deep(.el-tabs__header) {
        margin: 0;
      }
      :deep(.el-tabs__nav-scroll) {
        padding: 0 16px;
      }
      :deep(.el-tabs__nav-wrap::after) {
        height: 1px;
      }
      :deep(.el-tabs__content) {
        height: 0;
      }
    }
  }

  .el-form--inline .el-form-item {
    margin-right: 40px;
  }
}
.footer {
  height: 64px;
  line-height: 64px;
  padding: 0 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
