<template>
  <el-tabs v-model="activeName" class="permissions-tab">
    <el-tab-pane v-for="item in tabMenuList"
                 :key="item.name"
                 :label="item.label"
                 :name="item.name" />
  </el-tabs>
</template>

<script setup lang="ts">
import { onMounted, unref, computed, type PropType } from 'vue'
import { useResource } from '@/hooks/useResource'
import type { ListItem } from './type'

const { getMenuAuth } = useResource()
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  isPermission: {
    type: Boolean,
    default: true
  },
  list: {
    type: Object as PropType<ListItem[]>,
    default: () => {[]}
  }
})
const emit = defineEmits(['update:modelValue'])

const activeName = computed({
  get () {
    return props.modelValue
  },
  set (newVal) {
    emit('update:modelValue', newVal )
  }
})

const tabMenuList = computed(() => {
  return props.isPermission ? props.list.filter(item => getMenuAuth(item.permissionCode)) : props.list
})
onMounted(() => {
  const tableList = unref(tabMenuList)
  if (tableList.length) {
    activeName.value = tableList[0].name
  }
})
</script>

<style scoped lang="scss">

.permissions-tab{
  --el-tabs-header-height:56px;
  :deep(.el-tabs__header){
    padding-left: 16px;
    margin-bottom: 0;
  }
  :deep(.el-tabs__nav-wrap){
    &:after{
      height:1px;
      background: #EBEEF5;
    }
  }
}
</style>
