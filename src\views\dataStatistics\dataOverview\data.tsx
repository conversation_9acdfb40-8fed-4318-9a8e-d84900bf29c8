import { ref } from 'vue'
import type { ColumnType } from '@/types/table'
import type { FormSchema } from '@/components/business/searchFilter/src/type'
import timeUtils from '@/utils/libs/time'

export const pageConfig = {
  searchFormData: ref<FormSchema[]>([
    {
      key: 'timeRange',
      fieldName: '日期范围',
      component: 'DateRange',
      val: timeUtils.transTypeToTime(20, 'yyyy-MM-dd')
    }
  ]),

  columns: ref<ColumnType[]>([
    {
      title: '日期',
      key: 'day',
      width: 120,
      fixed: 'left'
    },
    {
      title: '总消耗',
      key: 'consumeAmount'
    },
    {
      title: '访问PV|UV',
      key: 'linkVisitPv,linkVisitUv',
      width: 150,
      render ({ cellData }){
        return <>
          <span>{`${cellData.linkVisitPv} | ${cellData.linkVisitUv}`}</span>
        </>
      }
    },
    {
      title: 'H5登录总|登录率',
      key: 'h5LoginAll,h5LoginRate',
      width: 150,
      render ({ cellData }){
        return <>
          <span>{`${cellData.h5LoginAll} | ${cellData.h5LoginRate}`}</span>
        </>
      }
    },
    {
      title: 'H5下载总|下载率',
      key: 'operateAppDownloadUv,downloadRate',
      width: 150,
      render ({ cellData }){
        return <>
          <span>{`${cellData.operateAppDownloadUv} | ${cellData.downloadRate}`}</span>
        </>
      }
    },
    {
      title: 'APP登录总|新',
      key: 'appLoginAll,appLoginNewUv',
      width: 120,
      render ({ cellData }){
        return <>
          <span>{`${cellData.appLoginAll} | ${cellData.appLoginNewUv}`}</span>
        </>
      }
    },
    {
      title: 'APP登录率',
      key: 'appLoginRate',
      width: 120
    },
    {
      title: 'APP留资总|新',
      key: 'appCapitalApplyUv,appCapitalApplyNewUv',
      width: 120,
      render ({ cellData }){
        return <>
          <span>{`${cellData.appCapitalApplyUv} | ${cellData.appCapitalApplyNewUv}`}</span>
        </>
      }
    },
    {
      title: 'APP留资率',
      key: 'appCapitalRate',
      width: 120
    },
    {
      title: 'APP初筛通过总',
      key: 'appFirstFilterUv',
      width: 120
    },
    {
      title: 'APP初筛通过率',
      key: 'appFirstFilterRate',
      width: 120
    },
    {
      title: 'APP预审申请总',
      key: 'appPreAuditApplyUv',
      width: 120
    },
    {
      title: 'APP预审申请率',
      key: 'appPreAuditApplyRate',
      width: 120
    },
    {
      title: 'APP电核总',
      key: 'appPhoneAuditUv',
      width: 120
    },
    {
      title: 'APP电核通过率',
      key: 'appPhoneAuditPassRate',
      width: 120
    },
    {
      title: 'APP授信总',
      key: 'appCreditUv',
      width: 120
    },
    {
      title: 'APP授信通过率',
      key: 'appCreditPassRate',
      width: 120
    },
    {
      title: 'APP放款总',
      key: 'appLoanUv',
      width: 120
    },
    {
      title: 'APP放款通过率',
      key: 'appLoanPassRate',
      width: 120
    },
    {
      title: '线索转化率',
      key: 'clueTransRate',
      width: 120
    },

    {
      title: 'APP初筛通过数|APP初筛拒绝数',
      key: 'appFirstScreenPassPv,appFirstScreenRefusePv',
      width: 260,
      render ({ cellData }){
        return <>
          <span>{`${cellData.appFirstScreenPassPv} | ${cellData.appFirstScreenRefusePv}`}</span>
        </>
      }
    },
    {
      title: 'APP初筛通过用户数|APP初筛拒绝用户数',
      key: 'appFirstScreenPassUv,appFirstScreenRefuseUv',
      width: 280,
      render ({ cellData }){
        return <>
          <span>{`${cellData.appFirstScreenPassUv} | ${cellData.appFirstScreenRefuseUv}`}</span>
        </>
      }
    },
    {
      title: 'APP申请预审链接数|APP获取预审链接数',
      key: 'appApplyPreAuditLinkPv,appFirstFilterPv',
      width: 280,
      render ({ cellData }){
        return <>
          <span>{`${cellData.appApplyPreAuditLinkPv} | ${cellData.appFirstFilterPv}`}</span>
        </>
      }
    },
    {
      title: 'APP申请预审链接用户数|APP获取预审链接用户数',
      key: 'appApplyPreAuditLinkUv,appFirstFilterUv',
      width: 320,
      render ({ cellData }){
        return <>
          <span>{`${cellData.appApplyPreAuditLinkUv} | ${cellData.appFirstFilterUv}`}</span>
        </>
      }
    }
  ])
}
