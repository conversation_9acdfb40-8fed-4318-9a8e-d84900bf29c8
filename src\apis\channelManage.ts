import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
import type { Channel } from '@/apis/interface/ryhInterface'
const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

// 渠道黑名单详情
export const getChannelBlackDetail = (data: { id: string }): Promise<Recordable> =>
  request.get<Recordable>('/ryh-channel/channel/blacklist/detail', data, jsonHeaderConfig)

// 渠道黑名单编辑
export const updateChannelBlackDetail = (data: Recordable) =>
  request.post('/ryh-channel/channel/blacklist/save', data, jsonHeaderConfig)

// 渠道黑名单获取渠道列表
export const getChannelListForBlack = (): Promise<Array<{ channelId: string; channelName: string }>> =>
  request.get<Array<{ channelId: string; channelName: string }>>('/ryh-channel/channel/blacklist/channelList', {}, jsonHeaderConfig)

// 渠道黑名单获取渠道列表
export const getTenantsForBlack = (): Promise<Array<{ tenantId: string; tenantName: string }>> =>
  request.get<Array<{ tenantId: string; tenantName: string }>>('/ryh-channel/channel/blacklist/tenantList', {}, jsonHeaderConfig)

// 批量添加黑名单
export const batchChannelBlackList = (data: Recordable) =>
  request.post('/ryh-channel/channel/blacklist/saveBatch', data, jsonHeaderConfig)

// 黑名单启用禁用
export const blackListEnable = (data: Recordable) =>
  request.post('/ryh-channel/channel/blacklist/enableOrDisable', data, jsonHeaderConfig)

// 批量获取机构下产品
export const getProductListByTenantId = (data: {tenantId: string}): Promise<Array<{ productId: string; productName: string }>> =>
  request.get<Array<{ productId: string; productName: string }>>('/ryh-channel/channel/blacklist/productList', data, jsonHeaderConfig, { ignoreCancelToken: true })

// 渠道管理-渠道商链接信息流渠道统计分页数据
export const channelMerchantInfoFLowCount = (data: any): Promise<Channel[]> =>
  request.post<Channel[]>(
    '/ryh-statistics/channel/channelMerchantInfoFLowCount',
    data,
    jsonHeaderConfig
  )

// 渠道管理-渠道商链接统计数据导出
export const channelMerchantCountExport =
  '/ryh-statistics/channelMerchantCountExport'
