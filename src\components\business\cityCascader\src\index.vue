<template>
  <el-cascader
    class="city-selector"
    v-model="input"
    placeholder="请选择"
    :options="cityList"
    filterable
    clearable
    collapse-tags
    collapse-tags-tooltip
    :props="cascaderProps"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import province from 'province-city-china/dist/level.json'
import citys from 'province-city-china/dist/city.json'
import type { PickerOption } from './type.ts'
import type { CascaderValue } from 'element-plus'
const props = defineProps({
  modelValue: {
    type: [Array, String]
  },
  cascaderProps: {
    type: Object,
    default: () => {
      return {
        multiple: true
      }
    }
  }, // 需要禁用的城市
  disabledCities: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 自定义城市列表
  inCityList: {
    type: Array,
    default: () => ([])
  },
  // 是否使用自定义城市
  showInCity: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue'])
const input = computed({
  get () {
    return props.modelValue as CascaderValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})
const cascaderProps = props.cascaderProps
const cityIsDisabled = (city:string) => {
  return props.disabledCities.includes(city)
}
const cityList = computed<Recordable[]>(() => {
  const municipalitys = [
    '北京市',
    '上海市',
    '天津市',
    '重庆市',
    '台湾省',
    '香港特别行政区',
    '澳门特别行政区'
  ]
  if (props.showInCity) {
    const cityArr = props.inCityList as string[]
    const dataArr = citys.filter(i => cityArr.includes(i.name))
    let resultArr = province.map(item => {
      let children = []
      // 直辖市处理
      if (municipalitys.includes(item.name)) {
        children = cityArr
        .filter(i => i === item.name)
        .map(k => ({ label: k, value: k, disabled: cityIsDisabled(k) }))
      } else {
        children = dataArr
        .filter(i => i.province === item.province)
        .map(k => ({ label: k.name, value: k.name, disabled: cityIsDisabled(k.name) }))
      }
      return {
        label: item.name,
        value: item.name,
        children
      }
    })
    return resultArr.filter(item => item.children.length)
  } else {
    const list: PickerOption[] = province.map((provin) => {
      const { name } = provin
      const obj: PickerOption = {
        label: name,
        value: name,
        children: []
      }
      if (municipalitys.includes(name)) {
        provin.children = [
          {
            area: '',
            city: '',
            code: '',
            name,
            province: ''
          }
        ]
      }
      obj.children = provin.children.map((city) => {
        const cityName = city.name
        return {
          label: cityName,
          value: cityName,
          disabled: cityIsDisabled(city.name)
        }
      })
      return obj
    })
    return list
  }
})
</script>