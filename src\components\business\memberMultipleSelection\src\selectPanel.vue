<template>
  <div class="personnelSelectionPanel">
    <div class="top">
      <div class="title">成员多选</div>
      <i @click="close"
         class="iconfont icon-close"/>
    </div>
    <div class="content">
      <el-tabs 
        v-model="activeName"
        @tab-change="handleChange"
        class="personnelSelectionPanelTabs">
        <el-tab-pane 
          label="组织架构"
          name="organizational">
          <Organizational
            ref="organizationalRef"
            :data="componentData.deComponentTreeVO"
            :userResignList="componentData.userResignVOList"
            :orginComponentData="orginComponentData.deComponentTreeVO"
            :checkedUser="checkedUser"
            @updateValue="updateValue"/>
        </el-tab-pane>
        <el-tab-pane 
          label="角色"
          name="role">
          <Role 
            ref="roleRef"
            v-model:data="componentData.roleVOList"
            :checkedUser="checkedUser"
            @updateValue="updateValue"/>
        </el-tab-pane>
        <el-tab-pane 
          label="成员"
          name="members">
          <Members 
            ref="membersRef" 
            v-model:data="componentData.userVOList"
            :checkedUser="checkedUser"
            @updateValue="updateValue"/>
        </el-tab-pane>
        <el-tab-pane 
          v-if="configuration"
          label="动态参数"
          name="dynamicParameters">
          <slot name="dynamic">
            <DynamicParametersVue
              :data="dynamicParameters"
              @updateParameter="updateParameter"/>
          </slot>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="bottom">
      <div class="left">
        <div v-if="activeName === 'organizational'">
          <el-checkbox 
            v-model="saveDefult"
            v-if="showDefault"
            label="保存默认值" />
          <el-checkbox 
            v-model="allChecked"
            label="选中所有可见选项" />
          <el-checkbox 
            v-model="isIncludeChild"
            label="父部门不包含子部门员工" />
        </div>
      </div>
      <div class="right">
        <RxkButton 
          @click="close">取消</RxkButton>
        <RxkButton type="primary" @click="confirm">确定</RxkButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, unref } from 'vue'
import type { RxkButton } from '@/components/common/RxkButton'
import Organizational from './components/organizationalPanel.vue'
import Members from './components/membersPanel.vue'
import DynamicParametersVue from './components/dynamicParameters.vue'
import Role from './components/rolePanel.vue'
import type { BasicProps, ComponentData, UserVOList, DynamicParameters } from './type'
import type { PanelActionType } from './hooks/usePanel'
import { isArray, isFunc } from '@/utils/is'
import { cloneDeep, recursion } from '@/utils/tools'

const emit = defineEmits(['close', 'confirm', 'registerPanel', 'update'])
const saveDefult = ref<boolean>(false) // 保存默认值
const organizationalRef = ref()
const roleRef = ref()
const membersRef = ref()

const activeName = ref('members')
const isIncludeChild = ref<boolean>(false) // 父部门不包含子部门员工
const configuration = ref<boolean>(false) // 是否展示动态参数配置
const showDefault = ref<boolean>(false) // 是否展示保存默认值
const allChecked = ref<boolean>(false) // 选中所有可见选项
const componentData = ref<ComponentData>({})
const checkedUser = ref<UserVOList[]>([])
const cloneCheckedUser = ref<UserVOList[]>([])
const dynamicParameters = ref<DynamicParameters>({
  acquireCurrent: 0,
  acquireCurrentFather: 0
})
const orginPropsData = ref<BasicProps>({})
const orginComponentData = ref<ComponentData>({})

async function reload (props: BasicProps, fn?:Fn) {
  // 默认没有触发
  console.log('orginPropsDataReload', cloneCheckedUser.value)
  console.log(JSON.stringify(orginComponentData.value) === JSON.stringify(props.data))
  const data = cloneDeep(cloneCheckedUser.value)
  handleChange(activeName.value)
  updateValue(data)
  fn && fn()
}

async function init (propsValue: BasicProps, fn?:Fn) {
  console.log(propsValue, '多选init')
  orginPropsData.value = cloneDeep(propsValue)
  console.log(propsValue, 'propsValuepropsValueMember', orginPropsData.value)
  activeName.value = propsValue.activeName || 'members'
  isIncludeChild.value = cloneDeep(propsValue.isIncludeChild || false)
  configuration.value = propsValue.configuration || false
  showDefault.value = propsValue.showDefault || false
  allChecked.value = propsValue.selectedShow || false
  dynamicParameters.value = propsValue.dynamicParameters || {
    acquireCurrent: 0,
    acquireCurrentFather: 0
  }
 
  // 获取数据
  const { data, api, menuId } = unref(orginPropsData.value)
  if(!api && data) {
    componentData.value = cloneDeep(data)
    orginComponentData.value = cloneDeep(data)
    // 处理部门人数数据
    handleDepartData()
    await handleCheckedValue(propsValue.showValueData || [])
    return false
  }
  if (!api || !isFunc(api)) return
  try {
    console.log(menuId, 'menuIdmenuId')
    const res = await api({ checkChildFlag: true, ...propsValue.paramsData, disableUserIds: propsValue.showValueData && propsValue.showValueData?.map(item => item.id) || [] }, menuId ? { MenuId: menuId } : '')
    if(isArray(res)) {
      componentData.value = cloneDeep(res[0])
      orginComponentData.value = cloneDeep(res[0])
    } else {
      componentData.value = cloneDeep(res)
      orginComponentData.value = cloneDeep(res)
    }
    await handleDepartData()
    await handleCheckedValue(propsValue.showValueData || [])
    fn && fn()
  }catch (err) {
    console.log(err)
  }
}

// 处理回显值
async function handleCheckedValue (showValueData: UserVOList[]) {
  let arr: UserVOList[] = []
  if (showValueData && showValueData.length > 0) {
    const ids = new Set(showValueData.map(item => item.id))
    const mergedUsers = [...(orginComponentData.value?.showUserList || []), ...(orginComponentData.value?.userVOList || [])]
    const userMap = new Map<string, UserVOList>()
    mergedUsers.forEach(user => {
      if (!userMap.has(user.id)) {
        userMap.set(user.id, user)
        if (ids.has(user.id)) {
          arr.push(user)
        }
      }
    })
  }
  console.log(arr, '最终回显值')
  cloneCheckedUser.value = arr
  updateValue(arr)
  emit('update', arr)
}

// 处理部门人数数据
async function handleDepartData () {
  const data = cloneDeep(orginComponentData.value.deComponentTreeVO)
  recursion(data, (item) => {
    item.indeterminate = false
    item.checked = false
    const len = []
    if (isIncludeChild.value) {
      item.len = item.userVOList.length
    } else {
      recursion(item.children, (children) => {
        item.userVOList.push(...children.userVOList)
      })
      item.len = len.length + item.userVOList.length
    }
  })
  componentData.value.deComponentTreeVO = cloneDeep(data)
  console.log(componentData.value.deComponentTreeVO, 'oooo')
}

const panelActionType: PanelActionType = {
  init,
  getCheckedUser,
  updateValue,
  getSelectedShow,
  getContainChildren,
  getSaveDefault,
  getParameter,
  reload,
  handleCheckedValue
}
emit('registerPanel', panelActionType)

function close () {
  emit('close')
  const clone = cloneDeep(cloneCheckedUser.value)
  organizationalRef.value.clear()
  roleRef.value.clear()
  emit('update', clone)
}

watch(isIncludeChild, async (val) => {
  await handleDepartData()
  organizationalRef.value.handleIncludeChild(val)
})

watch(allChecked, (val) => {
  organizationalRef.value.handleAllCheck(val)
})

function confirm (){
  cloneCheckedUser.value = cloneDeep(checkedUser.value)
  organizationalRef.value.clear()
  roleRef.value.clear()
  emit('confirm')
}

function updateValue (data: UserVOList[], type?: string){
  console.log(data, 'updateValue')
  checkedUser.value = data
  if(type && type === 'clear') {
    cloneCheckedUser.value = cloneDeep(checkedUser.value)
  }
}

function getSelectedShow () {
  return unref(allChecked.value)
}
function getContainChildren () {
  return unref(isIncludeChild.value)
}
function getSaveDefault () {
  return unref(saveDefult.value)
}
function getCheckedUser () {
  return unref(checkedUser.value)
}
function updateParameter (params: DynamicParameters) {
  dynamicParameters.value = params 
}
function getParameter () {
  return unref(dynamicParameters.value)
}
function handleChange (activeName: string) {
  switch(activeName) {
    case 'organizational':
      organizationalRef.value.updateCheck(checkedUser.value)
      break
    case 'role':
      roleRef.value.updateCheck(checkedUser.value)
      break
    case 'members':
      membersRef.value.updateCheck(checkedUser.value)
      break
  }
}
</script>

<style lang="scss" scoped>
.personnelSelectionPanel {
  max-height: 50vh;
  overflow-y: hidden;
  .top {
    @include flex-center(row, space-between, center);
    padding: 17px 24px;
    border-bottom: 1px solid #EBEEF5;
    .title {
      font-weight: 700;
      color: $gray;
      font-size: $saas-font-size-small;
    }
    .icon-close {
      cursor: pointer;
    }
  }
  .content {
    max-height: calc(50vh - 120px);
    height: 400px;
    overflow-y: auto;
    // @include flex-center(row, normal, normal);
    border-bottom: 1px solid #EBEEF5;
    padding: 0 16px;
    .personnelSelectionPanelTabs {
      width: 100%;
      height: 100%;
      --el-tabs-header-height: 48px !important;
      :deep(.el-tabs__header) {
        margin: 0;
      }
      :deep(.el-tabs__content) {
        height: calc(100% - 48px);
      }
      :deep(.el-tabs__nav) {
        transform: translateX(16px) !important;
      }
      :deep(.el-tab-pane) {
        height: 100%;
      }
      :deep(.el-tabs__active-bar) {
        background-color: $primary-color;
      }
      :deep(.el-tabs__item.is-active) {
        color: $primary-color;
        font-weight: 700;
      }
      :deep(.el-tabs__item) {
        color: $secondary-text-color;
      }
      :deep(.el-tabs__nav-wrap::after) {
        height: 1px;
        background-color: #EBEEF5;
      }
    }
    .left {
      width: 250px;
      border-right: 1px solid #EBEEF5;
      overflow-y: auto;
    }
  }
  .bottom {
    padding: 16px 24px;
    @include flex-center(row, space-between, center);
    .left {
      :deep(.el-checkbox) {
        margin-right: 16px;
      }
    }
  }
}
</style>
