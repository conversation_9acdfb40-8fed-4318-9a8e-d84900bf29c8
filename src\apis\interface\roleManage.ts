import type { ReqPage } from './index'
// * 用户管理模块
export namespace Role {
  export interface ReqRoleParams extends ReqPage {
    username: string;
    state: string;
    departmentId: number;
  }

  export interface ReqAddRoleParams {
    id?: string;
    code: 1 | 0;
    name: string;
    remarks: string;
    roleType: 'ADMIN' | 'SUPER';
  }

  export interface ReqEditRoleStateParams {
    id: string;
    state: 'DISABLED' | 'DRAFT' | 'ENABLED'
  }

  export interface ReqGetRoleParams extends ReqPage {
    count: boolean;
    model: {
      roleName: string;
    };
    orderBys: {
      field: string;
      sort: string;
    }[];
    searches: string;
  }

  export interface ReqGetRoleReturn {
    id: string;
    name: string;
    remarks: string;
    state: {
      code: 1 | 0
      name: 'DISABLED' | 'DRAFT' | 'ENABLED';
      value: '启用' | '禁用';
    }
  }
}