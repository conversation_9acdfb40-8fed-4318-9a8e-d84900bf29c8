import request from '@/utils/axios'
import { ContentTypeEnum } from '@/enums/axios'
import type { Permission } from '@/apis/interface/permissionManage'

/**
 * @name 角色管理
 */

const jsonHeaderConfig = {
  headers: {
    'Content-Type': ContentTypeEnum.JSON
  }
}

// 获取角色列表（含超管）
export const getAllRoleListApi = () => {
  return request.post('/admin/role/list/all', null, jsonHeaderConfig)
}

// 获取角色的权限菜单
export const getRoleMenuApi = (roleId: string) => {
  return request.post(
    '/admin/role/menuList?systemCode=BASE&roleId=' + roleId,
    null,
    jsonHeaderConfig
  )
}

// 设置菜单权限
export const setRoleMenuPermissionApi = (params: any) => {
  return request.post('/admin/role/permissions', params, jsonHeaderConfig)
}

// 移除角色下用户
export const removeRoleUserApi = (userId: string, roleId: string) => {
  return request.post(
    `/admin/user/removeRoleUser?userId=${userId}&roleId=${roleId}`,
    null,
    jsonHeaderConfig
  )
}

// 获取配置数据权限列表
export const getPermissionListApi = () => {
  return request.get('/admin/resource/listDataScope', null, jsonHeaderConfig)
}

// 设置菜单权限
export const setDataScopeApi = (params: Permission.ReqScopeDataParams) => {
  return request.post('/admin/role/dataScope', params, jsonHeaderConfig)
}

// 设置脱敏字段
export const setDesensitizeApi = (data: Permission.ReqSetDesensitizeParams) => {
  return request.post(
    '/admin/application/configDesensitize',
    data,
    jsonHeaderConfig
  )
}

// 获取脱敏字段
export const getDesensitizeApi = (
  roleId: string
): Promise<Permission.ReqGetDesensitizeReturn[]> => {
  return request.post('/admin/application/manage/field/list', { roleId })
}

// 获取角色详情
export const getRoleDetailApi = (
  id: string
): Promise<Permission.ReqRoleDetailReturn> => {
  return request.post(`/admin/role/details?id=${id}`, null, jsonHeaderConfig)
}

// 设置角色详情
export const setRoleDetailApi = (
  roleId: string,
  dataScopeList: string[],
  departmentIdList: string[]
) => {
  return request.post(
    '/admin/role/all/dataScope',
    { roleId, dataScopeList, departmentIdList },
    jsonHeaderConfig
  )
}

// 获取产品价格权限
export const getProductPricePermissionApi = (roleId: string) => {
  return request.get('/admin/application/findProductPrice', { roleId })
}

// 配置产品价格权限
export const setProductPricePermissionApi = (params: any) => {
  return request.post(
    '/admin/application/configProductPrice',
    params,
    jsonHeaderConfig
  )
}

// 查看当前用户的产品价格权限
export const getCurrentUserProductPricePermission = () => {
  return request.get('/admin/application/current/productPrice', null)
}
