.base-table-page {
  width: 100%;
  height: 100%;

  &.reHeight {
    height: calc(100% - 65px);
    padding: 0 16px;
    min-height: 120px;
  }
  &.showfootheight{
    height: calc(100% - 110px);
    padding: 0 16px;
  }

  .custom-table {
    //border-left: 1px solid #EBEEF5;
    //border-right: 1px solid #EBEEF5;
    max-height: 100%;
    height: 100%;

    .vxe-table--border-line {
      display: block;
    }
    .vxe-table--render-wrapper {
      height: 100%;

      .vxe-table--main-wrapper {
        height: 100%;

        .vxe-table--body-wrapper {
          height: calc(100% - 44px);
          min-height: 40px !important;
        }
      }

      .vxe-table--fixed-wrapper {
        .vxe-table--body-wrapper {
          height: calc(100% - 40px);
        }
      }
    }

    .vxe-body--row {
      .vxe-body--column {
        padding: 0;
        height: 40px;
        border-bottom: 1px solid #EBEEF5;
        &:first-child {
          border-left: 1px solid #EBEEF5;
        }
        &:last-child {
          border-right: 1px solid #EBEEF5;
        }
        .vxe-cell--checkbox {
          &.is--disabled {
            .vxe-checkbox--icon {
              background-color: var(--vxe-input-disabled-background-color);
              border-radius: 4px;
            }
          }
        }
      }
    }
  }

  .el-table__body {

    //border-left: 1px solid #EBEEF5;
    //border-right: 1px solid #EBEEF5;
    .el-table__cell {
      padding: 0;
      height: 40px;
      line-height: 40px;
      border-right-color: transparent;

      .cell {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #333333;
      }
    }
  }

  .vxe-header--row {
    .vxe-header--column {
      padding: 0;
      height: 40px!important;
      line-height: 22px;
      background-image: linear-gradient(var(--vxe-table-border-color), var(--vxe-table-border-color)), linear-gradient(var(--vxe-table-border-color), var(--vxe-table-border-color));
      background-repeat: no-repeat;
      background-size: var(--vxe-table-border-width) 100%, 100% var(--vxe-table-border-width);
      background-position: 100% 0, 100% 100%;

      // .vxe-resizable {
      //   display: none;
      // }
      .vxe-resizable.is--line::before {
        width: auto;
      }
    }

    .vxe-cell--title {
      width: 100%;
      display: inline-block;
    }
  }

  .custom-header {
    padding: 0;
    height: 40px;
    line-height: 40px;
    background: #F4F4F5 !important;

    //border-left: 1px solid #EBEEF5;
    //&:first-child{
    //  border-left: none;
    //}
    .cell {
      line-height: 40px;
      font-size: 14px;
      color: #666666;
      font-weight: 700;
    }
  }
}



.el-loading-mask {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.border-top{
 border-top: 1px solid #EBEEF5;
}
.pagination-box {
  display: flex;
  justify-content: space-between;
  padding: 0 24px;
 

  .custom-pagination {
    height: 64px;
    &-mini {
    --el-pagination-font-size: 12px;
    }
  }
}
.paginationshowfoot{
  padding-top:  45px;
}

// TODO: 与button按钮loading样式冲突
.is-loading {
  //color: #5687ff;
}

.operate {
  //border-left: 1px solid #EBEEF5;
}
.vxe-table--tooltip-wrapper{
  max-width: 900px;
  .vxe-table--tooltip-content{
    line-height: normal;
  }
}