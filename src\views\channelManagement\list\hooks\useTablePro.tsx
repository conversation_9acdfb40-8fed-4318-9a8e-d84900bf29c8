import type { TablePro } from '../type'
import { ElDatePicker } from 'element-plus'
import { reactive } from 'vue'
import { useDateRangePicker } from '@/hooks/useDateRangePicker'
import { getTenantsForBlack } from '@/apis/channelManage'
import { RemoteSelect } from '@/components/business/remoteSelect'
import { useCommonEnum } from './useCommonEnum'
const { commonTypeField, enumSwitch } = useCommonEnum()
const { calendarChange, disabledDate, visibleChange } = useDateRangePicker()

export function useTablePro (type?:number){
  const allOptions = reactive({})
  const handleSetOptions = (key: string, value: any) => {
    allOptions[key] = value
  }
  const getLabel = (options: any[], currentValue: any) => {
    const currentItem =
    options && options.length
      ? options.find((item: { value: any }) => item.value === currentValue)
      : ''
    return currentItem ? currentItem['label'] : '-'
  }
  /** 渠道组列表 */
  const groupListPro:TablePro = {
    searchFormData: [
      {
        fieldName: '渠道组名称',
        component: 'Input',
        key: 'channelGroupName',
        val: ''
      }
    ],
    columns: [
      {
        key: 'channelGroupName',
        title: '渠道组名称'
      },
      {
        key: 'channelTypeName',
        title: '渠道组类型'
      },
      {
        key: 'channelNames',
        title: '渠道列表'
      },
      {
        key: 'operate',
        title: '操作',
        fixed: 'right',
        width: 270,
        slot: 'operateSlot'
      }
    ]
  }
  /** 信息流渠道--Link */
  const informationLinkListPro:TablePro = {
    searchFormData: [
      {
        key: 'time',
        component: 'Render',
        val: '',
        fieldName: '',
        componentProps: {
          valueFormat: 'YYYYMMDD'
        },
        render: (modalValue:any) => {
          return type === 0 ? <ElDatePicker
            style="width: 100%"
            v-model={modalValue}
            type="daterange"
            range-separator="-"
            format="YYYY-MM-DD"
            value-format="YYYYMMDD"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable={false}
            disabledDate={(e: Date) => disabledDate(e, 30)}
            onCalendarChange={calendarChange}
            onVisibleChange = {visibleChange}
          /> : <ElDatePicker
            style="width: 100%"
            v-model={modalValue}
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYYMMDD"
            placeholder="请选择日期"
            clearable={true}
          />
        }
      }
    ],
    columns: [
      {
        key: 'day',
        title: '日期'
      },
      {
        key: 'channelId',
        title: '渠道ID'
      },
      {
        key: 'channelName',
        title: '渠道名称'
      },
      {
        key: 'visitUv',
        title: '访问UV'
      },
      {
        key: 'registPageUserAll',
        title: 'H5登录'
      },
      {
        key: 'visitLoginRate',
        title: '访登率'
      },
      {
        key: 'h5RetentionUv',
        title: 'H5留资'
      },
      {
        key: 'retentionRatePercent',
        title: '留资率'
      },
      {
        key: 'totalProfit',
        title: '收益'
      }
      // {
      //   key: 'operate',
      //   title: '操作',
      //   fixed: 'right',
      //   width: 270,
      //   slot: 'informationLinkOperateSlot'
      // }
    ]
  }
  /** 其他类型--Link */
  const otherLinkListPro:TablePro = {
    searchFormData: [
      {
        key: 'time',
        component: 'Render',
        val: '',
        fieldName: '',
        componentProps: {
          valueFormat: 'YYYYMMDD'
        },
        render: (modalValue:any) => {
          return type === 0 ? <ElDatePicker
            style="width: 100%"
            v-model={modalValue}
            type="daterange"
            range-separator="-"
            format="YYYY-MM-DD"
            value-format="YYYYMMDD"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable={false}
            disabledDate={(e: Date) => disabledDate(e, 30)}
            onCalendarChange={calendarChange}
            onVisibleChange = {visibleChange}
          /> : <ElDatePicker
            style="width: 100%"
            v-model={modalValue}
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYYMMDD"
            placeholder="请选择日期"
            clearable={true}
          />
        }
      }
    ],
    columns: [
      {
        key: 'day',
        title: '日期'
      },
      {
        key: 'channelId',
        title: '渠道ID'
      },
      {
        key: 'channelName',
        title: '渠道名称'
      },
      {
        key: 'settlementNums',
        title: '结算数'
      }
    ]
  }
  /** 黑名单定向 机构列表 */
  const blackAgencyListPro:TablePro = {
    searchFormData: [
      {
        key: 'tenantId',
        fieldName: '机构ID',
        component: 'Input',
        val: ''
      },
      {
        key: 'tenantShortName',
        component: 'Input',
        val: '',
        fieldName: '机构简称'
      },
     
      {
        key: 'tenantId2',
        component: 'Select-v2',
        val: '',
        fieldName: '机构名称',
        options: [],
        getSelectApi: getTenantsForBlack,
        originalfieldName: {
          label: 'tenantName',
          value: 'tenantId'
        },
        componentProps: {
          clearable: true
        }
      },
      {
        key: 'channelId',
        fieldName: '渠道ID',
        component: 'Input',
        val: ''
      },
      {
        key: 'status',
        component: 'Select',
        val: '',
        fieldName: '状态',
        options: [{ label: '开启', value: 0 }, { label: '关闭', value: 1 }],
        componentProps: {
          clearable: true
        }
      }
    ],
    columns: [
      {
        key: 'tenantId',
        title: '机构ID',
        width: 180
      },
      {
        key: 'tenantShortName',
        title: '机构简称'
      },
      {
        key: 'tenantName',
        title: '机构名称'
      },
      {
        key: 'channelNum',
        title: '屏蔽渠道数',
        width: 120
      },
      {
        key: 'channelBlockNum',
        title: '自动屏蔽渠道数',
        width: 120
      },
      {
        key: 'city',
        title: '获客城市'
      },
      {
        key: 'updateTime',
        title: '更新时间',
        width: 180
      },
      {
        key: 'operator',
        title: '操作人'
      },
      {
        key: 'operate',
        title: '操作',
        fixed: 'right',
        width: 150,
        slot: 'operateSlot'
      }
    ]
  }
  /** 渠道城市获客统计列表 */
  const cityVisitorStatistics:TablePro = {
    searchFormData: [],
    columns: [
      {
        key: 'city',
        title: '城市'
      },
      {
        key: 'h5LoginUv',
        title: 'H5登录数'
      },
      {
        key: 'capitalUvNew',
        title: '总留资'
      },
      {
        key: 'pushOrderSuccNum',
        title: '推送成功订单'
      },
      {
        key: 'incomeAmount',
        title: '信贷收益'
      }
    ]
  }
  // 信贷api半流程-api链接跳转列表
  const apiLinkListPro:TablePro = {
    searchFormData: [
      {
        key: 'time',
        component: 'Render',
        val: '',
        fieldName: '',
        componentProps: {
          valueFormat: 'YYYYMMDD'
        },
        render: (modalValue:any) => {
          return <ElDatePicker
            style="width: 100%"
            v-model={modalValue}
            type="daterange"
            range-separator="-"
            format="YYYY-MM-DD"
            value-format="YYYYMMDD"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable={false}
            disabledDate={(e: Date) => disabledDate(e, 30)}
            onCalendarChange={calendarChange}
            onVisibleChange = {visibleChange}
          />
        }
      }
    ],
    columns: [
      {
        key: 'channelName',
        title: '渠道名称'
      },
      {
        key: 'day',
        title: '日期'
      },
      {
        key: 'intervalSettlementOrderNum',
        title: '结算总订单数'
      },
      {
        key: 'intervalSettlementAmount',
        title: '结算总金额'
      }
    ]
  }
  return {
    groupListPro,
    informationLinkListPro,
    otherLinkListPro,
    blackAgencyListPro,
    cityVisitorStatistics,
    apiLinkListPro
  }
}
