import type { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig, AxiosError } from 'axios'
export interface CreateOptions extends AxiosRequestConfig {
    transform?: AxiosTransform;
    requestOptions?: RequestOptions;
}

export abstract class AxiosTransform {
  beforeRequestHook?: (config: AxiosRequestConfig, requestOptions: RequestOptions) => AxiosRequestConfig
  afterResponseHook?: (response: AxiosResponse<Result>, requestOptions: RequestOptions) => any
  requestInterceptors?:(config: InternalAxiosRequestConfig, options: CreateOptions) => InternalAxiosRequestConfig
  responseInterceptors?: (res: AxiosResponse<any>) => AxiosResponse<any>
  responseInterceptorsCatch?: (res: AxiosError<any>) => AxiosError<any>
}

export interface RequestOptions {
    /** 接口地址, 由不同环境提供,相当于axios的baseUrl */
    apiUrl?: string;
    /** 请求地址前缀, 由不同环境提供, */
    urlPrefix?: string;
    /** 是否加时间戳 */
    isJoinTimestamp?: boolean;
    /** 是否需要签名 */
    isNeedSign?: boolean;
    /** 是否开起防止相同接口重复请求 */
    repeatRequest?: boolean;
    /** ! 忽略重复请求，如果不设置，则如果遇到重复请求时，就会报错 */
    ignoreCancelToken?: boolean;
    /** 是否返回原生响应头 */
    isReturnNativeResponse?: boolean;
    /** 是否加入token */
    withToken?: boolean;
    /** 是否全局loading */
    loading?: boolean;
}

export type Code = '1000' | '2000' | '3000' | '4000' | '5000' | '6000' | '7000' | '-1000'

/**
 * 接口返回数据格式
 * */
export interface Result<T = any> {
    code: Code;
    success: boolean
    data?: T;
    msg?: string
}
